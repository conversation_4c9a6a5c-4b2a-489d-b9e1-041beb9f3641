# Grafana Alert Rules for IoT Device Monitoring

groups:
  - name: iot-device-alerts
    interval: 30s
    rules:
      - alert: DeviceOffline
        expr: |
          (time() - last_over_time(device_status_battery_level[5m])) > 300
        for: 2m
        labels:
          severity: warning
          service: iot-devices
        annotations:
          summary: "IoT Device {{ $labels.device_id }} is offline"
          description: "Device {{ $labels.device_id }} of type {{ $labels.device_type }} has not reported status for more than 5 minutes."

      - alert: LowBattery
        expr: |
          device_status_battery_level < 20
        for: 1m
        labels:
          severity: warning
          service: iot-devices
        annotations:
          summary: "Low battery on device {{ $labels.device_id }}"
          description: "Device {{ $labels.device_id }} battery level is {{ $value }}%"

      - alert: CriticalBattery
        expr: |
          device_status_battery_level < 5
        for: 30s
        labels:
          severity: critical
          service: iot-devices
        annotations:
          summary: "Critical battery on device {{ $labels.device_id }}"
          description: "Device {{ $labels.device_id }} battery level is critically low at {{ $value }}%"

      - alert: HighTemperature
        expr: |
          sensor_reading_temperature > 35
        for: 2m
        labels:
          severity: warning
          service: iot-sensors
        annotations:
          summary: "High temperature detected"
          description: "Temperature sensor {{ $labels.device_id }} in {{ $labels.location }} reports {{ $value }}°C"

      - alert: TemperatureSpike
        expr: |
          increase(sensor_reading_temperature[5m]) > 10
        for: 1m
        labels:
          severity: critical
          service: iot-sensors
        annotations:
          summary: "Temperature spike detected"
          description: "Rapid temperature increase of {{ $value }}°C detected on sensor {{ $labels.device_id }}"

      - alert: HighErrorRate
        expr: |
          rate(device_status_error_count[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: iot-system
        annotations:
          summary: "High error rate on device {{ $labels.device_id }}"
          description: "Device {{ $labels.device_id }} error rate is {{ $value | humanizePercentage }}"

      - alert: NoMotionDetected
        expr: |
          (time() - last_over_time(sensor_reading_motion_detected[1h])) > 3600
        for: 5m
        labels:
          severity: info
          service: iot-sensors
        annotations:
          summary: "No motion detected for extended period"
          description: "Motion sensor {{ $labels.device_id }} in {{ $labels.location }} has not detected motion for over 1 hour"

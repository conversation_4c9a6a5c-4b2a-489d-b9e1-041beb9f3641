#!/usr/bin/env node

// Simple test to verify the simulator components work
import { TemperatureSensor } from './src/devices/TemperatureSensor.js';
import { MotionSensor } from './src/devices/MotionSensor.js';
import { LightSensor } from './src/devices/LightSensor.js';
import { logger } from './src/utils/logger.js';

async function testDevices() {
  logger.info('🧪 Testing IoT Device Simulator Components');
  
  // Test device creation
  const tempSensor = new TemperatureSensor(null, 'Test Room');
  const motionSensor = new MotionSensor(null, 'Test Hallway');
  const lightSensor = new LightSensor(null, 'Test Office');
  
  logger.info('✅ Device creation successful');
  
  // Test data generation
  const tempReading = tempSensor.generateReading();
  const motionReading = motionSensor.generateReading();
  const lightReading = lightSensor.generateReading();
  
  logger.info('📊 Sample sensor readings:');
  logger.info('Temperature:', tempReading);
  logger.info('Motion:', motionReading);
  logger.info('Light:', lightReading);
  
  // Test device status
  const tempStatus = tempSensor.getStatus();
  logger.info('📱 Device status:', tempStatus);
  
  logger.success('🎉 All tests passed! Your simulator is ready.');
  logger.info('💡 Next steps:');
  logger.info('   1. Start infrastructure: docker compose up -d');
  logger.info('   2. Run setup: npm run setup');
  logger.info('   3. Start simulator: npm run simulator');
  logger.info('   4. Start collector: npm run collector');
}

testDevices().catch(console.error);

#!/usr/bin/env node

import { DataCollector } from './collectors/DataCollector.js';
import { config } from './config/index.js';
import { logger } from './utils/logger.js';

async function main() {
  logger.info('📊 Starting IoT Data Collector');
  logger.info('Configuration:', {
    mqttBroker: config.mqtt.brokerUrl,
    influxDB: config.influxdb.url,
    influxOrg: config.influxdb.org,
    influxBucket: config.influxdb.bucket
  });

  const collector = new DataCollector();
  
  // Setup graceful shutdown
  const shutdown = async (signal) => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    await collector.stop();
    process.exit(0);
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  
  try {
    await collector.start();
    
    logger.info('✅ Data collector is running. Press Ctrl+C to stop.');
    
    // Optional: Add interactive commands
    if (process.stdin.isTTY) {
      setupInteractiveCommands(collector);
    }
    
    // Start stats reporting
    setInterval(() => {
      const stats = collector.getStats();
      logger.info('Collector Statistics', {
        connected: stats.isConnected ? '🟢' : '🔴',
        messages: stats.messageCount,
        errors: stats.errorCount,
        errorRate: `${stats.errorRate.toFixed(2)}%`,
        uptime: `${Math.floor(stats.uptime / 1000)}s`
      });
    }, 60000); // Every minute
    
  } catch (error) {
    logger.error('Failed to start data collector', { error: error.message });
    process.exit(1);
  }
}

function setupInteractiveCommands(collector) {
  logger.info('Interactive commands available:');
  logger.info('  stats - Show collector statistics');
  logger.info('  quit - Stop the collector');
  
  process.stdin.setEncoding('utf8');
  process.stdin.on('data', async (input) => {
    const command = input.trim().toLowerCase();
    
    try {
      switch (command) {
        case 'stats':
          const stats = collector.getStats();
          console.log('\n📈 Collector Statistics:');
          console.log(`  Status: ${stats.isConnected ? '🟢 Connected' : '🔴 Disconnected'}`);
          console.log(`  Messages Processed: ${stats.messageCount}`);
          console.log(`  Errors: ${stats.errorCount}`);
          console.log(`  Error Rate: ${stats.errorRate.toFixed(2)}%`);
          console.log(`  Uptime: ${Math.floor(stats.uptime / 1000)} seconds`);
          break;
          
        case 'quit':
        case 'exit':
          console.log('👋 Stopping collector...');
          await collector.stop();
          process.exit(0);
          break;
          
        case 'help':
          setupInteractiveCommands(collector);
          break;
          
        default:
          if (command) {
            console.log(`Unknown command: ${command}. Type 'help' for available commands.`);
          }
      }
    } catch (error) {
      console.error(`Error executing command: ${error.message}`);
    }
    
    process.stdout.write('\n> ');
  });
  
  process.stdout.write('\n> ');
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

main().catch(console.error);

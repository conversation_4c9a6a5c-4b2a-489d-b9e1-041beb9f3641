#!/usr/bin/env node

import { DeviceManager } from './DeviceManager.js';
import { config } from './config/index.js';
import { logger } from './utils/logger.js';

async function main() {
  logger.info('🚀 Starting IoT Device Simulator');
  logger.info('Configuration:', {
    deviceCount: config.simulation.deviceCount,
    interval: `${config.simulation.interval}ms`,
    mqttBroker: config.mqtt.brokerUrl,
    influxDB: config.influxdb.url,
    failuresEnabled: config.simulation.enableFailures
  });

  const deviceManager = new DeviceManager();
  
  // Setup graceful shutdown
  deviceManager.setupGracefulShutdown();
  
  try {
    await deviceManager.start();
    
    // Keep the process running
    logger.info('✅ Simulator is running. Press Ctrl+C to stop.');
    
    // Optional: Add interactive commands
    if (process.stdin.isTTY) {
      setupInteractiveCommands(deviceManager);
    }
    
  } catch (error) {
    logger.error('Failed to start simulator', { error: error.message });
    process.exit(1);
  }
}

function setupInteractiveCommands(deviceManager) {
  logger.info('Interactive commands available:');
  logger.info('  stats - Show fleet statistics');
  logger.info('  devices - List all devices');
  logger.info('  fail <deviceId> - Simulate device failure');
  logger.info('  restart <deviceId> - Restart a device');
  logger.info('  quit - Stop the simulator');
  
  process.stdin.setEncoding('utf8');
  process.stdin.on('data', async (input) => {
    const command = input.trim().split(' ');
    const cmd = command[0].toLowerCase();
    
    try {
      switch (cmd) {
        case 'stats':
          const stats = deviceManager.getFleetStats();
          console.log('\n📊 Fleet Statistics:');
          console.log(`  Total Devices: ${stats.totalDevices}`);
          console.log(`  Connected: ${stats.connectedDevices}`);
          console.log(`  Online: ${stats.onlineDevices}`);
          console.log(`  Average Battery: ${stats.averageBatteryLevel.toFixed(1)}%`);
          console.log(`  Total Messages: ${stats.totalMessages}`);
          console.log(`  Error Rate: ${stats.errorRate.toFixed(2)}%`);
          console.log(`  Device Types:`, stats.deviceTypeStats);
          break;
          
        case 'devices':
          const devices = deviceManager.getAllDevicesStatus();
          console.log('\n📱 Device List:');
          devices.forEach(device => {
            const status = device.isOnline ? '🟢' : '🔴';
            const battery = device.batteryLevel > 20 ? '🔋' : '🪫';
            console.log(`  ${status} ${device.deviceId.substring(0, 8)} (${device.deviceType}) ${battery} ${device.batteryLevel.toFixed(1)}%`);
          });
          break;
          
        case 'fail':
          if (command[1]) {
            await deviceManager.simulateDeviceFailure(command[1]);
            console.log(`💥 Simulated failure for device ${command[1]}`);
          } else {
            console.log('Usage: fail <deviceId>');
          }
          break;
          
        case 'restart':
          if (command[1]) {
            await deviceManager.restartDevice(command[1]);
            console.log(`🔄 Restarted device ${command[1]}`);
          } else {
            console.log('Usage: restart <deviceId>');
          }
          break;
          
        case 'quit':
        case 'exit':
          console.log('👋 Stopping simulator...');
          await deviceManager.stop();
          process.exit(0);
          break;
          
        case 'help':
          setupInteractiveCommands(deviceManager);
          break;
          
        default:
          if (cmd) {
            console.log(`Unknown command: ${cmd}. Type 'help' for available commands.`);
          }
      }
    } catch (error) {
      console.error(`Error executing command: ${error.message}`);
    }
    
    process.stdout.write('\n> ');
  });
  
  process.stdout.write('\n> ');
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

main().catch(console.error);

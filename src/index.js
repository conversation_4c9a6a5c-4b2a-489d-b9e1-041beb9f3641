#!/usr/bin/env node

import { DeviceManager } from './DeviceManager.js';
import { DataCollector } from './collectors/DataCollector.js';
import { config } from './config/index.js';
import { logger } from './utils/logger.js';

async function main() {
  logger.info('🚀 Starting Complete IoT Simulation System');
  
  const collector = new DataCollector();
  const deviceManager = new DeviceManager();
  
  // Setup graceful shutdown
  const shutdown = async (signal) => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    await deviceManager.stop();
    await collector.stop();
    process.exit(0);
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  
  try {
    // Start data collector first
    logger.info('Starting data collector...');
    await collector.start();
    
    // Wait a moment for collector to be ready
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Start device fleet
    logger.info('Starting device fleet...');
    await deviceManager.start();
    
    logger.success('✅ Complete IoT simulation system is running!');
    logger.info('📊 Access Grafana at: http://localhost:3000 (admin/admin123)');
    logger.info('🔧 Access InfluxDB at: http://localhost:8086 (admin/password123)');
    logger.info('Press Ctrl+C to stop the system.');
    
    // Combined stats reporting
    setInterval(() => {
      const collectorStats = collector.getStats();
      const fleetStats = deviceManager.getFleetStats();
      
      logger.info('System Status', {
        collector: collectorStats.isConnected ? '🟢' : '🔴',
        devices: `${fleetStats.onlineDevices}/${fleetStats.totalDevices}`,
        messages: collectorStats.messageCount,
        avgBattery: `${fleetStats.averageBatteryLevel.toFixed(1)}%`,
        errorRate: `${collectorStats.errorRate.toFixed(2)}%`
      });
    }, 60000); // Every minute
    
  } catch (error) {
    logger.error('Failed to start IoT simulation system', { error: error.message });
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

main().catch(console.error);

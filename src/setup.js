import { InfluxDB } from '@influxdata/influxdb-client';
import { config } from './config/index.js';
import { logger } from './utils/logger.js';

async function setupInfluxDB() {
  try {
    logger.info('Setting up InfluxDB...');

    const client = new InfluxDB({
      url: config.influxdb.url,
      token: config.influxdb.token
    });

    // Test connection by trying to get query API
    const queryApi = client.getQueryApi(config.influxdb.org);

    // Simple test query
    const testQuery = `
      from(bucket: "${config.influxdb.bucket}")
      |> range(start: -1m)
      |> limit(n: 1)
    `;

    try {
      await queryApi.collectRows(testQuery);
      logger.success('InfluxDB connection successful');
    } catch (queryError) {
      // If bucket doesn't exist or no data, that's still a successful connection
      if (queryError.message.includes('bucket') || queryError.message.includes('no data')) {
        logger.success('InfluxDB connection successful (bucket ready for data)');
      } else {
        throw queryError;
      }
    }

    // The bucket should already be created by Docker initialization
    logger.success('InfluxDB setup completed');

    client.close();
  } catch (error) {
    logger.error('Failed to setup InfluxDB', { error: error.message });
    logger.info('Make sure InfluxDB is running: docker compose up -d');
    process.exit(1);
  }
}

async function main() {
  logger.info('Starting IoT Device Simulator setup...');
  await setupInfluxDB();
  logger.success('Setup completed successfully!');
}

main().catch(console.error);

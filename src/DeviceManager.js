import { TemperatureSensor } from './devices/TemperatureSensor.js';
import { MotionSensor } from './devices/MotionSensor.js';
import { LightSensor } from './devices/LightSensor.js';
import { config } from './config/index.js';
import { logger } from './utils/logger.js';

export class DeviceManager {
  constructor() {
    this.devices = new Map();
    this.deviceTypes = [
      { class: TemperatureSensor, weight: 0.4 }, // 40% temperature sensors
      { class: MotionSensor, weight: 0.3 },     // 30% motion sensors
      { class: LightSensor, weight: 0.3 }       // 30% light sensors
    ];
    this.locations = [
      'Living Room', 'Kitchen', 'Bedroom', 'Bathroom', 'Office',
      'Garage', 'Garden', 'Basement', 'Attic', 'Hallway',
      'Dining Room', 'Balcony', 'Storage Room', 'Workshop'
    ];
    this.isRunning = false;
    this.statsInterval = null;
  }

  async start() {
    try {
      logger.info(`Starting device fleet with ${config.simulation.deviceCount} devices`);
      
      await this.createDevices();
      await this.startAllDevices();
      this.startStatsReporting();
      
      this.isRunning = true;
      logger.success(`Device fleet started with ${this.devices.size} devices`);
      
    } catch (error) {
      logger.error('Failed to start device fleet', { error: error.message });
      throw error;
    }
  }

  async createDevices() {
    const devicePromises = [];
    
    for (let i = 0; i < config.simulation.deviceCount; i++) {
      const deviceType = this.selectDeviceType();
      const location = this.selectLocation();
      
      const device = new deviceType.class(null, location);
      this.devices.set(device.deviceId, device);
      
      // Set up event listeners
      this.setupDeviceEventListeners(device);
      
      logger.debug(`Created ${device.deviceType}`, { 
        deviceId: device.deviceId,
        location: location 
      });
    }
  }

  selectDeviceType() {
    const random = Math.random();
    let cumulativeWeight = 0;
    
    for (const deviceType of this.deviceTypes) {
      cumulativeWeight += deviceType.weight;
      if (random <= cumulativeWeight) {
        return deviceType;
      }
    }
    
    return this.deviceTypes[0]; // Fallback
  }

  selectLocation() {
    return this.locations[Math.floor(Math.random() * this.locations.length)];
  }

  setupDeviceEventListeners(device) {
    device.on('connected', () => {
      logger.debug(`Device connected`, { 
        deviceId: device.deviceId,
        type: device.deviceType 
      });
    });

    device.on('disconnected', () => {
      logger.warn(`Device disconnected`, { 
        deviceId: device.deviceId,
        type: device.deviceType 
      });
    });

    device.on('failure', (reason) => {
      logger.warn(`Device failed`, { 
        deviceId: device.deviceId,
        type: device.deviceType,
        reason 
      });
    });

    device.on('recovery', () => {
      logger.success(`Device recovered`, { 
        deviceId: device.deviceId,
        type: device.deviceType 
      });
    });

    device.on('error', (error) => {
      logger.error(`Device error`, { 
        deviceId: device.deviceId,
        type: device.deviceType,
        error: error.message 
      });
    });
  }

  async startAllDevices() {
    const startPromises = Array.from(this.devices.values()).map(async (device, index) => {
      // Stagger device starts to avoid overwhelming the broker
      await new Promise(resolve => setTimeout(resolve, index * 100));
      
      try {
        await device.start();
      } catch (error) {
        logger.error(`Failed to start device ${device.deviceId}`, { 
          error: error.message 
        });
      }
    });

    await Promise.allSettled(startPromises);
  }

  startStatsReporting() {
    this.statsInterval = setInterval(() => {
      this.logFleetStats();
    }, 60000); // Every minute
  }

  logFleetStats() {
    const stats = this.getFleetStats();
    
    logger.info('Fleet Statistics', {
      totalDevices: stats.totalDevices,
      connectedDevices: stats.connectedDevices,
      onlineDevices: stats.onlineDevices,
      averageBattery: `${stats.averageBatteryLevel.toFixed(1)}%`,
      totalMessages: stats.totalMessages,
      totalErrors: stats.totalErrors,
      errorRate: `${stats.errorRate.toFixed(2)}%`
    });
  }

  getFleetStats() {
    const devices = Array.from(this.devices.values());
    
    const connectedDevices = devices.filter(d => d.isConnected).length;
    const onlineDevices = devices.filter(d => d.isOnline).length;
    const totalMessages = devices.reduce((sum, d) => sum + d.messageCount, 0);
    const totalErrors = devices.reduce((sum, d) => sum + d.errorCount, 0);
    const averageBatteryLevel = devices.reduce((sum, d) => sum + d.batteryLevel, 0) / devices.length;
    
    const deviceTypeStats = {};
    devices.forEach(device => {
      if (!deviceTypeStats[device.deviceType]) {
        deviceTypeStats[device.deviceType] = 0;
      }
      deviceTypeStats[device.deviceType]++;
    });

    return {
      totalDevices: devices.length,
      connectedDevices,
      onlineDevices,
      averageBatteryLevel,
      totalMessages,
      totalErrors,
      errorRate: totalMessages > 0 ? (totalErrors / totalMessages) * 100 : 0,
      deviceTypeStats
    };
  }

  getDeviceStatus(deviceId) {
    const device = this.devices.get(deviceId);
    return device ? device.getStatus() : null;
  }

  getAllDevicesStatus() {
    return Array.from(this.devices.values()).map(device => device.getStatus());
  }

  async restartDevice(deviceId) {
    const device = this.devices.get(deviceId);
    if (!device) {
      throw new Error(`Device ${deviceId} not found`);
    }

    logger.info(`Restarting device ${deviceId}`);
    await device.stop();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    await device.start();
  }

  async simulateDeviceFailure(deviceId, reason = 'manual_failure') {
    const device = this.devices.get(deviceId);
    if (!device) {
      throw new Error(`Device ${deviceId} not found`);
    }

    logger.info(`Simulating failure for device ${deviceId}`, { reason });
    device.simulateFailure(reason);
  }

  async stop() {
    try {
      this.isRunning = false;
      
      if (this.statsInterval) {
        clearInterval(this.statsInterval);
      }

      logger.info('Stopping all devices...');
      
      const stopPromises = Array.from(this.devices.values()).map(device => 
        device.stop().catch(error => 
          logger.error(`Error stopping device ${device.deviceId}`, { 
            error: error.message 
          })
        )
      );

      await Promise.allSettled(stopPromises);
      
      logger.success('Device fleet stopped');
      
    } catch (error) {
      logger.error('Error stopping device fleet', { error: error.message });
    }
  }

  // Graceful shutdown handler
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      await this.stop();
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
  }
}

# Grafana Query Examples

This file contains useful InfluxDB Flux queries for creating comprehensive IoT monitoring dashboards.

## 📊 Basic Sensor Queries

### Temperature Readings Over Time
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "temperature")
  |> filter(fn: (r) => r["_field"] == "temperature")
  |> aggregateWindow(every: v.windowPeriod, fn: mean, createEmpty: false)
  |> yield(name: "temperature")
```

### Motion Detection Events
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "motion")
  |> filter(fn: (r) => r["_field"] == "motion_detected")
  |> filter(fn: (r) => r["_value"] == true)
  |> aggregateWindow(every: v.windowPeriod, fn: count, createEmpty: false)
  |> yield(name: "motion_events")
```

### Light Levels by Location
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "light")
  |> filter(fn: (r) => r["_field"] == "illuminance")
  |> group(columns: ["location"])
  |> aggregateWindow(every: v.windowPeriod, fn: mean, createEmpty: false)
  |> yield(name: "light_by_location")
```

## 🔋 Device Health Queries

### Current Battery Levels
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["_field"] == "battery_level")
  |> group(columns: ["device_id", "device_type"])
  |> last()
  |> yield(name: "current_battery")
```

### Device Uptime
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "device_status")
  |> filter(fn: (r) => r["_field"] == "uptime")
  |> group(columns: ["device_id"])
  |> last()
  |> map(fn: (r) => ({ r with _value: r._value / 3600000 })) // Convert to hours
  |> yield(name: "uptime_hours")
```

### Message Success Rate
```flux
messages = from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "device_status")
  |> filter(fn: (r) => r["_field"] == "message_count")
  |> group(columns: ["device_id"])
  |> last()

errors = from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "device_status")
  |> filter(fn: (r) => r["_field"] == "error_count")
  |> group(columns: ["device_id"])
  |> last()

join(tables: {messages: messages, errors: errors}, on: ["device_id"])
  |> map(fn: (r) => ({ 
      r with 
      _value: (r._value_messages - r._value_errors) / r._value_messages * 100.0 
  }))
  |> yield(name: "success_rate")
```

## 📈 Advanced Analytics

### Temperature Anomaly Detection
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "temperature")
  |> filter(fn: (r) => r["_field"] == "temperature")
  |> group(columns: ["device_id"])
  |> aggregateWindow(every: 5m, fn: mean, createEmpty: false)
  |> movingAverage(n: 12) // 1-hour moving average
  |> yield(name: "temp_moving_avg")
```

### Daily Motion Pattern
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "motion")
  |> filter(fn: (r) => r["_field"] == "motion_detected")
  |> filter(fn: (r) => r["_value"] == true)
  |> aggregateWindow(every: 1h, fn: count, createEmpty: false)
  |> group(columns: ["location"])
  |> yield(name: "hourly_motion")
```

### Energy Consumption Estimation
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["_field"] == "battery_level")
  |> group(columns: ["device_id"])
  |> derivative(unit: 1h, nonNegative: false)
  |> map(fn: (r) => ({ r with _value: r._value * -1 })) // Invert for consumption
  |> yield(name: "battery_drain_rate")
```

## 🚨 Alert Queries

### Devices Offline Detection
```flux
from(bucket: "iot-sensors")
  |> range(start: -10m)
  |> filter(fn: (r) => r["_measurement"] == "device_status")
  |> group(columns: ["device_id"])
  |> last()
  |> map(fn: (r) => ({ 
      r with 
      _value: if (uint(v: now()) - uint(v: r._time)) > uint(v: 5m) then 1 else 0 
  }))
  |> filter(fn: (r) => r["_value"] == 1)
  |> yield(name: "offline_devices")
```

### Low Battery Alert
```flux
from(bucket: "iot-sensors")
  |> range(start: -5m)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["_field"] == "battery_level")
  |> group(columns: ["device_id"])
  |> last()
  |> filter(fn: (r) => r["_value"] < 20.0)
  |> yield(name: "low_battery")
```

### Temperature Spike Detection
```flux
from(bucket: "iot-sensors")
  |> range(start: -15m)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "temperature")
  |> filter(fn: (r) => r["_field"] == "temperature")
  |> group(columns: ["device_id"])
  |> aggregateWindow(every: 1m, fn: mean, createEmpty: false)
  |> derivative(unit: 1m, nonNegative: false)
  |> filter(fn: (r) => r["_value"] > 5.0) // More than 5°C per minute
  |> yield(name: "temp_spikes")
```

## 📊 Dashboard Variables

### Device Type Variable
```flux
from(bucket: "iot-sensors")
  |> range(start: -24h)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> keep(columns: ["device_type"])
  |> group()
  |> distinct(column: "device_type")
  |> yield(name: "device_types")
```

### Location Variable
```flux
from(bucket: "iot-sensors")
  |> range(start: -24h)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> keep(columns: ["location"])
  |> group()
  |> distinct(column: "location")
  |> yield(name: "locations")
```

### Device ID Variable (filtered by type)
```flux
from(bucket: "iot-sensors")
  |> range(start: -24h)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["device_type"] == "${device_type}")
  |> keep(columns: ["device_id"])
  |> group()
  |> distinct(column: "device_id")
  |> yield(name: "device_ids")
```

## 🎯 Performance Optimization Tips

### Use Appropriate Time Windows
```flux
// Good: Aggregate data for better performance
|> aggregateWindow(every: v.windowPeriod, fn: mean, createEmpty: false)

// Avoid: Querying raw data over long periods
|> range(start: -30d) // Without aggregation
```

### Filter Early
```flux
// Good: Filter first, then process
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["device_type"] == "temperature_sensor")
  |> filter(fn: (r) => r["_field"] == "temperature")
  |> aggregateWindow(every: v.windowPeriod, fn: mean)

// Avoid: Process then filter
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> aggregateWindow(every: v.windowPeriod, fn: mean)
  |> filter(fn: (r) => r["device_type"] == "temperature_sensor")
```

### Limit Data Points
```flux
// Limit results for large datasets
|> limit(n: 1000)

// Or use sampling
|> sample(n: 100)
```

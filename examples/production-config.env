# Production Environment Configuration Example
# Copy this to .env and modify for your production deployment

# MQTT Configuration
MQTT_BROKER_URL=mqtt://your-mqtt-broker.com:1883
MQTT_USERNAME=iot_user
MQTT_PASSWORD=secure_password_here

# For TLS/SSL MQTT (recommended for production)
# MQTT_BROKER_URL=mqtts://your-mqtt-broker.com:8883

# InfluxDB Configuration
INFLUXDB_URL=https://your-influxdb.com:8086
INFLUXDB_TOKEN=your-production-token-here
INFLUXDB_ORG=your-organization
INFLUXDB_BUCKET=iot-sensors-prod

# Simulation Configuration (adjust for production load)
DEVICE_COUNT=50
SIMULATION_INTERVAL=30000
ENABLE_DEVICE_FAILURES=true
FAILURE_PROBABILITY=0.01

# Logging
LOG_LEVEL=info

# Security Settings (for production deployment)
# Enable these for production security
# MQTT_CA_CERT_PATH=/path/to/ca.crt
# MQTT_CLIENT_CERT_PATH=/path/to/client.crt
# MQTT_CLIENT_KEY_PATH=/path/to/client.key

# Performance Tuning
# BATCH_SIZE=100
# FLUSH_INTERVAL=5000
# MAX_RETRIES=3
# RETRY_DELAY=1000

# Monitoring
# HEALTH_CHECK_PORT=8080
# METRICS_PORT=9090
# ENABLE_PROMETHEUS=true

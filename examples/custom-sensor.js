// Example: Creating a Custom Humidity Sensor
// This demonstrates how to extend the IoT simulator with new sensor types

import { BaseDevice } from '../src/devices/BaseDevice.js';
import { config } from '../src/config/index.js';
import { logger } from '../src/utils/logger.js';

export class HumiditySensor extends BaseDevice {
  constructor(deviceId = null, location = 'Unknown') {
    super('humidity_sensor', deviceId);
    
    this.location = location;
    this.baseHumidity = Math.random() * 30 + 40; // 40-70% base humidity
    this.humidityVariation = Math.random() * 10 + 5; // ±5-15% variation
    this.isOutdoor = Math.random() > 0.7; // 30% chance of outdoor sensor
    
    this.readingInterval = null;
    this.startTime = Date.now();
  }

  async start() {
    await this.connect();
    this.startReadings();
  }

  startReadings() {
    this.readingInterval = setInterval(() => {
      if (this.isOnline && this.isConnected) {
        this.publishReading();
      }
    }, config.simulation.interval);
  }

  publishReading() {
    const reading = this.generateReading();
    
    const success = this.publish(
      `${config.mqtt.topics.sensors}/humidity/${this.deviceId}`,
      reading
    );

    if (success) {
      logger.debug(`Humidity sensor ${this.deviceId} reading`, { 
        location: this.location,
        humidity: reading.humidity,
        dewPoint: reading.dewPoint
      });
    }
  }

  generateReading() {
    // Simulate daily humidity cycle (higher at night)
    const timeOfDay = (Date.now() % (24 * 60 * 60 * 1000)) / (24 * 60 * 60 * 1000);
    const dailyCycle = Math.sin(timeOfDay * 2 * Math.PI + Math.PI) * 10; // ±10% daily variation
    
    // Weather patterns (outdoor sensors more variable)
    const weatherVariation = this.isOutdoor ? 
      (Math.random() - 0.5) * 20 : // ±10% for outdoor
      (Math.random() - 0.5) * 10;  // ±5% for indoor
    
    // Seasonal variation
    const seasonalVariation = Math.sin(Date.now() / (365 * 24 * 60 * 60 * 1000) * 2 * Math.PI) * 15;
    
    // Random noise
    const noise = (Math.random() - 0.5) * this.humidityVariation;
    
    let humidity = this.baseHumidity + dailyCycle + seasonalVariation + weatherVariation + noise;
    humidity = Math.max(0, Math.min(100, humidity)); // Clamp to 0-100%
    
    // Calculate dew point (simplified formula)
    const dewPoint = this.calculateDewPoint(humidity, 20); // Assume 20°C temperature
    
    // Simulate sensor degradation
    const degradation = (Date.now() - this.startTime) / (365 * 24 * 60 * 60 * 1000) * 2; // 2% per year
    
    return {
      humidity: Math.round((humidity + degradation) * 10) / 10,
      dewPoint: Math.round(dewPoint * 10) / 10,
      location: this.location,
      sensorType: this.isOutdoor ? 'outdoor' : 'indoor',
      unit: 'percent',
      quality: this.batteryLevel > 20 ? 'good' : 'degraded'
    };
  }

  // Simplified dew point calculation
  calculateDewPoint(humidity, temperature) {
    const a = 17.27;
    const b = 237.7;
    const alpha = ((a * temperature) / (b + temperature)) + Math.log(humidity / 100);
    return (b * alpha) / (a - alpha);
  }

  async stop() {
    if (this.readingInterval) {
      clearInterval(this.readingInterval);
    }
    await this.disconnect();
  }
}

// Example usage:
// const humiditySensor = new HumiditySensor(null, 'Greenhouse');
// await humiditySensor.start();

// To integrate into DeviceManager, add to deviceTypes array:
// { class: HumiditySensor, weight: 0.2 } // 20% humidity sensors

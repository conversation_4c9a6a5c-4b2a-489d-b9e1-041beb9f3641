{"version": 3, "sources": ["container/HashContainer/HashMap.js", "../../src/container/HashContainer/HashMap.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_Base", "require", "_checkObject", "_interopRequireDefault", "_throwError", "obj", "__esModule", "HashMapIterator", "HashContainerIterator", "constructor", "node", "header", "container", "iteratorType", "super", "this", "pointer", "_node", "_header", "throwIteratorAccessError", "self", "Proxy", "get", "_", "props", "_key", "_value", "set", "newValue", "TypeError", "copy", "HashMap", "HashC<PERSON>r", "for<PERSON>ach", "el", "setElement", "begin", "_head", "end", "rBegin", "_tail", "rEnd", "front", "_length", "back", "key", "isObject", "_set", "getElement<PERSON>y<PERSON>ey", "undefined", "checkObject", "index", "HASH_TAG", "_objMap", "_originMap", "getElementByPos", "pos", "RangeError", "_next", "find", "_findElementNode", "callback", "Symbol", "iterator", "bind", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACJvB,IAAAC,QAAAC,QAAA;;AACA,IAAAC,eAAAC,uBAAAF,QAAA;;AAEA,IAAAG,cAAAH,QAAA;;AAA8D,SAAAE,uBAAAE;IAAA,OAAAA,KAAAA,EAAAC,IAAAD,IAAA;QAAAN,SAAAM;;AAAA;;AAE9D,MAAME,wBAA8BC,MAAAA;IAElCC,YACEC,GACAC,GACAC,GACAC;QAEAC,MAAMJ,GAAMC,GAAQE;QACpBE,KAAKH,YAAYA;ADPjB;ICSEI;QACF,IAAID,KAAKE,MAAUF,KAAKG,GAAS;aAC/B,GAAAC,YAAAA;ADPE;QCSJ,MAAMC,IAAOL;QACb,OAAO,IAAIM,MAAuB,IAAI;YACpCC,IAAIC,GAAGC;gBACL,IAAIA,MAAU,KAAK,OAAOJ,EAAKH,EAAMQ,QAChC,IAAID,MAAU,KAAK,OAAOJ,EAAKH,EAAMS;ADLtC;YCONC,IAAIJ,GAAGC,GAAYI;gBACjB,IAAIJ,MAAU,KAAK;oBACjB,MAAM,IAAIK,UAAU;ADLd;gBCORT,EAAKH,EAAMS,IAASE;gBACpB,OAAO;ADLH;;AAER;ICOFE;QACE,OAAO,IAAIvB,gBAAsBQ,KAAKE,GAAOF,KAAKG,GAASH,KAAKH,WAAWG,KAAKF;ADLhF;;;ACaJ,MAAMkB,gBAAsBC,MAAAA;IAC1BvB,YAAYG,IAAmC;QAC7CE;QACA,MAAMM,IAAOL;QACbH,EAAUqB,SAAQ,SAAUC;YAC1Bd,EAAKe,WAAWD,EAAG,IAAIA,EAAG;ADVxB;AACJ;ICYFE;QACE,OAAO,IAAI7B,gBAAsBQ,KAAKsB,GAAOtB,KAAKG,GAASH;ADV3D;ICYFuB;QACE,OAAO,IAAI/B,gBAAsBQ,KAAKG,GAASH,KAAKG,GAASH;ADV7D;ICYFwB;QACE,OAAO,IAAIhC,gBAAsBQ,KAAKyB,GAAOzB,KAAKG,GAASH,MAAI;ADV/D;ICYF0B;QACE,OAAO,IAAIlC,gBAAsBQ,KAAKG,GAASH,KAAKG,GAASH,MAAI;ADVjE;ICYF2B;QACE,IAAI3B,KAAK4B,MAAY,GAAG;QACxB,OAAe,EAAC5B,KAAKsB,EAAMZ,GAAMV,KAAKsB,EAAMX;ADT5C;ICWFkB;QACE,IAAI7B,KAAK4B,MAAY,GAAG;QACxB,OAAe,EAAC5B,KAAKyB,EAAMf,GAAMV,KAAKyB,EAAMd;ADR5C;ICkBFS,WAAWU,GAAQ/C,GAAUgD;QAC3B,OAAO/B,KAAKgC,EAAKF,GAAK/C,GAAOgD;ADR7B;ICkBFE,gBAAgBH,GAAQC;QACtB,IAAIA,MAAaG,WAAWH,KAAW,GAAAI,aAAAA,SAAYL;QACnD,IAAIC,GAAU;YACZ,MAAMK,IAA0CN,EAAK9B,KAAKqC;YAC1D,OAAOD,MAAUF,YAAYlC,KAAKsC,EAAQF,GAAOzB,IAASuB;ADPxD;QCSJ,MAAMvC,IAAOK,KAAKuC,EAA4BT;QAC9C,OAAOnC,IAAOA,EAAKgB,IAASuB;ADP5B;ICSFM,gBAAgBC;QDPV,ICQsBA,IAAG,KAAHA,IAAQzC,KAAK4B,IAAO,GAhG3C;YAAE,MAAU,IAAIc;AD0Ff;QCOJ,IAAI/C,IAAOK,KAAKsB;QAChB,OAAOmB,KAAO;YACZ9C,IAAOA,EAAKgD;ADLV;QCOJ,OAAe,EAAChD,EAAKe,GAAMf,EAAKgB;ADLhC;ICcFiC,KAAKd,GAAQC;QACX,MAAMpC,IAAOK,KAAK6C,EAAiBf,GAAKC;QACxC,OAAO,IAAIvC,gBAAsBG,GAAMK,KAAKG,GAASH;ADLrD;ICOFkB,QAAQ4B;QACN,IAAIV,IAAQ;QACZ,IAAIzC,IAAOK,KAAKsB;QAChB,OAAO3B,MAASK,KAAKG,GAAS;YAC5B2C,EAAiB,EAACnD,EAAKe,GAAMf,EAAKgB,KAASyB,KAASpC;YACpDL,IAAOA,EAAKgD;ADLV;AACJ;ICOF,CAACI,OAAOC;QACN,OAAO;YACL,IAAIrD,IAAOK,KAAKsB;YAChB,OAAO3B,MAASK,KAAKG,GAAS;sBACd,EAACR,EAAKe,GAAMf,EAAKgB;gBAC/BhB,IAAOA,EAAKgD;ADLR;AACJ,UCMFM,KAAKjD,KANA;ADCP;;;ACOH,IAAAkD,WAEclC;;AAAOlC,QAAAE,UAAAkE", "file": "HashMap.js", "sourcesContent": ["import { HashContainer, HashContainerIterator } from \"./Base\";\nimport checkObject from \"../../utils/checkObject\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nclass HashMapIterator extends HashContainerIterator {\n    constructor(node, header, container, iteratorType) {\n        super(node, header, iteratorType);\n        this.container = container;\n    }\n    get pointer() {\n        if (this._node === this._header) {\n            throwIteratorAccessError();\n        }\n        const self = this;\n        return new Proxy([], {\n            get(_, props) {\n                if (props === '0')\n                    return self._node._key;\n                else if (props === '1')\n                    return self._node._value;\n            },\n            set(_, props, newValue) {\n                if (props !== '1') {\n                    throw new TypeError('props must be 1');\n                }\n                self._node._value = newValue;\n                return true;\n            }\n        });\n    }\n    copy() {\n        return new HashMapIterator(this._node, this._header, this.container, this.iteratorType);\n    }\n}\nclass HashMap extends HashContainer {\n    constructor(container = []) {\n        super();\n        const self = this;\n        container.forEach(function (el) {\n            self.setElement(el[0], el[1]);\n        });\n    }\n    begin() {\n        return new HashMapIterator(this._head, this._header, this);\n    }\n    end() {\n        return new HashMapIterator(this._header, this._header, this);\n    }\n    rBegin() {\n        return new HashMapIterator(this._tail, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    rEnd() {\n        return new HashMapIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    front() {\n        if (this._length === 0)\n            return;\n        return [this._head._key, this._head._value];\n    }\n    back() {\n        if (this._length === 0)\n            return;\n        return [this._tail._key, this._tail._value];\n    }\n    /**\n     * @description Insert a key-value pair or set value by the given key.\n     * @param key - The key want to insert.\n     * @param value - The value want to set.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns The size of container after setting.\n     */\n    setElement(key, value, isObject) {\n        return this._set(key, value, isObject);\n    }\n    /**\n     * @description Get the value of the element of the specified key.\n     * @param key - The key want to search.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @example\n     * const val = container.getElementByKey(1);\n     */\n    getElementByKey(key, isObject) {\n        if (isObject === undefined)\n            isObject = checkObject(key);\n        if (isObject) {\n            const index = key[this.HASH_TAG];\n            return index !== undefined ? this._objMap[index]._value : undefined;\n        }\n        const node = this._originMap[key];\n        return node ? node._value : undefined;\n    }\n    getElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        let node = this._head;\n        while (pos--) {\n            node = node._next;\n        }\n        return [node._key, node._value];\n    }\n    /**\n     * @description Check key if exist in container.\n     * @param key - The element you want to search.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns An iterator pointing to the element if found, or super end if not found.\n     */\n    find(key, isObject) {\n        const node = this._findElementNode(key, isObject);\n        return new HashMapIterator(node, this._header, this);\n    }\n    forEach(callback) {\n        let index = 0;\n        let node = this._head;\n        while (node !== this._header) {\n            callback([node._key, node._value], index++, this);\n            node = node._next;\n        }\n    }\n    [Symbol.iterator]() {\n        return function* () {\n            let node = this._head;\n            while (node !== this._header) {\n                yield [node._key, node._value];\n                node = node._next;\n            }\n        }.bind(this)();\n    }\n}\nexport default HashMap;\n", "import { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { Hash<PERSON>ontainer, HashContainerIterator, HashLinkNode } from '@/container/HashContainer/Base';\nimport checkObject from '@/utils/checkObject';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass HashMapIterator<K, V> extends HashContainerIterator<K, V> {\n  readonly container: HashMap<K, V>;\n  constructor(\n    node: HashLinkNode<K, V>,\n    header: HashLinkNode<K, V>,\n    container: HashMap<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    const self = this;\n    return new Proxy(<[K, V]><unknown>[], {\n      get(_, props: '0' | '1') {\n        if (props === '0') return self._node._key;\n        else if (props === '1') return self._node._value;\n      },\n      set(_, props: '1', newValue: V) {\n        if (props !== '1') {\n          throw new TypeError('props must be 1');\n        }\n        self._node._value = newValue;\n        return true;\n      }\n    });\n  }\n  copy() {\n    return new HashMapIterator<K, V>(this._node, this._header, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: HashMapIterator<K, V>): boolean;\n}\n\nexport type { HashMapIterator };\n\nclass HashMap<K, V> extends HashContainer<K, V> {\n  constructor(container: initContainer<[K, V]> = []) {\n    super();\n    const self = this;\n    container.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n  }\n  begin() {\n    return new HashMapIterator<K, V>(this._head, this._header, this);\n  }\n  end() {\n    return new HashMapIterator<K, V>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new HashMapIterator<K, V>(this._tail, this._header, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new HashMapIterator<K, V>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front() {\n    if (this._length === 0) return;\n    return <[K, V]>[this._head._key, this._head._value];\n  }\n  back() {\n    if (this._length === 0) return;\n    return <[K, V]>[this._tail._key, this._tail._value];\n  }\n  /**\n   * @description Insert a key-value pair or set value by the given key.\n   * @param key - The key want to insert.\n   * @param value - The value want to set.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns The size of container after setting.\n   */\n  setElement(key: K, value: V, isObject?: boolean) {\n    return this._set(key, value, isObject);\n  }\n  /**\n   * @description Get the value of the element of the specified key.\n   * @param key - The key want to search.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @example\n   * const val = container.getElementByKey(1);\n   */\n  getElementByKey(key: K, isObject?: boolean) {\n    if (isObject === undefined) isObject = checkObject(key);\n    if (isObject) {\n      const index = (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      return index !== undefined ? this._objMap[index]._value : undefined;\n    }\n    const node = this._originMap[<string><unknown>key];\n    return node ? node._value : undefined;\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let node = this._head;\n    while (pos--) {\n      node = node._next;\n    }\n    return <[K, V]>[node._key, node._value];\n  }\n  /**\n   * @description Check key if exist in container.\n   * @param key - The element you want to search.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns An iterator pointing to the element if found, or super end if not found.\n   */\n  find(key: K, isObject?: boolean) {\n    const node = this._findElementNode(key, isObject);\n    return new HashMapIterator<K, V>(node, this._header, this);\n  }\n  forEach(callback: (element: [K, V], index: number, hashMap: HashMap<K, V>) => void) {\n    let index = 0;\n    let node = this._head;\n    while (node !== this._header) {\n      callback(<[K, V]>[node._key, node._value], index++, this);\n      node = node._next;\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: HashMap<K, V>) {\n      let node = this._head;\n      while (node !== this._header) {\n        yield <[K, V]>[node._key, node._value];\n        node = node._next;\n      }\n    }.bind(this)();\n  }\n}\n\nexport default HashMap;\n"]}
{"version": 3, "sources": ["container/OtherContainer/PriorityQueue.js", "../../src/container/OtherContainer/PriorityQueue.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_ContainerBase", "require", "PriorityQueue", "Base", "constructor", "container", "cmp", "x", "y", "copy", "super", "this", "_cmp", "Array", "isArray", "_priorityQueue", "self", "for<PERSON>ach", "el", "push", "_length", "length", "<PERSON><PERSON><PERSON><PERSON>", "parent", "_pushDown", "_pushUp", "pos", "item", "parentItem", "left", "right", "minItem", "clear", "pop", "last", "top", "find", "indexOf", "remove", "index", "splice", "updateItem", "toArray", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,iBAAAC,QAAA;;AAEA,MAAMC,sBAAyBC,eAAAA;IAqB7BC,YACEC,IAA8B,IAC9BC,IACA,SAAUC,GAAMC;QACd,IAAID,IAAIC,GAAG,QAAQ;QACnB,IAAID,IAAIC,GAAG,OAAO;QAClB,OAAO;ADTT,OCWAC,IAAO;QAEPC;QACAC,KAAKC,IAAON;QACZ,IAAIO,MAAMC,QAAQT,IAAY;YAC5BM,KAAKI,IAAiBN,IAAO,KAAIJ,MAAaA;ADX5C,eCYG;YACLM,KAAKI,IAAiB;YACtB,MAAMC,IAAOL;YACbN,EAAUY,SAAQ,SAAUC;gBAC1BF,EAAKD,EAAeI,KAAKD;ADVrB;AACJ;QCYJP,KAAKS,IAAUT,KAAKI,EAAeM;QACnC,MAAMC,IAAaX,KAAKS,KAAW;QACnC,KAAK,IAAIG,IAAUZ,KAAKS,IAAU,KAAM,GAAGG,KAAU,KAAKA,GAAQ;YAChEZ,KAAKa,EAAUD,GAAQD;ADVrB;AACJ;ICeMG,EAAQC;QACd,MAAMC,IAAOhB,KAAKI,EAAeW;QACjC,OAAOA,IAAM,GAAG;YACd,MAAMH,IAAUG,IAAM,KAAM;YAC5B,MAAME,IAAajB,KAAKI,EAAeQ;YACvC,IAAIZ,KAAKC,EAAKgB,GAAYD,MAAS,GAAG;YACtChB,KAAKI,EAAeW,KAAOE;YAC3BF,IAAMH;ADTJ;QCWJZ,KAAKI,EAAeW,KAAOC;ADT3B;ICcMH,EAAUE,GAAaJ;QAC7B,MAAMK,IAAOhB,KAAKI,EAAeW;QACjC,OAAOA,IAAMJ,GAAY;YACvB,IAAIO,IAAOH,KAAO,IAAI;YACtB,MAAMI,IAAQD,IAAO;YACrB,IAAIE,IAAUpB,KAAKI,EAAec;YAClC,IACEC,IAAQnB,KAAKS,KACbT,KAAKC,EAAKmB,GAASpB,KAAKI,EAAee,MAAU,GACjD;gBACAD,IAAOC;gBACPC,IAAUpB,KAAKI,EAAee;ADX1B;YCaN,IAAInB,KAAKC,EAAKmB,GAASJ,MAAS,GAAG;YACnChB,KAAKI,EAAeW,KAAOK;YAC3BL,IAAMG;ADVJ;QCYJlB,KAAKI,EAAeW,KAAOC;ADV3B;ICYFK;QACErB,KAAKS,IAAU;QACfT,KAAKI,EAAeM,SAAS;ADV7B;ICmBFF,KAAKQ;QACHhB,KAAKI,EAAeI,KAAKQ;QACzBhB,KAAKc,EAAQd,KAAKS;QAClBT,KAAKS,KAAW;ADVhB;ICkBFa;QACE,IAAItB,KAAKS,MAAY,GAAG;QACxB,MAAMtB,IAAQa,KAAKI,EAAe;QAClC,MAAMmB,IAAOvB,KAAKI,EAAekB;QACjCtB,KAAKS,KAAW;QAChB,IAAIT,KAAKS,GAAS;YAChBT,KAAKI,EAAe,KAAKmB;YACzBvB,KAAKa,EAAU,GAAGb,KAAKS,KAAW;ADThC;QCWJ,OAAOtB;ADTP;ICgBFqC;QACE,OAAOxB,KAAKI,EAAe;ADT3B;ICqBFqB,KAAKT;QACH,OAAOhB,KAAKI,EAAesB,QAAQV,MAAS;ADT5C;ICqBFW,OAAOX;QACL,MAAMY,IAAQ5B,KAAKI,EAAesB,QAAQV;QAC1C,IAAIY,IAAQ,GAAG,OAAO;QACtB,IAAIA,MAAU,GAAG;YACf5B,KAAKsB;ADRH,eCSG,IAAIM,MAAU5B,KAAKS,IAAU,GAAG;YACrCT,KAAKI,EAAekB;YACpBtB,KAAKS,KAAW;ADPd,eCQG;YACLT,KAAKI,EAAeyB,OAAOD,GAAO,GAAG5B,KAAKI,EAAekB;YACzDtB,KAAKS,KAAW;YAChBT,KAAKc,EAAQc;YACb5B,KAAKa,EAAUe,GAAO5B,KAAKS,KAAW;ADNpC;QCQJ,OAAO;ADNP;ICmBFqB,WAAWd;QACT,MAAMY,IAAQ5B,KAAKI,EAAesB,QAAQV;QAC1C,IAAIY,IAAQ,GAAG,OAAO;QACtB5B,KAAKc,EAAQc;QACb5B,KAAKa,EAAUe,GAAO5B,KAAKS,KAAW;QACtC,OAAO;ADLP;ICYFsB;QACE,OAAO,KAAI/B,KAAKI;ADLhB;;;ACOH,IAAA4B,WAEczC;;AAAaL,QAAAE,UAAA4C", "file": "PriorityQueue.js", "sourcesContent": ["import { Base } from \"../ContainerBase\";\nclass PriorityQueue extends Base {\n    /**\n     * @description PriorityQueue's constructor.\n     * @param container - Initialize container, must have a forEach function.\n     * @param cmp - Compare function.\n     * @param copy - When the container is an array, you can choose to directly operate on the original object of\n     *               the array or perform a shallow copy. The default is shallow copy.\n     * @example\n     * new PriorityQueue();\n     * new PriorityQueue([1, 2, 3]);\n     * new PriorityQueue([1, 2, 3], (x, y) => x - y);\n     * new PriorityQueue([1, 2, 3], (x, y) => x - y, false);\n     */\n    constructor(container = [], cmp = function (x, y) {\n        if (x > y)\n            return -1;\n        if (x < y)\n            return 1;\n        return 0;\n    }, copy = true) {\n        super();\n        this._cmp = cmp;\n        if (Array.isArray(container)) {\n            this._priorityQueue = copy ? [...container] : container;\n        }\n        else {\n            this._priorityQueue = [];\n            const self = this;\n            container.forEach(function (el) {\n                self._priorityQueue.push(el);\n            });\n        }\n        this._length = this._priorityQueue.length;\n        const halfLength = this._length >> 1;\n        for (let parent = (this._length - 1) >> 1; parent >= 0; --parent) {\n            this._pushDown(parent, halfLength);\n        }\n    }\n    /**\n     * @internal\n     */\n    _pushUp(pos) {\n        const item = this._priorityQueue[pos];\n        while (pos > 0) {\n            const parent = (pos - 1) >> 1;\n            const parentItem = this._priorityQueue[parent];\n            if (this._cmp(parentItem, item) <= 0)\n                break;\n            this._priorityQueue[pos] = parentItem;\n            pos = parent;\n        }\n        this._priorityQueue[pos] = item;\n    }\n    /**\n     * @internal\n     */\n    _pushDown(pos, halfLength) {\n        const item = this._priorityQueue[pos];\n        while (pos < halfLength) {\n            let left = pos << 1 | 1;\n            const right = left + 1;\n            let minItem = this._priorityQueue[left];\n            if (right < this._length &&\n                this._cmp(minItem, this._priorityQueue[right]) > 0) {\n                left = right;\n                minItem = this._priorityQueue[right];\n            }\n            if (this._cmp(minItem, item) >= 0)\n                break;\n            this._priorityQueue[pos] = minItem;\n            pos = left;\n        }\n        this._priorityQueue[pos] = item;\n    }\n    clear() {\n        this._length = 0;\n        this._priorityQueue.length = 0;\n    }\n    /**\n     * @description Push element into a container in order.\n     * @param item - The element you want to push.\n     * @returns The size of heap after pushing.\n     * @example\n     * queue.push(1);\n     */\n    push(item) {\n        this._priorityQueue.push(item);\n        this._pushUp(this._length);\n        this._length += 1;\n    }\n    /**\n     * @description Removes the top element.\n     * @returns The element you popped.\n     * @example\n     * queue.pop();\n     */\n    pop() {\n        if (this._length === 0)\n            return;\n        const value = this._priorityQueue[0];\n        const last = this._priorityQueue.pop();\n        this._length -= 1;\n        if (this._length) {\n            this._priorityQueue[0] = last;\n            this._pushDown(0, this._length >> 1);\n        }\n        return value;\n    }\n    /**\n     * @description Accesses the top element.\n     * @example\n     * const top = queue.top();\n     */\n    top() {\n        return this._priorityQueue[0];\n    }\n    /**\n     * @description Check if element is in heap.\n     * @param item - The item want to find.\n     * @returns Whether element is in heap.\n     * @example\n     * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n     * const obj = { id: 1 };\n     * que.push(obj);\n     * console.log(que.find(obj));  // true\n     */\n    find(item) {\n        return this._priorityQueue.indexOf(item) >= 0;\n    }\n    /**\n     * @description Remove specified item from heap.\n     * @param item - The item want to remove.\n     * @returns Whether remove success.\n     * @example\n     * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n     * const obj = { id: 1 };\n     * que.push(obj);\n     * que.remove(obj);\n     */\n    remove(item) {\n        const index = this._priorityQueue.indexOf(item);\n        if (index < 0)\n            return false;\n        if (index === 0) {\n            this.pop();\n        }\n        else if (index === this._length - 1) {\n            this._priorityQueue.pop();\n            this._length -= 1;\n        }\n        else {\n            this._priorityQueue.splice(index, 1, this._priorityQueue.pop());\n            this._length -= 1;\n            this._pushUp(index);\n            this._pushDown(index, this._length >> 1);\n        }\n        return true;\n    }\n    /**\n     * @description Update item and it's pos in the heap.\n     * @param item - The item want to update.\n     * @returns Whether update success.\n     * @example\n     * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n     * const obj = { id: 1 };\n     * que.push(obj);\n     * obj.id = 2;\n     * que.updateItem(obj);\n     */\n    updateItem(item) {\n        const index = this._priorityQueue.indexOf(item);\n        if (index < 0)\n            return false;\n        this._pushUp(index);\n        this._pushDown(index, this._length >> 1);\n        return true;\n    }\n    /**\n     * @returns Return a copy array of heap.\n     * @example\n     * const arr = queue.toArray();\n     */\n    toArray() {\n        return [...this._priorityQueue];\n    }\n}\nexport default PriorityQueue;\n", "import { Base, initContainer } from '@/container/ContainerBase';\n\nclass PriorityQueue<T> extends Base {\n  /**\n   * @internal\n   */\n  private readonly _priorityQueue: T[];\n  /**\n   * @internal\n   */\n  private readonly _cmp: (x: T, y: T) => number;\n  /**\n   * @description PriorityQueue's constructor.\n   * @param container - Initialize container, must have a forEach function.\n   * @param cmp - Compare function.\n   * @param copy - When the container is an array, you can choose to directly operate on the original object of\n   *               the array or perform a shallow copy. The default is shallow copy.\n   * @example\n   * new PriorityQueue();\n   * new PriorityQueue([1, 2, 3]);\n   * new PriorityQueue([1, 2, 3], (x, y) => x - y);\n   * new PriorityQueue([1, 2, 3], (x, y) => x - y, false);\n   */\n  constructor(\n    container: initContainer<T> = [],\n    cmp: (x: T, y: T) => number =\n    function (x: T, y: T) {\n      if (x > y) return -1;\n      if (x < y) return 1;\n      return 0;\n    },\n    copy = true\n  ) {\n    super();\n    this._cmp = cmp;\n    if (Array.isArray(container)) {\n      this._priorityQueue = copy ? [...container] : container;\n    } else {\n      this._priorityQueue = [];\n      const self = this;\n      container.forEach(function (el) {\n        self._priorityQueue.push(el);\n      });\n    }\n    this._length = this._priorityQueue.length;\n    const halfLength = this._length >> 1;\n    for (let parent = (this._length - 1) >> 1; parent >= 0; --parent) {\n      this._pushDown(parent, halfLength);\n    }\n  }\n  /**\n   * @internal\n   */\n  private _pushUp(pos: number) {\n    const item = this._priorityQueue[pos];\n    while (pos > 0) {\n      const parent = (pos - 1) >> 1;\n      const parentItem = this._priorityQueue[parent];\n      if (this._cmp(parentItem, item) <= 0) break;\n      this._priorityQueue[pos] = parentItem;\n      pos = parent;\n    }\n    this._priorityQueue[pos] = item;\n  }\n  /**\n   * @internal\n   */\n  private _pushDown(pos: number, halfLength: number) {\n    const item = this._priorityQueue[pos];\n    while (pos < halfLength) {\n      let left = pos << 1 | 1;\n      const right = left + 1;\n      let minItem = this._priorityQueue[left];\n      if (\n        right < this._length &&\n        this._cmp(minItem, this._priorityQueue[right]) > 0\n      ) {\n        left = right;\n        minItem = this._priorityQueue[right];\n      }\n      if (this._cmp(minItem, item) >= 0) break;\n      this._priorityQueue[pos] = minItem;\n      pos = left;\n    }\n    this._priorityQueue[pos] = item;\n  }\n  clear() {\n    this._length = 0;\n    this._priorityQueue.length = 0;\n  }\n  /**\n   * @description Push element into a container in order.\n   * @param item - The element you want to push.\n   * @returns The size of heap after pushing.\n   * @example\n   * queue.push(1);\n   */\n  push(item: T) {\n    this._priorityQueue.push(item);\n    this._pushUp(this._length);\n    this._length += 1;\n  }\n  /**\n   * @description Removes the top element.\n   * @returns The element you popped.\n   * @example\n   * queue.pop();\n   */\n  pop() {\n    if (this._length === 0) return;\n    const value = this._priorityQueue[0];\n    const last = this._priorityQueue.pop()!;\n    this._length -= 1;\n    if (this._length) {\n      this._priorityQueue[0] = last;\n      this._pushDown(0, this._length >> 1);\n    }\n    return value;\n  }\n  /**\n   * @description Accesses the top element.\n   * @example\n   * const top = queue.top();\n   */\n  top(): T | undefined {\n    return this._priorityQueue[0];\n  }\n  /**\n   * @description Check if element is in heap.\n   * @param item - The item want to find.\n   * @returns Whether element is in heap.\n   * @example\n   * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n   * const obj = { id: 1 };\n   * que.push(obj);\n   * console.log(que.find(obj));  // true\n   */\n  find(item: T) {\n    return this._priorityQueue.indexOf(item) >= 0;\n  }\n  /**\n   * @description Remove specified item from heap.\n   * @param item - The item want to remove.\n   * @returns Whether remove success.\n   * @example\n   * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n   * const obj = { id: 1 };\n   * que.push(obj);\n   * que.remove(obj);\n   */\n  remove(item: T) {\n    const index = this._priorityQueue.indexOf(item);\n    if (index < 0) return false;\n    if (index === 0) {\n      this.pop();\n    } else if (index === this._length - 1) {\n      this._priorityQueue.pop();\n      this._length -= 1;\n    } else {\n      this._priorityQueue.splice(index, 1, this._priorityQueue.pop()!);\n      this._length -= 1;\n      this._pushUp(index);\n      this._pushDown(index, this._length >> 1);\n    }\n    return true;\n  }\n  /**\n   * @description Update item and it's pos in the heap.\n   * @param item - The item want to update.\n   * @returns Whether update success.\n   * @example\n   * const que = new PriorityQueue([], (x, y) => x.id - y.id);\n   * const obj = { id: 1 };\n   * que.push(obj);\n   * obj.id = 2;\n   * que.updateItem(obj);\n   */\n  updateItem(item: T) {\n    const index = this._priorityQueue.indexOf(item);\n    if (index < 0) return false;\n    this._pushUp(index);\n    this._pushDown(index, this._length >> 1);\n    return true;\n  }\n  /**\n   * @returns Return a copy array of heap.\n   * @example\n   * const arr = queue.toArray();\n   */\n  toArray() {\n    return [...this._priorityQueue];\n  }\n}\n\nexport default PriorityQueue;\n"]}
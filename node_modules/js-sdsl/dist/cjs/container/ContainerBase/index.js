"use strict";

Object.defineProperty(exports, "t", {
    value: true
});

exports.ContainerIterator = exports.Container = exports.Base = void 0;

class ContainerIterator {
    constructor(t = 0) {
        this.iteratorType = t;
    }
    equals(t) {
        return this.o === t.o;
    }
}

exports.ContainerIterator = ContainerIterator;

class Base {
    constructor() {
        this.i = 0;
    }
    get length() {
        return this.i;
    }
    size() {
        return this.i;
    }
    empty() {
        return this.i === 0;
    }
}

exports.Base = Base;

class Container extends Base {}

exports.Container = Container;
//# sourceMappingURL=index.js.map

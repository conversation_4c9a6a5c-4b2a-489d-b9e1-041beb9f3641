{"version": 3, "sources": ["container/SequentialContainer/Vector.js", "../../src/container/SequentialContainer/Vector.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_Base", "_interopRequireDefault", "require", "_RandomIterator", "obj", "__esModule", "VectorIterator", "RandomIterator", "constructor", "node", "container", "iteratorType", "super", "this", "copy", "_node", "Vector", "SequentialContainer", "Array", "isArray", "_vector", "_length", "length", "self", "for<PERSON>ach", "el", "pushBack", "clear", "begin", "end", "rBegin", "rEnd", "front", "back", "getElementByPos", "pos", "RangeError", "eraseElementByPos", "splice", "eraseElementByValue", "index", "i", "eraseElementByIterator", "iter", "next", "element", "push", "popBack", "pop", "setElementByPos", "insert", "num", "fill", "find", "reverse", "unique", "sort", "cmp", "callback", "Symbol", "iterator", "bind", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,QAAAC,uBAAAC,QAAA;;AAEA,IAAAC,kBAAAD,QAAA;;AAAqF,SAAAD,uBAAAG;IAAA,OAAAA,KAAAA,EAAAC,IAAAD,IAAA;QAAAL,SAAAK;;AAAA;;AAGrF,MAAME,uBAA0BC,gBAAAA;IAE9BC,YAAYC,GAAcC,GAAsBC;QAC9CC,MAAMH,GAAME;QACZE,KAAKH,YAAYA;ADFjB;ICIFI;QACE,OAAO,IAAIR,eAAkBO,KAAKE,GAAOF,KAAKH,WAAWG,KAAKF;ADF9D;;;ACUJ,MAAMK,eAAkBC,MAAAA;IAUtBT,YAAYE,IAA8B,IAAII,IAAO;QACnDF;QACA,IAAIM,MAAMC,QAAQT,IAAY;YAC5BG,KAAKO,IAAUN,IAAO,KAAIJ,MAAaA;YACvCG,KAAKQ,IAAUX,EAAUY;ADXvB,eCYG;YACLT,KAAKO,IAAU;YACf,MAAMG,IAAOV;YACbH,EAAUc,SAAQ,SAAUC;gBAC1BF,EAAKG,SAASD;ADVV;AACJ;AACJ;ICYFE;QACEd,KAAKQ,IAAU;QACfR,KAAKO,EAAQE,SAAS;ADVtB;ICYFM;QACE,OAAO,IAAItB,eAAkB,GAAGO;ADVhC;ICYFgB;QACE,OAAO,IAAIvB,eAAkBO,KAAKQ,GAASR;ADV3C;ICYFiB;QACE,OAAO,IAAIxB,eAAkBO,KAAKQ,IAAU,GAAGR,MAAI;ADVnD;ICYFkB;QACE,OAAO,IAAIzB,gBAAmB,GAAGO,MAAI;ADVrC;ICYFmB;QACE,OAAOnB,KAAKO,EAAQ;ADVpB;ICYFa;QACE,OAAOpB,KAAKO,EAAQP,KAAKQ,IAAU;ADVnC;ICYFa,gBAAgBC;QDVV,ICWsBA,IAAG,KAAHA,IAAQtB,KAAKQ,IAAO,GA3DpC;YAAE,MAAU,IAAIe;ADkDtB;QCUJ,OAAOvB,KAAKO,EAAQe;ADRpB;ICUFE,kBAAkBF;QDRZ,ICSsBA,IAAG,KAAHA,IAAQtB,KAAKQ,IAAO,GA/DpC;YAAE,MAAU,IAAIe;ADwDtB;QCQJvB,KAAKO,EAAQkB,OAAOH,GAAK;QACzBtB,KAAKQ,KAAW;QAChB,OAAOR,KAAKQ;ADNZ;ICQFkB,oBAAoBzC;QAClB,IAAI0C,IAAQ;QACZ,KAAK,IAAIC,IAAI,GAAGA,IAAI5B,KAAKQ,KAAWoB,GAAG;YACrC,IAAI5B,KAAKO,EAAQqB,OAAO3C,GAAO;gBAC7Be,KAAKO,EAAQoB,OAAW3B,KAAKO,EAAQqB;ADNjC;AACJ;QCQJ5B,KAAKQ,IAAUR,KAAKO,EAAQE,SAASkB;QACrC,OAAO3B,KAAKQ;ADNZ;ICQFqB,uBAAuBC;QACrB,MAAM5B,IAAQ4B,EAAK5B;QACnB4B,IAAOA,EAAKC;QACZ/B,KAAKwB,kBAAkBtB;QACvB,OAAO4B;ADNP;ICQFjB,SAASmB;QACPhC,KAAKO,EAAQ0B,KAAKD;QAClBhC,KAAKQ,KAAW;QAChB,OAAOR,KAAKQ;ADNZ;ICQF0B;QACE,IAAIlC,KAAKQ,MAAY,GAAG;QACxBR,KAAKQ,KAAW;QAChB,OAAOR,KAAKO,EAAQ4B;ADLpB;ICOFC,gBAAgBd,GAAaU;QDLvB,ICMsBV,IAAG,KAAHA,IAAQtB,KAAKQ,IAAO,GA/FpC;YAAE,MAAU,IAAIe;AD2FtB;QCKJvB,KAAKO,EAAQe,KAAOU;ADHpB;ICKFK,OAAOf,GAAaU,GAAYM,IAAM;QDHhC,ICIsBhB,IAAG,KAAHA,IAAQtB,KAAKQ,GAnG7B;YAAE,MAAU,IAAIe;ADiGtB;QCGJvB,KAAKO,EAAQkB,OAAOH,GAAK,MAAM,IAAIjB,MAASiC,GAAKC,KAAKP;QACtDhC,KAAKQ,KAAW8B;QAChB,OAAOtC,KAAKQ;ADDZ;ICGFgC,KAAKR;QACH,KAAK,IAAIJ,IAAI,GAAGA,IAAI5B,KAAKQ,KAAWoB,GAAG;YACrC,IAAI5B,KAAKO,EAAQqB,OAAOI,GAAS;gBAC/B,OAAO,IAAIvC,eAAkBmC,GAAG5B;ADD5B;AACJ;QCGJ,OAAOA,KAAKgB;ADDZ;ICGFyB;QACEzC,KAAKO,EAAQkC;ADDb;ICGFC;QACE,IAAIf,IAAQ;QACZ,KAAK,IAAIC,IAAI,GAAGA,IAAI5B,KAAKQ,KAAWoB,GAAG;YACrC,IAAI5B,KAAKO,EAAQqB,OAAO5B,KAAKO,EAAQqB,IAAI,IAAI;gBAC3C5B,KAAKO,EAAQoB,OAAW3B,KAAKO,EAAQqB;ADDjC;AACJ;QCGJ5B,KAAKQ,IAAUR,KAAKO,EAAQE,SAASkB;QACrC,OAAO3B,KAAKQ;ADDZ;ICGFmC,KAAKC;QACH5C,KAAKO,EAAQoC,KAAKC;ADDlB;ICGFjC,QAAQkC;QACN,KAAK,IAAIjB,IAAI,GAAGA,IAAI5B,KAAKQ,KAAWoB,GAAG;YACrCiB,EAAS7C,KAAKO,EAAQqB,IAAIA,GAAG5B;ADD3B;AACJ;ICGF,CAAC8C,OAAOC;QACN,OAAO;mBACG/C,KAAKO;ADDX,UCEFyC,KAAKhD,KAFA;ADCP;;;ACGH,IAAAiD,WAEc9C;;AAAMnB,QAAAE,UAAA+D", "file": "Vector.js", "sourcesContent": ["import SequentialContainer from './Base';\nimport { RandomIterator } from \"./Base/RandomIterator\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nclass VectorIterator extends RandomIterator {\n    constructor(node, container, iteratorType) {\n        super(node, iteratorType);\n        this.container = container;\n    }\n    copy() {\n        return new VectorIterator(this._node, this.container, this.iteratorType);\n    }\n}\nclass Vector extends SequentialContainer {\n    /**\n     * @param container - Initialize container, must have a forEach function.\n     * @param copy - When the container is an array, you can choose to directly operate on the original object of\n     *               the array or perform a shallow copy. The default is shallow copy.\n     */\n    constructor(container = [], copy = true) {\n        super();\n        if (Array.isArray(container)) {\n            this._vector = copy ? [...container] : container;\n            this._length = container.length;\n        }\n        else {\n            this._vector = [];\n            const self = this;\n            container.forEach(function (el) {\n                self.pushBack(el);\n            });\n        }\n    }\n    clear() {\n        this._length = 0;\n        this._vector.length = 0;\n    }\n    begin() {\n        return new VectorIterator(0, this);\n    }\n    end() {\n        return new VectorIterator(this._length, this);\n    }\n    rBegin() {\n        return new VectorIterator(this._length - 1, this, 1 /* IteratorType.REVERSE */);\n    }\n    rEnd() {\n        return new VectorIterator(-1, this, 1 /* IteratorType.REVERSE */);\n    }\n    front() {\n        return this._vector[0];\n    }\n    back() {\n        return this._vector[this._length - 1];\n    }\n    getElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        return this._vector[pos];\n    }\n    eraseElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        this._vector.splice(pos, 1);\n        this._length -= 1;\n        return this._length;\n    }\n    eraseElementByValue(value) {\n        let index = 0;\n        for (let i = 0; i < this._length; ++i) {\n            if (this._vector[i] !== value) {\n                this._vector[index++] = this._vector[i];\n            }\n        }\n        this._length = this._vector.length = index;\n        return this._length;\n    }\n    eraseElementByIterator(iter) {\n        const _node = iter._node;\n        iter = iter.next();\n        this.eraseElementByPos(_node);\n        return iter;\n    }\n    pushBack(element) {\n        this._vector.push(element);\n        this._length += 1;\n        return this._length;\n    }\n    popBack() {\n        if (this._length === 0)\n            return;\n        this._length -= 1;\n        return this._vector.pop();\n    }\n    setElementByPos(pos, element) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        this._vector[pos] = element;\n    }\n    insert(pos, element, num = 1) {\n        if (pos < 0 || pos > this._length) {\n            throw new RangeError();\n        }\n        this._vector.splice(pos, 0, ...new Array(num).fill(element));\n        this._length += num;\n        return this._length;\n    }\n    find(element) {\n        for (let i = 0; i < this._length; ++i) {\n            if (this._vector[i] === element) {\n                return new VectorIterator(i, this);\n            }\n        }\n        return this.end();\n    }\n    reverse() {\n        this._vector.reverse();\n    }\n    unique() {\n        let index = 1;\n        for (let i = 1; i < this._length; ++i) {\n            if (this._vector[i] !== this._vector[i - 1]) {\n                this._vector[index++] = this._vector[i];\n            }\n        }\n        this._length = this._vector.length = index;\n        return this._length;\n    }\n    sort(cmp) {\n        this._vector.sort(cmp);\n    }\n    forEach(callback) {\n        for (let i = 0; i < this._length; ++i) {\n            callback(this._vector[i], i, this);\n        }\n    }\n    [Symbol.iterator]() {\n        return function* () {\n            yield* this._vector;\n        }.bind(this)();\n    }\n}\nexport default Vector;\n", "import SequentialContainer from './Base';\nimport { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { RandomIterator } from '@/container/SequentialContainer/Base/RandomIterator';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\n\nclass VectorIterator<T> extends RandomIterator<T> {\n  container: Vector<T>;\n  constructor(node: number, container: Vector<T>, iteratorType?: IteratorType) {\n    super(node, iteratorType);\n    this.container = container;\n  }\n  copy() {\n    return new VectorIterator<T>(this._node, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: VectorIterator<T>): boolean;\n}\n\nexport type { VectorIterator };\n\nclass Vector<T> extends SequentialContainer<T> {\n  /**\n   * @internal\n   */\n  private readonly _vector: T[];\n  /**\n   * @param container - Initialize container, must have a forEach function.\n   * @param copy - When the container is an array, you can choose to directly operate on the original object of\n   *               the array or perform a shallow copy. The default is shallow copy.\n   */\n  constructor(container: initContainer<T> = [], copy = true) {\n    super();\n    if (Array.isArray(container)) {\n      this._vector = copy ? [...container] : container;\n      this._length = container.length;\n    } else {\n      this._vector = [];\n      const self = this;\n      container.forEach(function (el) {\n        self.pushBack(el);\n      });\n    }\n  }\n  clear() {\n    this._length = 0;\n    this._vector.length = 0;\n  }\n  begin() {\n    return new VectorIterator<T>(0, this);\n  }\n  end() {\n    return new VectorIterator<T>(this._length, this);\n  }\n  rBegin() {\n    return new VectorIterator<T>(this._length - 1, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new VectorIterator<T>(-1, this, IteratorType.REVERSE);\n  }\n  front(): T | undefined {\n    return this._vector[0];\n  }\n  back(): T | undefined {\n    return this._vector[this._length - 1];\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    return this._vector[pos];\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    this._vector.splice(pos, 1);\n    this._length -= 1;\n    return this._length;\n  }\n  eraseElementByValue(value: T) {\n    let index = 0;\n    for (let i = 0; i < this._length; ++i) {\n      if (this._vector[i] !== value) {\n        this._vector[index++] = this._vector[i];\n      }\n    }\n    this._length = this._vector.length = index;\n    return this._length;\n  }\n  eraseElementByIterator(iter: VectorIterator<T>) {\n    const _node = iter._node;\n    iter = iter.next();\n    this.eraseElementByPos(_node);\n    return iter;\n  }\n  pushBack(element: T) {\n    this._vector.push(element);\n    this._length += 1;\n    return this._length;\n  }\n  popBack() {\n    if (this._length === 0) return;\n    this._length -= 1;\n    return this._vector.pop();\n  }\n  setElementByPos(pos: number, element: T) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    this._vector[pos] = element;\n  }\n  insert(pos: number, element: T, num = 1) {\n    $checkWithinAccessParams!(pos, 0, this._length);\n    this._vector.splice(pos, 0, ...new Array<T>(num).fill(element));\n    this._length += num;\n    return this._length;\n  }\n  find(element: T) {\n    for (let i = 0; i < this._length; ++i) {\n      if (this._vector[i] === element) {\n        return new VectorIterator<T>(i, this);\n      }\n    }\n    return this.end();\n  }\n  reverse() {\n    this._vector.reverse();\n  }\n  unique() {\n    let index = 1;\n    for (let i = 1; i < this._length; ++i) {\n      if (this._vector[i] !== this._vector[i - 1]) {\n        this._vector[index++] = this._vector[i];\n      }\n    }\n    this._length = this._vector.length = index;\n    return this._length;\n  }\n  sort(cmp?: (x: T, y: T) => number) {\n    this._vector.sort(cmp);\n  }\n  forEach(callback: (element: T, index: number, vector: Vector<T>) => void) {\n    for (let i = 0; i < this._length; ++i) {\n      callback(this._vector[i], i, this);\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: Vector<T>) {\n      yield * this._vector;\n    }.bind(this)();\n  }\n}\n\nexport default Vector;\n"]}
{"version": 3, "sources": ["container/SequentialContainer/Deque.js", "../../src/container/SequentialContainer/Deque.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_Base", "_interopRequireDefault", "require", "_RandomIterator", "obj", "__esModule", "DequeIterator", "RandomIterator", "constructor", "node", "container", "iteratorType", "super", "this", "copy", "_node", "<PERSON><PERSON>", "SequentialContainer", "_bucketSize", "_first", "_curFirst", "_last", "_curLast", "_bucketNum", "_map", "_length", "length", "size", "TypeError", "Math", "max", "ceil", "i", "push", "Array", "needBucketNum", "self", "for<PERSON>ach", "element", "pushBack", "_reAllocate", "newMap", "addBucketNum", "_getElementIndex", "pos", "offset", "offsetRemainder", "curNodePointerIndex", "curNodeBucketIndex", "clear", "begin", "end", "rBegin", "rEnd", "front", "back", "popBack", "pushFront", "popFront", "getElementByPos", "RangeError", "setElementByPos", "insert", "num", "arr", "cut", "eraseElementByPos", "el", "eraseElementByValue", "eraseElementByIterator", "iter", "next", "find", "reverse", "l", "r", "tmp", "unique", "index", "pre", "cur", "sort", "cmp", "shrinkToFit", "callback", "Symbol", "iterator", "bind", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,QAAAC,uBAAAC,QAAA;;AAEA,IAAAC,kBAAAD,QAAA;;AAAqF,SAAAD,uBAAAG;IAAA,OAAAA,KAAAA,EAAAC,IAAAD,IAAA;QAAAL,SAAAK;;AAAA;;AAIrF,MAAME,sBAAyBC,gBAAAA;IAE7BC,YAAYC,GAAcC,GAAqBC;QAC7CC,MAAMH,GAAME;QACZE,KAAKH,YAAYA;ADFjB;ICIFI;QACE,OAAO,IAAIR,cAAiBO,KAAKE,GAAOF,KAAKH,WAAWG,KAAKF;ADF7D;;;ACUJ,MAAMK,cAAiBC,MAAAA;IA6BrBT,YAAYE,IAA8B,IAAIQ,IAAe,KAAK;QAChEN;QA1BMC,KAAAM,IAAS;QAITN,KAAAO,IAAY;QAIZP,KAAAQ,IAAQ;QAIRR,KAAAS,IAAW;QAIXT,KAAAU,IAAa;QAQbV,KAAAW,IAAc;QAGpB,MAAMC,IAAO;YDXL,WCtC6Cf,EACvDgB,WAAM,UAAc,OAgDiBhB,EAhDCgB;YDuC5B,WCvCgDhB,EAEtDiB,SAAI,UAAc,OA8CejB,EA9CGiB;YDuC9B,WCtCHjB,EAAWiB,SAAI,YAChB,OA4C+BjB,EA5CbiB;YAAO,MAAQ,IAAIC,UAAU;ADwC9C,UCIQ;QACbf,KAAKK,IAAcA;QACnBL,KAAKU,IAAaM,KAAKC,IAAID,KAAKE,KAAKN,IAAUZ,KAAKK,IAAc;QAClE,KAAK,IAAIc,IAAI,GAAGA,IAAInB,KAAKU,KAAcS,GAAG;YACxCnB,KAAKW,EAAKS,KAAK,IAAIC,MAAMrB,KAAKK;ADH5B;QCKJ,MAAMiB,IAAgBN,KAAKE,KAAKN,IAAUZ,KAAKK;QAC/CL,KAAKM,IAASN,KAAKQ,KAASR,KAAKU,KAAc,MAAMY,KAAiB;QACtEtB,KAAKO,IAAYP,KAAKS,IAAYT,KAAKK,IAAcO,IAAUZ,KAAKK,KAAgB;QACpF,MAAMkB,IAAOvB;QACbH,EAAU2B,SAAQ,SAAUC;YAC1BF,EAAKG,SAASD;ADHZ;AACJ;ICSME;QACN,MAAMC,IAAS;QACf,MAAMC,IAAeb,KAAKC,IAAIjB,KAAKU,KAAc,GAAG;QACpD,KAAK,IAAIS,IAAI,GAAGA,IAAIU,KAAgBV,GAAG;YACrCS,EAAOT,KAAK,IAAIE,MAAMrB,KAAKK;ADHzB;QCKJ,KAAK,IAAIc,IAAInB,KAAKM,GAAQa,IAAInB,KAAKU,KAAcS,GAAG;YAClDS,EAAOA,EAAOf,UAAUb,KAAKW,EAAKQ;ADHhC;QCKJ,KAAK,IAAIA,IAAI,GAAGA,IAAInB,KAAKQ,KAASW,GAAG;YACnCS,EAAOA,EAAOf,UAAUb,KAAKW,EAAKQ;ADHhC;QCKJS,EAAOA,EAAOf,UAAU,KAAIb,KAAKW,EAAKX,KAAKQ;QAC3CR,KAAKM,IAASuB;QACd7B,KAAKQ,IAAQoB,EAAOf,SAAS;QAC7B,KAAK,IAAIM,IAAI,GAAGA,IAAIU,KAAgBV,GAAG;YACrCS,EAAOA,EAAOf,UAAU,IAAIQ,MAAMrB,KAAKK;ADHrC;QCKJL,KAAKW,IAAOiB;QACZ5B,KAAKU,IAAakB,EAAOf;ADHzB;ICUMiB,EAAiBC;QACvB,MAAMC,IAAShC,KAAKO,IAAYwB,IAAM;QACtC,MAAME,IAAkBD,IAAShC,KAAKK;QACtC,IAAI6B,IAAsBD,IAAkB;QAC5C,IAAIE,IAAqBnC,KAAKM,KAAU0B,IAASC,KAAmBjC,KAAKK;QACzE,IAAI4B,MAAoB,GAAGE,KAAsB;QACjDA,KAAsBnC,KAAKU;QAC3B,IAAIwB,IAAsB,GAAGA,KAAuBlC,KAAKK;QACzD,OAAO;YAAE8B;YAAoBD;;ADD7B;ICGFE;QACEpC,KAAKW,IAAO,EAAC,IAAIU,MAAMrB,KAAKK;QAC5BL,KAAKU,IAAa;QAClBV,KAAKM,IAASN,KAAKQ,IAAQR,KAAKY,IAAU;QAC1CZ,KAAKO,IAAYP,KAAKS,IAAWT,KAAKK,KAAe;ADDrD;ICGFgC;QACE,OAAO,IAAI5C,cAAiB,GAAGO;ADD/B;ICGFsC;QACE,OAAO,IAAI7C,cAAiBO,KAAKY,GAASZ;ADD1C;ICGFuC;QACE,OAAO,IAAI9C,cAAiBO,KAAKY,IAAU,GAAGZ,MAAI;ADDlD;ICGFwC;QACE,OAAO,IAAI/C,eAAkB,GAAGO,MAAI;ADDpC;ICGFyC;QACE,IAAIzC,KAAKY,MAAY,GAAG;QACxB,OAAOZ,KAAKW,EAAKX,KAAKM,GAAQN,KAAKO;ADAnC;ICEFmC;QACE,IAAI1C,KAAKY,MAAY,GAAG;QACxB,OAAOZ,KAAKW,EAAKX,KAAKQ,GAAOR,KAAKS;ADClC;ICCFiB,SAASD;QACP,IAAIzB,KAAKY,GAAS;YAChB,IAAIZ,KAAKS,IAAWT,KAAKK,IAAc,GAAG;gBACxCL,KAAKS,KAAY;ADCb,mBCAC,IAAIT,KAAKQ,IAAQR,KAAKU,IAAa,GAAG;gBAC3CV,KAAKQ,KAAS;gBACdR,KAAKS,IAAW;ADEZ,mBCDC;gBACLT,KAAKQ,IAAQ;gBACbR,KAAKS,IAAW;ADGZ;YCDN,IACET,KAAKQ,MAAUR,KAAKM,KACpBN,KAAKS,MAAaT,KAAKO,GACvBP,KAAK2B;ADEL;QCAJ3B,KAAKY,KAAW;QAChBZ,KAAKW,EAAKX,KAAKQ,GAAOR,KAAKS,KAAYgB;QACvC,OAAOzB,KAAKY;ADEZ;ICAF+B;QACE,IAAI3C,KAAKY,MAAY,GAAG;QACxB,MAAM3B,IAAQe,KAAKW,EAAKX,KAAKQ,GAAOR,KAAKS;QACzC,IAAIT,KAAKY,MAAY,GAAG;YACtB,IAAIZ,KAAKS,IAAW,GAAG;gBACrBT,KAAKS,KAAY;ADGb,mBCFC,IAAIT,KAAKQ,IAAQ,GAAG;gBACzBR,KAAKQ,KAAS;gBACdR,KAAKS,IAAWT,KAAKK,IAAc;ADI/B,mBCHC;gBACLL,KAAKQ,IAAQR,KAAKU,IAAa;gBAC/BV,KAAKS,IAAWT,KAAKK,IAAc;ADK/B;AACJ;QCHJL,KAAKY,KAAW;QAChB,OAAO3B;ADKP;ICEF2D,UAAUnB;QACR,IAAIzB,KAAKY,GAAS;YAChB,IAAIZ,KAAKO,IAAY,GAAG;gBACtBP,KAAKO,KAAa;ADKd,mBCJC,IAAIP,KAAKM,IAAS,GAAG;gBAC1BN,KAAKM,KAAU;gBACfN,KAAKO,IAAYP,KAAKK,IAAc;ADMhC,mBCLC;gBACLL,KAAKM,IAASN,KAAKU,IAAa;gBAChCV,KAAKO,IAAYP,KAAKK,IAAc;ADOhC;YCLN,IACEL,KAAKM,MAAWN,KAAKQ,KACrBR,KAAKO,MAAcP,KAAKS,GACxBT,KAAK2B;ADML;QCJJ3B,KAAKY,KAAW;QAChBZ,KAAKW,EAAKX,KAAKM,GAAQN,KAAKO,KAAakB;QACzC,OAAOzB,KAAKY;ADMZ;ICAFiC;QACE,IAAI7C,KAAKY,MAAY,GAAG;QACxB,MAAM3B,IAAQe,KAAKW,EAAKX,KAAKM,GAAQN,KAAKO;QAC1C,IAAIP,KAAKY,MAAY,GAAG;YACtB,IAAIZ,KAAKO,IAAYP,KAAKK,IAAc,GAAG;gBACzCL,KAAKO,KAAa;ADOd,mBCNC,IAAIP,KAAKM,IAASN,KAAKU,IAAa,GAAG;gBAC5CV,KAAKM,KAAU;gBACfN,KAAKO,IAAY;ADQb,mBCPC;gBACLP,KAAKM,IAAS;gBACdN,KAAKO,IAAY;ADSb;AACJ;QCPJP,KAAKY,KAAW;QAChB,OAAO3B;ADSP;ICPF6D,gBAAgBf;QDSV,ICRsBA,IAAG,KAAHA,IAAQ/B,KAAKY,IAAO,GAlNxB;YAAE,MAAU,IAAImC;AD4NlC;QCTJ,OAAMZ,oBACJA,GAAkBD,qBAClBA,KACElC,KAAK8B,EAAiBC;QAC1B,OAAO/B,KAAKW,EAAKwB,GAAoBD;ADQrC;ICNFc,gBAAgBjB,GAAaN;QDQvB,ICPsBM,IAAG,KAAHA,IAAQ/B,KAAKY,IAAO,GA1NxB;YAAE,MAAU,IAAImC;ADmOlC;QCRJ,OAAMZ,oBACJA,GAAkBD,qBAClBA,KACElC,KAAK8B,EAAiBC;QAC1B/B,KAAKW,EAAKwB,GAAoBD,KAAuBT;ADOrD;ICLFwB,OAAOlB,GAAaN,GAAYyB,IAAM;QDOhC,ICNsBnB,IAAG,KAAHA,IAAQ/B,KAAKY,GAlOjB;YAAE,MAAU,IAAImC;AD0OlC;QCPJ,IAAIhB,MAAQ,GAAG;YACb,OAAOmB,KAAOlD,KAAK4C,UAAUnB;ADU3B,eCTG,IAAIM,MAAQ/B,KAAKY,GAAS;YAC/B,OAAOsC,KAAOlD,KAAK0B,SAASD;ADY1B,eCXG;YACL,MAAM0B,IAAW;YACjB,KAAK,IAAIhC,IAAIY,GAAKZ,IAAInB,KAAKY,KAAWO,GAAG;gBACvCgC,EAAI/B,KAAKpB,KAAK8C,gBAAgB3B;ADa1B;YCXNnB,KAAKoD,IAAIrB,IAAM;YACf,KAAK,IAAIZ,IAAI,GAAGA,IAAI+B,KAAO/B,GAAGnB,KAAK0B,SAASD;YAC5C,KAAK,IAAIN,IAAI,GAAGA,IAAIgC,EAAItC,UAAUM,GAAGnB,KAAK0B,SAASyB,EAAIhC;ADerD;QCbJ,OAAOnB,KAAKY;ADeZ;ICNFwC,IAAIrB;QACF,IAAIA,IAAM,GAAG;YACX/B,KAAKoC;YACL,OAAO;ADeL;QCbJ,OAAMD,oBACJA,GAAkBD,qBAClBA,KACElC,KAAK8B,EAAiBC;QAC1B/B,KAAKQ,IAAQ2B;QACbnC,KAAKS,IAAWyB;QAChBlC,KAAKY,IAAUmB,IAAM;QACrB,OAAO/B,KAAKY;ADYZ;ICVFyC,kBAAkBtB;QDYZ,ICXsBA,IAAG,KAAHA,IAAQ/B,KAAKY,IAAO,GAxQxB;YAAE,MAAU,IAAImC;ADqRlC;QCZJ,IAAIhB,MAAQ,GAAG/B,KAAK6C,iBACf,IAAId,MAAQ/B,KAAKY,IAAU,GAAGZ,KAAK2C,gBACnC;YACH,MAAMQ,IAAM;YACZ,KAAK,IAAIhC,IAAIY,IAAM,GAAGZ,IAAInB,KAAKY,KAAWO,GAAG;gBAC3CgC,EAAI/B,KAAKpB,KAAK8C,gBAAgB3B;ADgB1B;YCdNnB,KAAKoD,IAAIrB;YACT/B,KAAK2C;YACL,MAAMpB,IAAOvB;YACbmD,EAAI3B,SAAQ,SAAU8B;gBACpB/B,EAAKG,SAAS4B;ADgBV;AACJ;QCdJ,OAAOtD,KAAKY;ADgBZ;ICdF2C,oBAAoBtE;QAClB,IAAIe,KAAKY,MAAY,GAAG,OAAO;QAC/B,MAAMuC,IAAW;QACjB,KAAK,IAAIhC,IAAI,GAAGA,IAAInB,KAAKY,KAAWO,GAAG;YACrC,MAAMM,IAAUzB,KAAK8C,gBAAgB3B;YACrC,IAAIM,MAAYxC,GAAOkE,EAAI/B,KAAKK;ADkB9B;QChBJ,MAAMb,IAAUuC,EAAItC;QACpB,KAAK,IAAIM,IAAI,GAAGA,IAAIP,KAAWO,GAAGnB,KAAKgD,gBAAgB7B,GAAGgC,EAAIhC;QAC9D,OAAOnB,KAAKoD,IAAIxC,IAAU;ADmB1B;ICjBF4C,uBAAuBC;QACrB,MAAMvD,IAAQuD,EAAKvD;QACnBF,KAAKqD,kBAAkBnD;QACvBuD,IAAOA,EAAKC;QACZ,OAAOD;ADmBP;ICjBFE,KAAKlC;QACH,KAAK,IAAIN,IAAI,GAAGA,IAAInB,KAAKY,KAAWO,GAAG;YACrC,IAAInB,KAAK8C,gBAAgB3B,OAAOM,GAAS;gBACvC,OAAO,IAAIhC,cAAiB0B,GAAGnB;ADmB3B;AACJ;QCjBJ,OAAOA,KAAKsC;ADmBZ;ICjBFsB;QACE,IAAIC,IAAI;QAAG,IAAIC,IAAI9D,KAAKY,IAAU;QAClC,OAAOiD,IAAIC,GAAG;YACZ,MAAMC,IAAM/D,KAAK8C,gBAAgBe;YACjC7D,KAAKgD,gBAAgBa,GAAG7D,KAAK8C,gBAAgBgB;YAC7C9D,KAAKgD,gBAAgBc,GAAGC;YACxBF,KAAK;YACLC,KAAK;ADoBH;AACJ;IClBFE;QACE,IAAIhE,KAAKY,KAAW,GAAG;YACrB,OAAOZ,KAAKY;ADoBV;QClBJ,IAAIqD,IAAQ;QACZ,IAAIC,IAAMlE,KAAK8C,gBAAgB;QAC/B,KAAK,IAAI3B,IAAI,GAAGA,IAAInB,KAAKY,KAAWO,GAAG;YACrC,MAAMgD,IAAMnE,KAAK8C,gBAAgB3B;YACjC,IAAIgD,MAAQD,GAAK;gBACfA,IAAMC;gBACNnE,KAAKgD,gBAAgBiB,KAASE;ADoB1B;AACJ;QClBJ,OAAOnE,KAAKY,IAAUqD,GAAOjE,KAAK2C;QAClC,OAAO3C,KAAKY;ADqBZ;ICnBFwD,KAAKC;QACH,MAAMlB,IAAW;QACjB,KAAK,IAAIhC,IAAI,GAAGA,IAAInB,KAAKY,KAAWO,GAAG;YACrCgC,EAAI/B,KAAKpB,KAAK8C,gBAAgB3B;ADqB5B;QCnBJgC,EAAIiB,KAAKC;QACT,KAAK,IAAIlD,IAAI,GAAGA,IAAInB,KAAKY,KAAWO,GAAGnB,KAAKgD,gBAAgB7B,GAAGgC,EAAIhC;ADsBnE;ICjBFmD;QACE,IAAItE,KAAKY,MAAY,GAAG;QACxB,MAAMuC,IAAW;QACjBnD,KAAKwB,SAAQ,SAAU8B;YACrBH,EAAI/B,KAAKkC;ADuBP;QCrBJtD,KAAKU,IAAaM,KAAKC,IAAID,KAAKE,KAAKlB,KAAKY,IAAUZ,KAAKK,IAAc;QACvEL,KAAKY,IAAUZ,KAAKM,IAASN,KAAKQ,IAAQR,KAAKO,IAAYP,KAAKS,IAAW;QAC3ET,KAAKW,IAAO;QACZ,KAAK,IAAIQ,IAAI,GAAGA,IAAInB,KAAKU,KAAcS,GAAG;YACxCnB,KAAKW,EAAKS,KAAK,IAAIC,MAAMrB,KAAKK;ADuB5B;QCrBJ,KAAK,IAAIc,IAAI,GAAGA,IAAIgC,EAAItC,UAAUM,GAAGnB,KAAK0B,SAASyB,EAAIhC;ADwBvD;ICtBFK,QAAQ+C;QACN,KAAK,IAAIpD,IAAI,GAAGA,IAAInB,KAAKY,KAAWO,GAAG;YACrCoD,EAASvE,KAAK8C,gBAAgB3B,IAAIA,GAAGnB;ADwBnC;AACJ;ICtBF,CAACwE,OAAOC;QACN,OAAO;YACL,KAAK,IAAItD,IAAI,GAAGA,IAAInB,KAAKY,KAAWO,GAAG;sBAC/BnB,KAAK8C,gBAAgB3B;ADwBvB;AACJ,UCvBFuD,KAAK1E,KAJA;AD4BP;;;ACtBH,IAAA2E,WAEcxE;;AAAKnB,QAAAE,UAAAyF", "file": "Deque.js", "sourcesContent": ["import SequentialContainer from './Base';\nimport { RandomIterator } from \"./Base/RandomIterator\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nimport $getContainerSize from \"../../utils/getContainerSize.macro\";\nclass DequeIterator extends RandomIterator {\n    constructor(node, container, iteratorType) {\n        super(node, iteratorType);\n        this.container = container;\n    }\n    copy() {\n        return new DequeIterator(this._node, this.container, this.iteratorType);\n    }\n}\nclass Deque extends SequentialContainer {\n    constructor(container = [], _bucketSize = (1 << 12)) {\n        super();\n        /**\n         * @internal\n         */\n        this._first = 0;\n        /**\n         * @internal\n         */\n        this._curFirst = 0;\n        /**\n         * @internal\n         */\n        this._last = 0;\n        /**\n         * @internal\n         */\n        this._curLast = 0;\n        /**\n         * @internal\n         */\n        this._bucketNum = 0;\n        /**\n         * @internal\n         */\n        this._map = [];\n        const _length = (() => {\n            if (typeof container.length === \"number\")\n                return container.length;\n            if (typeof container.size === \"number\")\n                return container.size;\n            if (typeof container.size === \"function\")\n                return container.size();\n            throw new TypeError(\"Cannot get the length or size of the container\");\n        })();\n        this._bucketSize = _bucketSize;\n        this._bucketNum = Math.max(Math.ceil(_length / this._bucketSize), 1);\n        for (let i = 0; i < this._bucketNum; ++i) {\n            this._map.push(new Array(this._bucketSize));\n        }\n        const needBucketNum = Math.ceil(_length / this._bucketSize);\n        this._first = this._last = (this._bucketNum >> 1) - (needBucketNum >> 1);\n        this._curFirst = this._curLast = (this._bucketSize - _length % this._bucketSize) >> 1;\n        const self = this;\n        container.forEach(function (element) {\n            self.pushBack(element);\n        });\n    }\n    /**\n     * @description Growth the Deque.\n     * @internal\n     */\n    _reAllocate() {\n        const newMap = [];\n        const addBucketNum = Math.max(this._bucketNum >> 1, 1);\n        for (let i = 0; i < addBucketNum; ++i) {\n            newMap[i] = new Array(this._bucketSize);\n        }\n        for (let i = this._first; i < this._bucketNum; ++i) {\n            newMap[newMap.length] = this._map[i];\n        }\n        for (let i = 0; i < this._last; ++i) {\n            newMap[newMap.length] = this._map[i];\n        }\n        newMap[newMap.length] = [...this._map[this._last]];\n        this._first = addBucketNum;\n        this._last = newMap.length - 1;\n        for (let i = 0; i < addBucketNum; ++i) {\n            newMap[newMap.length] = new Array(this._bucketSize);\n        }\n        this._map = newMap;\n        this._bucketNum = newMap.length;\n    }\n    /**\n     * @description Get the bucket position of the element and the pointer position by index.\n     * @param pos - The element's index.\n     * @internal\n     */\n    _getElementIndex(pos) {\n        const offset = this._curFirst + pos + 1;\n        const offsetRemainder = offset % this._bucketSize;\n        let curNodePointerIndex = offsetRemainder - 1;\n        let curNodeBucketIndex = this._first + (offset - offsetRemainder) / this._bucketSize;\n        if (offsetRemainder === 0)\n            curNodeBucketIndex -= 1;\n        curNodeBucketIndex %= this._bucketNum;\n        if (curNodePointerIndex < 0)\n            curNodePointerIndex += this._bucketSize;\n        return { curNodeBucketIndex, curNodePointerIndex };\n    }\n    clear() {\n        this._map = [new Array(this._bucketSize)];\n        this._bucketNum = 1;\n        this._first = this._last = this._length = 0;\n        this._curFirst = this._curLast = this._bucketSize >> 1;\n    }\n    begin() {\n        return new DequeIterator(0, this);\n    }\n    end() {\n        return new DequeIterator(this._length, this);\n    }\n    rBegin() {\n        return new DequeIterator(this._length - 1, this, 1 /* IteratorType.REVERSE */);\n    }\n    rEnd() {\n        return new DequeIterator(-1, this, 1 /* IteratorType.REVERSE */);\n    }\n    front() {\n        if (this._length === 0)\n            return;\n        return this._map[this._first][this._curFirst];\n    }\n    back() {\n        if (this._length === 0)\n            return;\n        return this._map[this._last][this._curLast];\n    }\n    pushBack(element) {\n        if (this._length) {\n            if (this._curLast < this._bucketSize - 1) {\n                this._curLast += 1;\n            }\n            else if (this._last < this._bucketNum - 1) {\n                this._last += 1;\n                this._curLast = 0;\n            }\n            else {\n                this._last = 0;\n                this._curLast = 0;\n            }\n            if (this._last === this._first &&\n                this._curLast === this._curFirst)\n                this._reAllocate();\n        }\n        this._length += 1;\n        this._map[this._last][this._curLast] = element;\n        return this._length;\n    }\n    popBack() {\n        if (this._length === 0)\n            return;\n        const value = this._map[this._last][this._curLast];\n        if (this._length !== 1) {\n            if (this._curLast > 0) {\n                this._curLast -= 1;\n            }\n            else if (this._last > 0) {\n                this._last -= 1;\n                this._curLast = this._bucketSize - 1;\n            }\n            else {\n                this._last = this._bucketNum - 1;\n                this._curLast = this._bucketSize - 1;\n            }\n        }\n        this._length -= 1;\n        return value;\n    }\n    /**\n     * @description Push the element to the front.\n     * @param element - The element you want to push.\n     * @returns The size of queue after pushing.\n     */\n    pushFront(element) {\n        if (this._length) {\n            if (this._curFirst > 0) {\n                this._curFirst -= 1;\n            }\n            else if (this._first > 0) {\n                this._first -= 1;\n                this._curFirst = this._bucketSize - 1;\n            }\n            else {\n                this._first = this._bucketNum - 1;\n                this._curFirst = this._bucketSize - 1;\n            }\n            if (this._first === this._last &&\n                this._curFirst === this._curLast)\n                this._reAllocate();\n        }\n        this._length += 1;\n        this._map[this._first][this._curFirst] = element;\n        return this._length;\n    }\n    /**\n     * @description Remove the _first element.\n     * @returns The element you popped.\n     */\n    popFront() {\n        if (this._length === 0)\n            return;\n        const value = this._map[this._first][this._curFirst];\n        if (this._length !== 1) {\n            if (this._curFirst < this._bucketSize - 1) {\n                this._curFirst += 1;\n            }\n            else if (this._first < this._bucketNum - 1) {\n                this._first += 1;\n                this._curFirst = 0;\n            }\n            else {\n                this._first = 0;\n                this._curFirst = 0;\n            }\n        }\n        this._length -= 1;\n        return value;\n    }\n    getElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        const { curNodeBucketIndex, curNodePointerIndex } = this._getElementIndex(pos);\n        return this._map[curNodeBucketIndex][curNodePointerIndex];\n    }\n    setElementByPos(pos, element) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        const { curNodeBucketIndex, curNodePointerIndex } = this._getElementIndex(pos);\n        this._map[curNodeBucketIndex][curNodePointerIndex] = element;\n    }\n    insert(pos, element, num = 1) {\n        if (pos < 0 || pos > this._length) {\n            throw new RangeError();\n        }\n        if (pos === 0) {\n            while (num--)\n                this.pushFront(element);\n        }\n        else if (pos === this._length) {\n            while (num--)\n                this.pushBack(element);\n        }\n        else {\n            const arr = [];\n            for (let i = pos; i < this._length; ++i) {\n                arr.push(this.getElementByPos(i));\n            }\n            this.cut(pos - 1);\n            for (let i = 0; i < num; ++i)\n                this.pushBack(element);\n            for (let i = 0; i < arr.length; ++i)\n                this.pushBack(arr[i]);\n        }\n        return this._length;\n    }\n    /**\n     * @description Remove all elements after the specified position (excluding the specified position).\n     * @param pos - The previous position of the first removed element.\n     * @returns The size of the container after cutting.\n     * @example\n     * deque.cut(1); // Then deque's size will be 2. deque -> [0, 1]\n     */\n    cut(pos) {\n        if (pos < 0) {\n            this.clear();\n            return 0;\n        }\n        const { curNodeBucketIndex, curNodePointerIndex } = this._getElementIndex(pos);\n        this._last = curNodeBucketIndex;\n        this._curLast = curNodePointerIndex;\n        this._length = pos + 1;\n        return this._length;\n    }\n    eraseElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        if (pos === 0)\n            this.popFront();\n        else if (pos === this._length - 1)\n            this.popBack();\n        else {\n            const arr = [];\n            for (let i = pos + 1; i < this._length; ++i) {\n                arr.push(this.getElementByPos(i));\n            }\n            this.cut(pos);\n            this.popBack();\n            const self = this;\n            arr.forEach(function (el) {\n                self.pushBack(el);\n            });\n        }\n        return this._length;\n    }\n    eraseElementByValue(value) {\n        if (this._length === 0)\n            return 0;\n        const arr = [];\n        for (let i = 0; i < this._length; ++i) {\n            const element = this.getElementByPos(i);\n            if (element !== value)\n                arr.push(element);\n        }\n        const _length = arr.length;\n        for (let i = 0; i < _length; ++i)\n            this.setElementByPos(i, arr[i]);\n        return this.cut(_length - 1);\n    }\n    eraseElementByIterator(iter) {\n        const _node = iter._node;\n        this.eraseElementByPos(_node);\n        iter = iter.next();\n        return iter;\n    }\n    find(element) {\n        for (let i = 0; i < this._length; ++i) {\n            if (this.getElementByPos(i) === element) {\n                return new DequeIterator(i, this);\n            }\n        }\n        return this.end();\n    }\n    reverse() {\n        let l = 0;\n        let r = this._length - 1;\n        while (l < r) {\n            const tmp = this.getElementByPos(l);\n            this.setElementByPos(l, this.getElementByPos(r));\n            this.setElementByPos(r, tmp);\n            l += 1;\n            r -= 1;\n        }\n    }\n    unique() {\n        if (this._length <= 1) {\n            return this._length;\n        }\n        let index = 1;\n        let pre = this.getElementByPos(0);\n        for (let i = 1; i < this._length; ++i) {\n            const cur = this.getElementByPos(i);\n            if (cur !== pre) {\n                pre = cur;\n                this.setElementByPos(index++, cur);\n            }\n        }\n        while (this._length > index)\n            this.popBack();\n        return this._length;\n    }\n    sort(cmp) {\n        const arr = [];\n        for (let i = 0; i < this._length; ++i) {\n            arr.push(this.getElementByPos(i));\n        }\n        arr.sort(cmp);\n        for (let i = 0; i < this._length; ++i)\n            this.setElementByPos(i, arr[i]);\n    }\n    /**\n     * @description Remove as much useless space as possible.\n     */\n    shrinkToFit() {\n        if (this._length === 0)\n            return;\n        const arr = [];\n        this.forEach(function (el) {\n            arr.push(el);\n        });\n        this._bucketNum = Math.max(Math.ceil(this._length / this._bucketSize), 1);\n        this._length = this._first = this._last = this._curFirst = this._curLast = 0;\n        this._map = [];\n        for (let i = 0; i < this._bucketNum; ++i) {\n            this._map.push(new Array(this._bucketSize));\n        }\n        for (let i = 0; i < arr.length; ++i)\n            this.pushBack(arr[i]);\n    }\n    forEach(callback) {\n        for (let i = 0; i < this._length; ++i) {\n            callback(this.getElementByPos(i), i, this);\n        }\n    }\n    [Symbol.iterator]() {\n        return function* () {\n            for (let i = 0; i < this._length; ++i) {\n                yield this.getElementByPos(i);\n            }\n        }.bind(this)();\n    }\n}\nexport default Deque;\n", "import SequentialContainer from './Base';\nimport { IteratorType, initContainer } from '@/container/ContainerBase';\nimport { RandomIterator } from '@/container/SequentialContainer/Base/RandomIterator';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport $getContainerSize from '@/utils/getContainerSize.macro';\n\nclass DequeIterator<T> extends RandomIterator<T> {\n  readonly container: Deque<T>;\n  constructor(node: number, container: Deque<T>, iteratorType?: IteratorType) {\n    super(node, iteratorType);\n    this.container = container;\n  }\n  copy() {\n    return new DequeIterator<T>(this._node, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: DequeIterator<T>): boolean;\n}\n\nexport type { DequeIterator };\n\nclass Deque<T> extends SequentialContainer<T> {\n  /**\n   * @internal\n   */\n  private _first = 0;\n  /**\n   * @internal\n   */\n  private _curFirst = 0;\n  /**\n   * @internal\n   */\n  private _last = 0;\n  /**\n   * @internal\n   */\n  private _curLast = 0;\n  /**\n   * @internal\n   */\n  private _bucketNum = 0;\n  /**\n   * @internal\n   */\n  private readonly _bucketSize: number;\n  /**\n   * @internal\n   */\n  private _map: T[][] = [];\n  constructor(container: initContainer<T> = [], _bucketSize = (1 << 12)) {\n    super();\n    const _length = $getContainerSize!(container);\n    this._bucketSize = _bucketSize;\n    this._bucketNum = Math.max(Math.ceil(_length / this._bucketSize), 1);\n    for (let i = 0; i < this._bucketNum; ++i) {\n      this._map.push(new Array(this._bucketSize));\n    }\n    const needBucketNum = Math.ceil(_length / this._bucketSize);\n    this._first = this._last = (this._bucketNum >> 1) - (needBucketNum >> 1);\n    this._curFirst = this._curLast = (this._bucketSize - _length % this._bucketSize) >> 1;\n    const self = this;\n    container.forEach(function (element) {\n      self.pushBack(element);\n    });\n  }\n  /**\n   * @description Growth the Deque.\n   * @internal\n   */\n  private _reAllocate() {\n    const newMap = [];\n    const addBucketNum = Math.max(this._bucketNum >> 1, 1);\n    for (let i = 0; i < addBucketNum; ++i) {\n      newMap[i] = new Array(this._bucketSize);\n    }\n    for (let i = this._first; i < this._bucketNum; ++i) {\n      newMap[newMap.length] = this._map[i];\n    }\n    for (let i = 0; i < this._last; ++i) {\n      newMap[newMap.length] = this._map[i];\n    }\n    newMap[newMap.length] = [...this._map[this._last]];\n    this._first = addBucketNum;\n    this._last = newMap.length - 1;\n    for (let i = 0; i < addBucketNum; ++i) {\n      newMap[newMap.length] = new Array(this._bucketSize);\n    }\n    this._map = newMap;\n    this._bucketNum = newMap.length;\n  }\n  /**\n   * @description Get the bucket position of the element and the pointer position by index.\n   * @param pos - The element's index.\n   * @internal\n   */\n  private _getElementIndex(pos: number) {\n    const offset = this._curFirst + pos + 1;\n    const offsetRemainder = offset % this._bucketSize;\n    let curNodePointerIndex = offsetRemainder - 1;\n    let curNodeBucketIndex = this._first + (offset - offsetRemainder) / this._bucketSize;\n    if (offsetRemainder === 0) curNodeBucketIndex -= 1;\n    curNodeBucketIndex %= this._bucketNum;\n    if (curNodePointerIndex < 0) curNodePointerIndex += this._bucketSize;\n    return { curNodeBucketIndex, curNodePointerIndex };\n  }\n  clear() {\n    this._map = [new Array(this._bucketSize)];\n    this._bucketNum = 1;\n    this._first = this._last = this._length = 0;\n    this._curFirst = this._curLast = this._bucketSize >> 1;\n  }\n  begin() {\n    return new DequeIterator<T>(0, this);\n  }\n  end() {\n    return new DequeIterator<T>(this._length, this);\n  }\n  rBegin() {\n    return new DequeIterator<T>(this._length - 1, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new DequeIterator<T>(-1, this, IteratorType.REVERSE);\n  }\n  front(): T | undefined {\n    if (this._length === 0) return;\n    return this._map[this._first][this._curFirst];\n  }\n  back(): T | undefined {\n    if (this._length === 0) return;\n    return this._map[this._last][this._curLast];\n  }\n  pushBack(element: T) {\n    if (this._length) {\n      if (this._curLast < this._bucketSize - 1) {\n        this._curLast += 1;\n      } else if (this._last < this._bucketNum - 1) {\n        this._last += 1;\n        this._curLast = 0;\n      } else {\n        this._last = 0;\n        this._curLast = 0;\n      }\n      if (\n        this._last === this._first &&\n        this._curLast === this._curFirst\n      ) this._reAllocate();\n    }\n    this._length += 1;\n    this._map[this._last][this._curLast] = element;\n    return this._length;\n  }\n  popBack() {\n    if (this._length === 0) return;\n    const value = this._map[this._last][this._curLast];\n    if (this._length !== 1) {\n      if (this._curLast > 0) {\n        this._curLast -= 1;\n      } else if (this._last > 0) {\n        this._last -= 1;\n        this._curLast = this._bucketSize - 1;\n      } else {\n        this._last = this._bucketNum - 1;\n        this._curLast = this._bucketSize - 1;\n      }\n    }\n    this._length -= 1;\n    return value;\n  }\n  /**\n   * @description Push the element to the front.\n   * @param element - The element you want to push.\n   * @returns The size of queue after pushing.\n   */\n  pushFront(element: T) {\n    if (this._length) {\n      if (this._curFirst > 0) {\n        this._curFirst -= 1;\n      } else if (this._first > 0) {\n        this._first -= 1;\n        this._curFirst = this._bucketSize - 1;\n      } else {\n        this._first = this._bucketNum - 1;\n        this._curFirst = this._bucketSize - 1;\n      }\n      if (\n        this._first === this._last &&\n        this._curFirst === this._curLast\n      ) this._reAllocate();\n    }\n    this._length += 1;\n    this._map[this._first][this._curFirst] = element;\n    return this._length;\n  }\n  /**\n   * @description Remove the _first element.\n   * @returns The element you popped.\n   */\n  popFront() {\n    if (this._length === 0) return;\n    const value = this._map[this._first][this._curFirst];\n    if (this._length !== 1) {\n      if (this._curFirst < this._bucketSize - 1) {\n        this._curFirst += 1;\n      } else if (this._first < this._bucketNum - 1) {\n        this._first += 1;\n        this._curFirst = 0;\n      } else {\n        this._first = 0;\n        this._curFirst = 0;\n      }\n    }\n    this._length -= 1;\n    return value;\n  }\n  getElementByPos(pos: number): T {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    const {\n      curNodeBucketIndex,\n      curNodePointerIndex\n    } = this._getElementIndex(pos);\n    return this._map[curNodeBucketIndex][curNodePointerIndex]!;\n  }\n  setElementByPos(pos: number, element: T) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    const {\n      curNodeBucketIndex,\n      curNodePointerIndex\n    } = this._getElementIndex(pos);\n    this._map[curNodeBucketIndex][curNodePointerIndex] = element;\n  }\n  insert(pos: number, element: T, num = 1) {\n    $checkWithinAccessParams!(pos, 0, this._length);\n    if (pos === 0) {\n      while (num--) this.pushFront(element);\n    } else if (pos === this._length) {\n      while (num--) this.pushBack(element);\n    } else {\n      const arr: T[] = [];\n      for (let i = pos; i < this._length; ++i) {\n        arr.push(this.getElementByPos(i));\n      }\n      this.cut(pos - 1);\n      for (let i = 0; i < num; ++i) this.pushBack(element);\n      for (let i = 0; i < arr.length; ++i) this.pushBack(arr[i]);\n    }\n    return this._length;\n  }\n  /**\n   * @description Remove all elements after the specified position (excluding the specified position).\n   * @param pos - The previous position of the first removed element.\n   * @returns The size of the container after cutting.\n   * @example\n   * deque.cut(1); // Then deque's size will be 2. deque -> [0, 1]\n   */\n  cut(pos: number) {\n    if (pos < 0) {\n      this.clear();\n      return 0;\n    }\n    const {\n      curNodeBucketIndex,\n      curNodePointerIndex\n    } = this._getElementIndex(pos);\n    this._last = curNodeBucketIndex;\n    this._curLast = curNodePointerIndex;\n    this._length = pos + 1;\n    return this._length;\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    if (pos === 0) this.popFront();\n    else if (pos === this._length - 1) this.popBack();\n    else {\n      const arr = [];\n      for (let i = pos + 1; i < this._length; ++i) {\n        arr.push(this.getElementByPos(i));\n      }\n      this.cut(pos);\n      this.popBack();\n      const self = this;\n      arr.forEach(function (el) {\n        self.pushBack(el);\n      });\n    }\n    return this._length;\n  }\n  eraseElementByValue(value: T) {\n    if (this._length === 0) return 0;\n    const arr: T[] = [];\n    for (let i = 0; i < this._length; ++i) {\n      const element = this.getElementByPos(i);\n      if (element !== value) arr.push(element);\n    }\n    const _length = arr.length;\n    for (let i = 0; i < _length; ++i) this.setElementByPos(i, arr[i]);\n    return this.cut(_length - 1);\n  }\n  eraseElementByIterator(iter: DequeIterator<T>) {\n    const _node = iter._node;\n    this.eraseElementByPos(_node);\n    iter = iter.next();\n    return iter;\n  }\n  find(element: T) {\n    for (let i = 0; i < this._length; ++i) {\n      if (this.getElementByPos(i) === element) {\n        return new DequeIterator<T>(i, this);\n      }\n    }\n    return this.end();\n  }\n  reverse() {\n    let l = 0; let r = this._length - 1;\n    while (l < r) {\n      const tmp = this.getElementByPos(l);\n      this.setElementByPos(l, this.getElementByPos(r));\n      this.setElementByPos(r, tmp);\n      l += 1;\n      r -= 1;\n    }\n  }\n  unique() {\n    if (this._length <= 1) {\n      return this._length;\n    }\n    let index = 1;\n    let pre = this.getElementByPos(0);\n    for (let i = 1; i < this._length; ++i) {\n      const cur = this.getElementByPos(i);\n      if (cur !== pre) {\n        pre = cur;\n        this.setElementByPos(index++, cur);\n      }\n    }\n    while (this._length > index) this.popBack();\n    return this._length;\n  }\n  sort(cmp?: (x: T, y: T) => number) {\n    const arr: T[] = [];\n    for (let i = 0; i < this._length; ++i) {\n      arr.push(this.getElementByPos(i));\n    }\n    arr.sort(cmp);\n    for (let i = 0; i < this._length; ++i) this.setElementByPos(i, arr[i]);\n  }\n  /**\n   * @description Remove as much useless space as possible.\n   */\n  shrinkToFit() {\n    if (this._length === 0) return;\n    const arr: T[] = [];\n    this.forEach(function (el) {\n      arr.push(el);\n    });\n    this._bucketNum = Math.max(Math.ceil(this._length / this._bucketSize), 1);\n    this._length = this._first = this._last = this._curFirst = this._curLast = 0;\n    this._map = [];\n    for (let i = 0; i < this._bucketNum; ++i) {\n      this._map.push(new Array(this._bucketSize));\n    }\n    for (let i = 0; i < arr.length; ++i) this.pushBack(arr[i]);\n  }\n  forEach(callback: (element: T, index: number, deque: Deque<T>) => void) {\n    for (let i = 0; i < this._length; ++i) {\n      callback(this.getElementByPos(i), i, this);\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: Deque<T>) {\n      for (let i = 0; i < this._length; ++i) {\n        yield this.getElementByPos(i);\n      }\n    }.bind(this)();\n  }\n}\n\nexport default Deque;\n"]}
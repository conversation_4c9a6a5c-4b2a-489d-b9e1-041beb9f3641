{"version": 3, "sources": ["container/TreeContainer/Base/index.js", "../../src/container/TreeContainer/Base/index.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_TreeNode", "require", "_ContainerBase", "_throwError", "TreeC<PERSON>r", "Container", "constructor", "cmp", "x", "y", "enableIndex", "super", "this", "_root", "undefined", "_cmp", "_TreeNodeClass", "TreeNodeEnableIndex", "_set", "key", "hint", "curNode", "_preSet", "p", "_parent", "_header", "_subTreeSize", "nodeList", "_insertNodeSelfBalance", "parentNode", "grandParent", "_recount", "_length", "_eraseNode", "_preEraseNode", "TreeNode", "_lowerBound", "resNode", "cmpResult", "_key", "_right", "_left", "_upperBound", "_reverseLowerBound", "_reverseUpperBound", "_eraseNodeSelfBalance", "_color", "brother", "_rotateLeft", "_rotateRight", "clear", "swapNode", "_value", "_inOrderTraversal", "callback", "ifReturn", "uncle", "GP", "minNode", "compareToMin", "maxNode", "compareToMax", "iterNode", "_node", "iterCmpRes", "preNode", "_pre", "preCmpRes", "_findElementNode", "updateKeyByIterator", "iter", "node", "throwIteratorAccessError", "_next", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "eraseElementByPos", "pos", "RangeError", "index", "self", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eraseElementByIterator", "hasNoRight", "isNormal", "iteratorType", "next", "for<PERSON>ach", "element", "getElementByPos", "res", "getHeight", "traversal", "Math", "max", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACJvB,IAAAC,YAAAC,QAAA;;AACA,IAAAC,iBAAAD,QAAA;;AAEA,IAAAE,cAAAF,QAAA;;AAEA,MAAeG,sBAA4BC,eAAAA;IA4BzCC,YACEC,IACA,SAAUC,GAAMC;QACd,IAAID,IAAIC,GAAG,QAAQ;QACnB,IAAID,IAAIC,GAAG,OAAO;QAClB,OAAO;ADzBT,OC2BAC,IAAc;QAEdC;QAjCQC,KAAAC,IAAoCC;QAkC5CF,KAAKG,IAAOR;QACZ,IAAIG,GAAa;YACfE,KAAKI,KAAiBC,UAAAA;YACtBL,KAAKM,IAAO,SAAUC,GAAKrB,GAAOsB;gBAChC,MAAMC,IAAUT,KAAKU,GAAQH,GAAKrB,GAAOsB;gBACzC,IAAIC,GAAS;oBACX,IAAIE,IAAIF,EAAQG;oBAChB,OAAOD,MAAMX,KAAKa,GAAS;wBACzBF,EAAEG,MAAgB;wBAClBH,IAAIA,EAAEC;ADvBE;oBCyBV,MAAMG,IAAWf,KAAKgB,GAAuBP;oBAC7C,IAAIM,GAAU;wBACZ,OAAME,YACJA,GAAUC,aACVA,GAAWT,SACXA,KACEM;wBACJE,EAAWE;wBACXD,EAAYC;wBACZV,EAAQU;AD3BA;AACJ;gBC6BR,OAAOnB,KAAKoB;AD3BR;YC6BNpB,KAAKqB,IAAa,SAAUZ;gBAC1B,IAAIE,IAAIX,KAAKsB,GAAcb;gBAC3B,OAAOE,MAAMX,KAAKa,GAAS;oBACzBF,EAAEG,MAAgB;oBAClBH,IAAIA,EAAEC;AD3BA;AACJ;AACJ,eC4BG;YACLZ,KAAKI,KAAiBmB,UAAAA;YACtBvB,KAAKM,IAAO,SAAUC,GAAKrB,GAAOsB;gBAChC,MAAMC,IAAUT,KAAKU,GAAQH,GAAKrB,GAAOsB;gBACzC,IAAIC,GAAST,KAAKgB,GAAuBP;gBACzC,OAAOT,KAAKoB;ADzBR;YC2BNpB,KAAKqB,IAAarB,KAAKsB;ADzBrB;QC2BJtB,KAAKa,IAAU,IAAIb,KAAKI;ADzBxB;IC8BQoB,EAAYf,GAAqCF;QACzD,IAAIkB,IAAUzB,KAAKa;QACnB,OAAOJ,GAAS;YACd,MAAMiB,IAAY1B,KAAKG,EAAKM,EAAQkB,GAAOpB;YAC3C,IAAImB,IAAY,GAAG;gBACjBjB,IAAUA,EAAQmB;ADzBd,mBC0BC,IAAIF,IAAY,GAAG;gBACxBD,IAAUhB;gBACVA,IAAUA,EAAQoB;ADxBd,mBCyBC,OAAOpB;ADtBZ;QCwBJ,OAAOgB;ADtBP;IC2BQK,EAAYrB,GAAqCF;QACzD,IAAIkB,IAAUzB,KAAKa;QACnB,OAAOJ,GAAS;YACd,MAAMiB,IAAY1B,KAAKG,EAAKM,EAAQkB,GAAOpB;YAC3C,IAAImB,KAAa,GAAG;gBAClBjB,IAAUA,EAAQmB;ADtBd,mBCuBC;gBACLH,IAAUhB;gBACVA,IAAUA,EAAQoB;ADrBd;AACJ;QCuBJ,OAAOJ;ADrBP;IC0BQM,EAAmBtB,GAAqCF;QAChE,IAAIkB,IAAUzB,KAAKa;QACnB,OAAOJ,GAAS;YACd,MAAMiB,IAAY1B,KAAKG,EAAKM,EAAQkB,GAAOpB;YAC3C,IAAImB,IAAY,GAAG;gBACjBD,IAAUhB;gBACVA,IAAUA,EAAQmB;ADrBd,mBCsBC,IAAIF,IAAY,GAAG;gBACxBjB,IAAUA,EAAQoB;ADpBd,mBCqBC,OAAOpB;ADlBZ;QCoBJ,OAAOgB;ADlBP;ICuBQO,GAAmBvB,GAAqCF;QAChE,IAAIkB,IAAUzB,KAAKa;QACnB,OAAOJ,GAAS;YACd,MAAMiB,IAAY1B,KAAKG,EAAKM,EAAQkB,GAAOpB;YAC3C,IAAImB,IAAY,GAAG;gBACjBD,IAAUhB;gBACVA,IAAUA,EAAQmB;ADlBd,mBCmBC;gBACLnB,IAAUA,EAAQoB;ADjBd;AACJ;QCmBJ,OAAOJ;ADjBP;ICsBQQ,GAAsBxB;QAC9B,OAAO,MAAM;YACX,MAAMQ,IAAaR,EAAQG;YAC3B,IAAIK,MAAejB,KAAKa,GAAS;YACjC,IAAIJ,EAAQyB,OAAM,GAAwB;gBACxCzB,EAAQyB,KAAM;gBACd;ADhBI;YCkBN,IAAIzB,MAAYQ,EAAWY,GAAO;gBAChC,MAAMM,IAAUlB,EAAWW;gBAC3B,IAAIO,EAAQD,OAAM,GAAwB;oBACxCC,EAAQD,KAAM;oBACdjB,EAAWiB,KAAM;oBACjB,IAAIjB,MAAejB,KAAKC,GAAO;wBAC7BD,KAAKC,IAAQgB,EAAWmB;ADhBhB,2BCiBHnB,EAAWmB;ADdZ,uBCeD;oBACL,IAAID,EAAQP,KAAUO,EAAQP,EAAOM,OAAM,GAAwB;wBACjEC,EAAQD,KAASjB,EAAWiB;wBAC5BjB,EAAWiB,KAAM;wBACjBC,EAAQP,EAAOM,KAAM;wBACrB,IAAIjB,MAAejB,KAAKC,GAAO;4BAC7BD,KAAKC,IAAQgB,EAAWmB;ADbd,+BCcLnB,EAAWmB;wBAClB;ADXQ,2BCYH,IAAID,EAAQN,KAASM,EAAQN,EAAMK,OAAM,GAAwB;wBACtEC,EAAQD,KAAM;wBACdC,EAAQN,EAAMK,KAAM;wBACpBC,EAAQE;ADVA,2BCWH;wBACLF,EAAQD,KAAM;wBACdzB,IAAUQ;ADTF;AACJ;AACJ,mBCUC;gBACL,MAAMkB,IAAUlB,EAAWY;gBAC3B,IAAIM,EAAQD,OAAM,GAAwB;oBACxCC,EAAQD,KAAM;oBACdjB,EAAWiB,KAAM;oBACjB,IAAIjB,MAAejB,KAAKC,GAAO;wBAC7BD,KAAKC,IAAQgB,EAAWoB;ADRhB,2BCSHpB,EAAWoB;ADNZ,uBCOD;oBACL,IAAIF,EAAQN,KAASM,EAAQN,EAAMK,OAAM,GAAwB;wBAC/DC,EAAQD,KAASjB,EAAWiB;wBAC5BjB,EAAWiB,KAAM;wBACjBC,EAAQN,EAAMK,KAAM;wBACpB,IAAIjB,MAAejB,KAAKC,GAAO;4BAC7BD,KAAKC,IAAQgB,EAAWoB;ADLd,+BCMLpB,EAAWoB;wBAClB;ADHQ,2BCIH,IAAIF,EAAQP,KAAUO,EAAQP,EAAOM,OAAM,GAAwB;wBACxEC,EAAQD,KAAM;wBACdC,EAAQP,EAAOM,KAAM;wBACrBC,EAAQC;ADFA,2BCGH;wBACLD,EAAQD,KAAM;wBACdzB,IAAUQ;ADDF;AACJ;AACJ;AACJ;AACJ;ICMQK,GAAcb;QACtB,IAAIT,KAAKoB,MAAY,GAAG;YACtBpB,KAAKsC;YACL,OAAOtC,KAAKa;ADDV;QCGJ,IAAI0B,IAAW9B;QACf,OAAO8B,EAASV,KAASU,EAASX,GAAQ;YACxC,IAAIW,EAASX,GAAQ;gBACnBW,IAAWA,EAASX;gBACpB,OAAOW,EAASV,GAAOU,IAAWA,EAASV;ADAvC,mBCCC;gBACLU,IAAWA,EAASV;ADChB;aCCLpB,EAAQkB,GAAMY,EAASZ,KAAQ,EAACY,EAASZ,GAAMlB,EAAQkB;aACvDlB,EAAQ+B,GAAQD,EAASC,KAAU,EAACD,EAASC,GAAQ/B,EAAQ+B;YAC9D/B,IAAU8B;ADCR;QCCJ,IAAIvC,KAAKa,EAAQgB,MAAUU,GAAU;YACnCvC,KAAKa,EAAQgB,IAAQU,EAAS3B;ADC5B,eCAG,IAAIZ,KAAKa,EAAQe,MAAWW,GAAU;YAC3CvC,KAAKa,EAAQe,IAASW,EAAS3B;ADE7B;QCAJZ,KAAKiC,GAAsBM;QAC3B,MAAM3B,IAAU2B,EAAS3B;QACzB,IAAI2B,MAAa3B,EAAQiB,GAAO;YAC9BjB,EAAQiB,IAAQ3B;ADEd,eCDGU,EAAQgB,IAAS1B;QACxBF,KAAKoB,KAAW;QAChBpB,KAAKC,EAAOiC,KAAM;QAClB,OAAOtB;ADIP;ICCQ6B,GACRhC,GACAiC;QAEA,IAAIjC,MAAYP,WAAW,OAAO;QAClC,MAAMyC,IAAW3C,KAAKyC,GAAkBhC,EAAQoB,GAAOa;QACvD,IAAIC,GAAU,OAAO;QACrB,IAAID,EAASjC,IAAU,OAAO;QAC9B,OAAOT,KAAKyC,GAAkBhC,EAAQmB,GAAQc;ADI9C;ICCQ1B,GAAuBP;QAC/B,OAAO,MAAM;YACX,MAAMQ,IAAaR,EAAQG;YAC3B,IAAIK,EAAWiB,OAAM,GAA0B;YAC/C,MAAMhB,IAAcD,EAAWL;YAC/B,IAAIK,MAAeC,EAAYW,GAAO;gBACpC,MAAMe,IAAQ1B,EAAYU;gBAC1B,IAAIgB,KAASA,EAAMV,OAAM,GAAwB;oBAC/CU,EAAMV,KAASjB,EAAWiB,KAAM;oBAChC,IAAIhB,MAAgBlB,KAAKC,GAAO;oBAChCiB,EAAYgB,KAAM;oBAClBzB,IAAUS;oBACV;ADMM,uBCLD,IAAIT,MAAYQ,EAAWW,GAAQ;oBACxCnB,EAAQyB,KAAM;oBACd,IAAIzB,EAAQoB,GAAOpB,EAAQoB,EAAMjB,KAAUK;oBAC3C,IAAIR,EAAQmB,GAAQnB,EAAQmB,EAAOhB,KAAUM;oBAC7CD,EAAWW,IAASnB,EAAQoB;oBAC5BX,EAAYW,IAAQpB,EAAQmB;oBAC5BnB,EAAQoB,IAAQZ;oBAChBR,EAAQmB,IAASV;oBACjB,IAAIA,MAAgBlB,KAAKC,GAAO;wBAC9BD,KAAKC,IAAQQ;wBACbT,KAAKa,EAAQD,KAAUH;ADSf,2BCRH;wBACL,MAAMoC,IAAK3B,EAAYN;wBACvB,IAAIiC,EAAGhB,MAAUX,GAAa;4BAC5B2B,EAAGhB,IAAQpB;ADUD,+BCTLoC,EAAGjB,IAASnB;ADYX;oBCVVA,EAAQG,KAAUM,EAAYN;oBAC9BK,EAAWL,KAAUH;oBACrBS,EAAYN,KAAUH;oBACtBS,EAAYgB,KAAM;oBAClB,OAAO;wBAAEjB;wBAAYC;wBAAaT;;ADY5B,uBCXD;oBACLQ,EAAWiB,KAAM;oBACjB,IAAIhB,MAAgBlB,KAAKC,GAAO;wBAC9BD,KAAKC,IAAQiB,EAAYmB;ADajB,2BCZHnB,EAAYmB;oBACnBnB,EAAYgB,KAAM;ADeZ;AACJ,mBCdC;gBACL,MAAMU,IAAQ1B,EAAYW;gBAC1B,IAAIe,KAASA,EAAMV,OAAM,GAAwB;oBAC/CU,EAAMV,KAASjB,EAAWiB,KAAM;oBAChC,IAAIhB,MAAgBlB,KAAKC,GAAO;oBAChCiB,EAAYgB,KAAM;oBAClBzB,IAAUS;oBACV;ADiBM,uBChBD,IAAIT,MAAYQ,EAAWY,GAAO;oBACvCpB,EAAQyB,KAAM;oBACd,IAAIzB,EAAQoB,GAAOpB,EAAQoB,EAAMjB,KAAUM;oBAC3C,IAAIT,EAAQmB,GAAQnB,EAAQmB,EAAOhB,KAAUK;oBAC7CC,EAAYU,IAASnB,EAAQoB;oBAC7BZ,EAAWY,IAAQpB,EAAQmB;oBAC3BnB,EAAQoB,IAAQX;oBAChBT,EAAQmB,IAASX;oBACjB,IAAIC,MAAgBlB,KAAKC,GAAO;wBAC9BD,KAAKC,IAAQQ;wBACbT,KAAKa,EAAQD,KAAUH;ADoBf,2BCnBH;wBACL,MAAMoC,IAAK3B,EAAYN;wBACvB,IAAIiC,EAAGhB,MAAUX,GAAa;4BAC5B2B,EAAGhB,IAAQpB;ADqBD,+BCpBLoC,EAAGjB,IAASnB;ADuBX;oBCrBVA,EAAQG,KAAUM,EAAYN;oBAC9BK,EAAWL,KAAUH;oBACrBS,EAAYN,KAAUH;oBACtBS,EAAYgB,KAAM;oBAClB,OAAO;wBAAEjB;wBAAYC;wBAAaT;;ADuB5B,uBCtBD;oBACLQ,EAAWiB,KAAM;oBACjB,IAAIhB,MAAgBlB,KAAKC,GAAO;wBAC9BD,KAAKC,IAAQiB,EAAYkB;ADwBjB,2BCvBHlB,EAAYkB;oBACnBlB,EAAYgB,KAAM;AD0BZ;AACJ;YCxBN;AD0BE;AACJ;ICrBQxB,GAAQH,GAAQrB,GAAWsB;QACnC,IAAIR,KAAKC,MAAUC,WAAW;YAC5BF,KAAKoB,KAAW;YAChBpB,KAAKC,IAAQ,IAAID,KAAKI,GAAeG,GAAKrB;YAC1Cc,KAAKC,EAAMiC,KAAM;YACjBlC,KAAKC,EAAMW,KAAUZ,KAAKa;YAC1Bb,KAAKa,EAAQD,KAAUZ,KAAKC;YAC5BD,KAAKa,EAAQgB,IAAQ7B,KAAKC;YAC1BD,KAAKa,EAAQe,IAAS5B,KAAKC;YAC3B;AD0BE;QCxBJ,IAAIQ;QACJ,MAAMqC,IAAU9C,KAAKa,EAAQgB;QAC7B,MAAMkB,IAAe/C,KAAKG,EAAK2C,EAAQnB,GAAOpB;QAC9C,IAAIwC,MAAiB,GAAG;YACtBD,EAAQN,IAAStD;YACjB;AD0BE,eCzBG,IAAI6D,IAAe,GAAG;YAC3BD,EAAQjB,IAAQ,IAAI7B,KAAKI,GAAeG,GAAKrB;YAC7C4D,EAAQjB,EAAMjB,KAAUkC;YACxBrC,IAAUqC,EAAQjB;YAClB7B,KAAKa,EAAQgB,IAAQpB;AD2BnB,eC1BG;YACL,MAAMuC,IAAUhD,KAAKa,EAAQe;YAC7B,MAAMqB,IAAejD,KAAKG,EAAK6C,EAAQrB,GAAOpB;YAC9C,IAAI0C,MAAiB,GAAG;gBACtBD,EAAQR,IAAStD;gBACjB;AD4BI,mBC3BC,IAAI+D,IAAe,GAAG;gBAC3BD,EAAQpB,IAAS,IAAI5B,KAAKI,GAAeG,GAAKrB;gBAC9C8D,EAAQpB,EAAOhB,KAAUoC;gBACzBvC,IAAUuC,EAAQpB;gBAClB5B,KAAKa,EAAQe,IAASnB;AD6BlB,mBC5BC;gBACL,IAAID,MAASN,WAAW;oBACtB,MAAMgD,IAAW1C,EAAK2C;oBACtB,IAAID,MAAalD,KAAKa,GAAS;wBAC7B,MAAMuC,IAAapD,KAAKG,EAAK+C,EAASvB,GAAOpB;wBAC7C,IAAI6C,MAAe,GAAG;4BACpBF,EAASV,IAAStD;4BAClB;AD8BU,+BC7BsB,IAAIkE,IAAa,GAAG;4BACpD,MAAMC,IAAUH,EAASI;4BACzB,MAAMC,IAAYvD,KAAKG,EAAKkD,EAAQ1B,GAAOpB;4BAC3C,IAAIgD,MAAc,GAAG;gCACnBF,EAAQb,IAAStD;gCACjB;AD+BY,mCC9BP,IAAIqE,IAAY,GAAG;gCACxB9C,IAAU,IAAIT,KAAKI,GAAeG,GAAKrB;gCACvC,IAAImE,EAAQzB,MAAW1B,WAAW;oCAChCmD,EAAQzB,IAASnB;oCACjBA,EAAQG,KAAUyC;ADgCJ,uCC/BT;oCACLH,EAASrB,IAAQpB;oCACjBA,EAAQG,KAAUsC;ADiCJ;AACJ;AACJ;AACJ;AACJ;gBC/BR,IAAIzC,MAAYP,WAAW;oBACzBO,IAAUT,KAAKC;oBACf,OAAO,MAAM;wBACX,MAAMyB,IAAY1B,KAAKG,EAAKM,EAAQkB,GAAOpB;wBAC3C,IAAImB,IAAY,GAAG;4BACjB,IAAIjB,EAAQoB,MAAU3B,WAAW;gCAC/BO,EAAQoB,IAAQ,IAAI7B,KAAKI,GAAeG,GAAKrB;gCAC7CuB,EAAQoB,EAAMjB,KAAUH;gCACxBA,IAAUA,EAAQoB;gCAClB;ADiCY;4BC/BdpB,IAAUA,EAAQoB;ADiCR,+BChCL,IAAIH,IAAY,GAAG;4BACxB,IAAIjB,EAAQmB,MAAW1B,WAAW;gCAChCO,EAAQmB,IAAS,IAAI5B,KAAKI,GAAeG,GAAKrB;gCAC9CuB,EAAQmB,EAAOhB,KAAUH;gCACzBA,IAAUA,EAAQmB;gCAClB;ADkCY;4BChCdnB,IAAUA,EAAQmB;ADkCR,+BCjCL;4BACLnB,EAAQ+B,IAAStD;4BACjB;ADmCU;AACJ;AACJ;AACJ;AACJ;QCjCJc,KAAKoB,KAAW;QAChB,OAAOX;ADmCP;IC9BQ+C,EAAiB/C,GAAqCF;QAC9D,OAAOE,GAAS;YACd,MAAMiB,IAAY1B,KAAKG,EAAKM,EAAQkB,GAAOpB;YAC3C,IAAImB,IAAY,GAAG;gBACjBjB,IAAUA,EAAQmB;ADmCd,mBClCC,IAAIF,IAAY,GAAG;gBACxBjB,IAAUA,EAAQoB;ADoCd,mBCnCC,OAAOpB;ADsCZ;QCpCJ,OAAOA,KAAWT,KAAKa;ADsCvB;ICpCFyB;QACEtC,KAAKoB,IAAU;QACfpB,KAAKC,IAAQC;QACbF,KAAKa,EAAQD,KAAUV;QACvBF,KAAKa,EAAQgB,IAAQ7B,KAAKa,EAAQe,IAAS1B;ADsC3C;IC1BFuD,oBAAoBC,GAA0BnD;QAC5C,MAAMoD,IAAOD,EAAKP;QAClB,IAAIQ,MAAS3D,KAAKa,GAAS;aACzB,GAAA+C,YAAAA;ADsCE;QCpCJ,IAAI5D,KAAKoB,MAAY,GAAG;YACtBuC,EAAKhC,IAAOpB;YACZ,OAAO;ADsCL;QCpCJ,IAAIoD,MAAS3D,KAAKa,EAAQgB,GAAO;YAC/B,IAAI7B,KAAKG,EAAKwD,EAAKE,IAAQlC,GAAOpB,KAAO,GAAG;gBAC1CoD,EAAKhC,IAAOpB;gBACZ,OAAO;ADsCH;YCpCN,OAAO;ADsCL;QCpCJ,IAAIoD,MAAS3D,KAAKa,EAAQe,GAAQ;YAChC,IAAI5B,KAAKG,EAAKwD,EAAKL,IAAO3B,GAAOpB,KAAO,GAAG;gBACzCoD,EAAKhC,IAAOpB;gBACZ,OAAO;ADsCH;YCpCN,OAAO;ADsCL;QCpCJ,MAAMuD,IAASH,EAAKL,IAAO3B;QAC3B,IAAI3B,KAAKG,EAAK2D,GAAQvD,MAAQ,GAAG,OAAO;QACxC,MAAMwD,IAAUJ,EAAKE,IAAQlC;QAC7B,IAAI3B,KAAKG,EAAK4D,GAASxD,MAAQ,GAAG,OAAO;QACzCoD,EAAKhC,IAAOpB;QACZ,OAAO;ADwCP;ICtCFyD,kBAAkBC;QDwCZ,ICvCsBA,IAAG,KAAHA,IAAQjE,KAAKoB,IAAO,GAnfd;YAAE,MAAU,IAAI8C;AD4hB5C;QCxCJ,IAAIC,IAAQ;QACZ,MAAMC,IAAOpE;QACbA,KAAKyC,GACHzC,KAAKC,IACL,SAAUQ;YACR,IAAIwD,MAAQE,GAAO;gBACjBC,EAAK/C,EAAWZ;gBAChB,OAAO;ADwCL;YCtCJ0D,KAAS;YACT,OAAO;ADwCP;QCtCJ,OAAOnE,KAAKoB;ADwCZ;ICjCFiD,kBAAkB9D;QAChB,IAAIP,KAAKoB,MAAY,GAAG,OAAO;QAC/B,MAAMX,IAAUT,KAAKwD,EAAiBxD,KAAKC,GAAOM;QAClD,IAAIE,MAAYT,KAAKa,GAAS,OAAO;QACrCb,KAAKqB,EAAWZ;QAChB,OAAO;AD0CP;ICxCF6D,uBAAuBZ;QACrB,MAAMC,IAAOD,EAAKP;QAClB,IAAIQ,MAAS3D,KAAKa,GAAS;aACzB,GAAA+C,YAAAA;AD0CE;QCxCJ,MAAMW,IAAaZ,EAAK/B,MAAW1B;QACnC,MAAMsE,IAAWd,EAAKe,iBAAY;QAElC,IAAID,GAAU;YAEZ,IAAID,GAAYb,EAAKgB;AD2CnB,eC1CG;YAGL,KAAKH,KAAcZ,EAAK9B,MAAU3B,WAAWwD,EAAKgB;AD6ChD;QC3CJ1E,KAAKqB,EAAWsC;QAChB,OAAOD;AD6CP;IC3CFiB,QAAQjC;QACN,IAAIyB,IAAQ;QACZ,KAAK,MAAMS,KAAW5E,MAAM0C,EAASkC,GAAST,KAASnE;AD8CvD;IC5CF6E,gBAAgBZ;QD8CV,IC7CsBA,IAAG,KAAHA,IAAQjE,KAAKoB,IAAO,GAtiBd;YAAE,MAAU,IAAI8C;ADqlB5C;QC9CJ,IAAIY;QACJ,IAAIX,IAAQ;QACZ,KAAK,MAAMS,KAAW5E,MAAM;YAC1B,IAAImE,MAAUF,GAAK;gBACjBa,IAAMF;gBACN;ADgDI;YC9CNT,KAAS;ADgDP;QC9CJ,OAAmBW;ADgDnB;IC1CFC;QACE,IAAI/E,KAAKoB,MAAY,GAAG,OAAO;QAC/B,MAAM4D,YACJ,SAAUvE;YACR,KAAKA,GAAS,OAAO;YACrB,OAAOwE,KAAKC,IAAIF,UAAUvE,EAAQoB,IAAQmD,UAAUvE,EAAQmB,MAAW;ADiDvE;QC/CJ,OAAOoD,UAAUhF,KAAKC;ADiDtB;;;ACrBH,IAAAkF,WAEc3F;;AAAaP,QAAAE,UAAAgG", "file": "index.js", "sourcesContent": ["import { TreeNode, TreeNodeEnableIndex } from './TreeNode';\nimport { Container } from \"../../ContainerBase\";\nimport $checkWithinAccessParams from \"../../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../../utils/throwError\";\nclass TreeContainer extends Container {\n    /**\n     * @internal\n     */\n    constructor(cmp = function (x, y) {\n        if (x < y)\n            return -1;\n        if (x > y)\n            return 1;\n        return 0;\n    }, enableIndex = false) {\n        super();\n        /**\n         * @internal\n         */\n        this._root = undefined;\n        this._cmp = cmp;\n        if (enableIndex) {\n            this._TreeNodeClass = TreeNodeEnableIndex;\n            this._set = function (key, value, hint) {\n                const curNode = this._preSet(key, value, hint);\n                if (curNode) {\n                    let p = curNode._parent;\n                    while (p !== this._header) {\n                        p._subTreeSize += 1;\n                        p = p._parent;\n                    }\n                    const nodeList = this._insertNodeSelfBalance(curNode);\n                    if (nodeList) {\n                        const { parentNode, grandParent, curNode } = nodeList;\n                        parentNode._recount();\n                        grandParent._recount();\n                        curNode._recount();\n                    }\n                }\n                return this._length;\n            };\n            this._eraseNode = function (curNode) {\n                let p = this._preEraseNode(curNode);\n                while (p !== this._header) {\n                    p._subTreeSize -= 1;\n                    p = p._parent;\n                }\n            };\n        }\n        else {\n            this._TreeNodeClass = TreeNode;\n            this._set = function (key, value, hint) {\n                const curNode = this._preSet(key, value, hint);\n                if (curNode)\n                    this._insertNodeSelfBalance(curNode);\n                return this._length;\n            };\n            this._eraseNode = this._preEraseNode;\n        }\n        this._header = new this._TreeNodeClass();\n    }\n    /**\n     * @internal\n     */\n    _lowerBound(curNode, key) {\n        let resNode = this._header;\n        while (curNode) {\n            const cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult < 0) {\n                curNode = curNode._right;\n            }\n            else if (cmpResult > 0) {\n                resNode = curNode;\n                curNode = curNode._left;\n            }\n            else\n                return curNode;\n        }\n        return resNode;\n    }\n    /**\n     * @internal\n     */\n    _upperBound(curNode, key) {\n        let resNode = this._header;\n        while (curNode) {\n            const cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult <= 0) {\n                curNode = curNode._right;\n            }\n            else {\n                resNode = curNode;\n                curNode = curNode._left;\n            }\n        }\n        return resNode;\n    }\n    /**\n     * @internal\n     */\n    _reverseLowerBound(curNode, key) {\n        let resNode = this._header;\n        while (curNode) {\n            const cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult < 0) {\n                resNode = curNode;\n                curNode = curNode._right;\n            }\n            else if (cmpResult > 0) {\n                curNode = curNode._left;\n            }\n            else\n                return curNode;\n        }\n        return resNode;\n    }\n    /**\n     * @internal\n     */\n    _reverseUpperBound(curNode, key) {\n        let resNode = this._header;\n        while (curNode) {\n            const cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult < 0) {\n                resNode = curNode;\n                curNode = curNode._right;\n            }\n            else {\n                curNode = curNode._left;\n            }\n        }\n        return resNode;\n    }\n    /**\n     * @internal\n     */\n    _eraseNodeSelfBalance(curNode) {\n        while (true) {\n            const parentNode = curNode._parent;\n            if (parentNode === this._header)\n                return;\n            if (curNode._color === 1 /* TreeNodeColor.RED */) {\n                curNode._color = 0 /* TreeNodeColor.BLACK */;\n                return;\n            }\n            if (curNode === parentNode._left) {\n                const brother = parentNode._right;\n                if (brother._color === 1 /* TreeNodeColor.RED */) {\n                    brother._color = 0 /* TreeNodeColor.BLACK */;\n                    parentNode._color = 1 /* TreeNodeColor.RED */;\n                    if (parentNode === this._root) {\n                        this._root = parentNode._rotateLeft();\n                    }\n                    else\n                        parentNode._rotateLeft();\n                }\n                else {\n                    if (brother._right && brother._right._color === 1 /* TreeNodeColor.RED */) {\n                        brother._color = parentNode._color;\n                        parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                        brother._right._color = 0 /* TreeNodeColor.BLACK */;\n                        if (parentNode === this._root) {\n                            this._root = parentNode._rotateLeft();\n                        }\n                        else\n                            parentNode._rotateLeft();\n                        return;\n                    }\n                    else if (brother._left && brother._left._color === 1 /* TreeNodeColor.RED */) {\n                        brother._color = 1 /* TreeNodeColor.RED */;\n                        brother._left._color = 0 /* TreeNodeColor.BLACK */;\n                        brother._rotateRight();\n                    }\n                    else {\n                        brother._color = 1 /* TreeNodeColor.RED */;\n                        curNode = parentNode;\n                    }\n                }\n            }\n            else {\n                const brother = parentNode._left;\n                if (brother._color === 1 /* TreeNodeColor.RED */) {\n                    brother._color = 0 /* TreeNodeColor.BLACK */;\n                    parentNode._color = 1 /* TreeNodeColor.RED */;\n                    if (parentNode === this._root) {\n                        this._root = parentNode._rotateRight();\n                    }\n                    else\n                        parentNode._rotateRight();\n                }\n                else {\n                    if (brother._left && brother._left._color === 1 /* TreeNodeColor.RED */) {\n                        brother._color = parentNode._color;\n                        parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                        brother._left._color = 0 /* TreeNodeColor.BLACK */;\n                        if (parentNode === this._root) {\n                            this._root = parentNode._rotateRight();\n                        }\n                        else\n                            parentNode._rotateRight();\n                        return;\n                    }\n                    else if (brother._right && brother._right._color === 1 /* TreeNodeColor.RED */) {\n                        brother._color = 1 /* TreeNodeColor.RED */;\n                        brother._right._color = 0 /* TreeNodeColor.BLACK */;\n                        brother._rotateLeft();\n                    }\n                    else {\n                        brother._color = 1 /* TreeNodeColor.RED */;\n                        curNode = parentNode;\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * @internal\n     */\n    _preEraseNode(curNode) {\n        if (this._length === 1) {\n            this.clear();\n            return this._header;\n        }\n        let swapNode = curNode;\n        while (swapNode._left || swapNode._right) {\n            if (swapNode._right) {\n                swapNode = swapNode._right;\n                while (swapNode._left)\n                    swapNode = swapNode._left;\n            }\n            else {\n                swapNode = swapNode._left;\n            }\n            [curNode._key, swapNode._key] = [swapNode._key, curNode._key];\n            [curNode._value, swapNode._value] = [swapNode._value, curNode._value];\n            curNode = swapNode;\n        }\n        if (this._header._left === swapNode) {\n            this._header._left = swapNode._parent;\n        }\n        else if (this._header._right === swapNode) {\n            this._header._right = swapNode._parent;\n        }\n        this._eraseNodeSelfBalance(swapNode);\n        const _parent = swapNode._parent;\n        if (swapNode === _parent._left) {\n            _parent._left = undefined;\n        }\n        else\n            _parent._right = undefined;\n        this._length -= 1;\n        this._root._color = 0 /* TreeNodeColor.BLACK */;\n        return _parent;\n    }\n    /**\n     * @internal\n     */\n    _inOrderTraversal(curNode, callback) {\n        if (curNode === undefined)\n            return false;\n        const ifReturn = this._inOrderTraversal(curNode._left, callback);\n        if (ifReturn)\n            return true;\n        if (callback(curNode))\n            return true;\n        return this._inOrderTraversal(curNode._right, callback);\n    }\n    /**\n     * @internal\n     */\n    _insertNodeSelfBalance(curNode) {\n        while (true) {\n            const parentNode = curNode._parent;\n            if (parentNode._color === 0 /* TreeNodeColor.BLACK */)\n                return;\n            const grandParent = parentNode._parent;\n            if (parentNode === grandParent._left) {\n                const uncle = grandParent._right;\n                if (uncle && uncle._color === 1 /* TreeNodeColor.RED */) {\n                    uncle._color = parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (grandParent === this._root)\n                        return;\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                    curNode = grandParent;\n                    continue;\n                }\n                else if (curNode === parentNode._right) {\n                    curNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (curNode._left)\n                        curNode._left._parent = parentNode;\n                    if (curNode._right)\n                        curNode._right._parent = grandParent;\n                    parentNode._right = curNode._left;\n                    grandParent._left = curNode._right;\n                    curNode._left = parentNode;\n                    curNode._right = grandParent;\n                    if (grandParent === this._root) {\n                        this._root = curNode;\n                        this._header._parent = curNode;\n                    }\n                    else {\n                        const GP = grandParent._parent;\n                        if (GP._left === grandParent) {\n                            GP._left = curNode;\n                        }\n                        else\n                            GP._right = curNode;\n                    }\n                    curNode._parent = grandParent._parent;\n                    parentNode._parent = curNode;\n                    grandParent._parent = curNode;\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                    return { parentNode, grandParent, curNode };\n                }\n                else {\n                    parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (grandParent === this._root) {\n                        this._root = grandParent._rotateRight();\n                    }\n                    else\n                        grandParent._rotateRight();\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                }\n            }\n            else {\n                const uncle = grandParent._left;\n                if (uncle && uncle._color === 1 /* TreeNodeColor.RED */) {\n                    uncle._color = parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (grandParent === this._root)\n                        return;\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                    curNode = grandParent;\n                    continue;\n                }\n                else if (curNode === parentNode._left) {\n                    curNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (curNode._left)\n                        curNode._left._parent = grandParent;\n                    if (curNode._right)\n                        curNode._right._parent = parentNode;\n                    grandParent._right = curNode._left;\n                    parentNode._left = curNode._right;\n                    curNode._left = grandParent;\n                    curNode._right = parentNode;\n                    if (grandParent === this._root) {\n                        this._root = curNode;\n                        this._header._parent = curNode;\n                    }\n                    else {\n                        const GP = grandParent._parent;\n                        if (GP._left === grandParent) {\n                            GP._left = curNode;\n                        }\n                        else\n                            GP._right = curNode;\n                    }\n                    curNode._parent = grandParent._parent;\n                    parentNode._parent = curNode;\n                    grandParent._parent = curNode;\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                    return { parentNode, grandParent, curNode };\n                }\n                else {\n                    parentNode._color = 0 /* TreeNodeColor.BLACK */;\n                    if (grandParent === this._root) {\n                        this._root = grandParent._rotateLeft();\n                    }\n                    else\n                        grandParent._rotateLeft();\n                    grandParent._color = 1 /* TreeNodeColor.RED */;\n                }\n            }\n            return;\n        }\n    }\n    /**\n     * @internal\n     */\n    _preSet(key, value, hint) {\n        if (this._root === undefined) {\n            this._length += 1;\n            this._root = new this._TreeNodeClass(key, value);\n            this._root._color = 0 /* TreeNodeColor.BLACK */;\n            this._root._parent = this._header;\n            this._header._parent = this._root;\n            this._header._left = this._root;\n            this._header._right = this._root;\n            return;\n        }\n        let curNode;\n        const minNode = this._header._left;\n        const compareToMin = this._cmp(minNode._key, key);\n        if (compareToMin === 0) {\n            minNode._value = value;\n            return;\n        }\n        else if (compareToMin > 0) {\n            minNode._left = new this._TreeNodeClass(key, value);\n            minNode._left._parent = minNode;\n            curNode = minNode._left;\n            this._header._left = curNode;\n        }\n        else {\n            const maxNode = this._header._right;\n            const compareToMax = this._cmp(maxNode._key, key);\n            if (compareToMax === 0) {\n                maxNode._value = value;\n                return;\n            }\n            else if (compareToMax < 0) {\n                maxNode._right = new this._TreeNodeClass(key, value);\n                maxNode._right._parent = maxNode;\n                curNode = maxNode._right;\n                this._header._right = curNode;\n            }\n            else {\n                if (hint !== undefined) {\n                    const iterNode = hint._node;\n                    if (iterNode !== this._header) {\n                        const iterCmpRes = this._cmp(iterNode._key, key);\n                        if (iterCmpRes === 0) {\n                            iterNode._value = value;\n                            return;\n                        }\n                        else /* istanbul ignore else */ if (iterCmpRes > 0) {\n                            const preNode = iterNode._pre();\n                            const preCmpRes = this._cmp(preNode._key, key);\n                            if (preCmpRes === 0) {\n                                preNode._value = value;\n                                return;\n                            }\n                            else if (preCmpRes < 0) {\n                                curNode = new this._TreeNodeClass(key, value);\n                                if (preNode._right === undefined) {\n                                    preNode._right = curNode;\n                                    curNode._parent = preNode;\n                                }\n                                else {\n                                    iterNode._left = curNode;\n                                    curNode._parent = iterNode;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (curNode === undefined) {\n                    curNode = this._root;\n                    while (true) {\n                        const cmpResult = this._cmp(curNode._key, key);\n                        if (cmpResult > 0) {\n                            if (curNode._left === undefined) {\n                                curNode._left = new this._TreeNodeClass(key, value);\n                                curNode._left._parent = curNode;\n                                curNode = curNode._left;\n                                break;\n                            }\n                            curNode = curNode._left;\n                        }\n                        else if (cmpResult < 0) {\n                            if (curNode._right === undefined) {\n                                curNode._right = new this._TreeNodeClass(key, value);\n                                curNode._right._parent = curNode;\n                                curNode = curNode._right;\n                                break;\n                            }\n                            curNode = curNode._right;\n                        }\n                        else {\n                            curNode._value = value;\n                            return;\n                        }\n                    }\n                }\n            }\n        }\n        this._length += 1;\n        return curNode;\n    }\n    /**\n     * @internal\n     */\n    _findElementNode(curNode, key) {\n        while (curNode) {\n            const cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult < 0) {\n                curNode = curNode._right;\n            }\n            else if (cmpResult > 0) {\n                curNode = curNode._left;\n            }\n            else\n                return curNode;\n        }\n        return curNode || this._header;\n    }\n    clear() {\n        this._length = 0;\n        this._root = undefined;\n        this._header._parent = undefined;\n        this._header._left = this._header._right = undefined;\n    }\n    /**\n     * @description Update node's key by iterator.\n     * @param iter - The iterator you want to change.\n     * @param key - The key you want to update.\n     * @returns Whether the modification is successful.\n     * @example\n     * const st = new orderedSet([1, 2, 5]);\n     * const iter = st.find(2);\n     * st.updateKeyByIterator(iter, 3); // then st will become [1, 3, 5]\n     */\n    updateKeyByIterator(iter, key) {\n        const node = iter._node;\n        if (node === this._header) {\n            throwIteratorAccessError();\n        }\n        if (this._length === 1) {\n            node._key = key;\n            return true;\n        }\n        if (node === this._header._left) {\n            if (this._cmp(node._next()._key, key) > 0) {\n                node._key = key;\n                return true;\n            }\n            return false;\n        }\n        if (node === this._header._right) {\n            if (this._cmp(node._pre()._key, key) < 0) {\n                node._key = key;\n                return true;\n            }\n            return false;\n        }\n        const preKey = node._pre()._key;\n        if (this._cmp(preKey, key) >= 0)\n            return false;\n        const nextKey = node._next()._key;\n        if (this._cmp(nextKey, key) <= 0)\n            return false;\n        node._key = key;\n        return true;\n    }\n    eraseElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        let index = 0;\n        const self = this;\n        this._inOrderTraversal(this._root, function (curNode) {\n            if (pos === index) {\n                self._eraseNode(curNode);\n                return true;\n            }\n            index += 1;\n            return false;\n        });\n        return this._length;\n    }\n    /**\n     * @description Remove the element of the specified key.\n     * @param key - The key you want to remove.\n     * @returns Whether erase successfully.\n     */\n    eraseElementByKey(key) {\n        if (this._length === 0)\n            return false;\n        const curNode = this._findElementNode(this._root, key);\n        if (curNode === this._header)\n            return false;\n        this._eraseNode(curNode);\n        return true;\n    }\n    eraseElementByIterator(iter) {\n        const node = iter._node;\n        if (node === this._header) {\n            throwIteratorAccessError();\n        }\n        const hasNoRight = node._right === undefined;\n        const isNormal = iter.iteratorType === 0 /* IteratorType.NORMAL */;\n        // For the normal iterator, the `next` node will be swapped to `this` node when has right.\n        if (isNormal) {\n            // So we should move it to next when it's right is null.\n            if (hasNoRight)\n                iter.next();\n        }\n        else {\n            // For the reverse iterator, only when it doesn't have right and has left the `next` node will be swapped.\n            // So when it has right, or it is a leaf node we should move it to `next`.\n            if (!hasNoRight || node._left === undefined)\n                iter.next();\n        }\n        this._eraseNode(node);\n        return iter;\n    }\n    forEach(callback) {\n        let index = 0;\n        for (const element of this)\n            callback(element, index++, this);\n    }\n    getElementByPos(pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        let res;\n        let index = 0;\n        for (const element of this) {\n            if (index === pos) {\n                res = element;\n                break;\n            }\n            index += 1;\n        }\n        return res;\n    }\n    /**\n     * @description Get the height of the tree.\n     * @returns Number about the height of the RB-tree.\n     */\n    getHeight() {\n        if (this._length === 0)\n            return 0;\n        const traversal = function (curNode) {\n            if (!curNode)\n                return 0;\n            return Math.max(traversal(curNode._left), traversal(curNode._right)) + 1;\n        };\n        return traversal(this._root);\n    }\n}\nexport default TreeContainer;\n", "import type TreeIterator from './TreeIterator';\nimport { TreeNode, TreeNodeColor, TreeNodeEnableIndex } from './TreeNode';\nimport { Container, IteratorType } from '@/container/ContainerBase';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nabstract class TreeContainer<K, V> extends Container<K | [K, V]> {\n  /**\n   * @internal\n   */\n  protected _root: TreeNode<K, V> | undefined = undefined;\n  /**\n   * @internal\n   */\n  protected _header: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected readonly _cmp: (x: K, y: K) => number;\n  /**\n   * @internal\n   */\n  protected readonly _TreeNodeClass: typeof TreeNode | typeof TreeNodeEnableIndex;\n  /**\n   * @internal\n   */\n  protected readonly _eraseNode: (curNode: TreeNode<K, V>) => void;\n  /**\n   * @internal\n   */\n  protected _set: (key: K, value: V, hint?: TreeIterator<K, V>) => number;\n  /**\n   * @internal\n   */\n  protected constructor(\n    cmp: (x: K, y: K) => number =\n    function (x: K, y: K) {\n      if (x < y) return -1;\n      if (x > y) return 1;\n      return 0;\n    },\n    enableIndex = false\n  ) {\n    super();\n    this._cmp = cmp;\n    if (enableIndex) {\n      this._TreeNodeClass = TreeNodeEnableIndex;\n      this._set = function (key, value, hint) {\n        const curNode = this._preSet(key, value, hint);\n        if (curNode) {\n          let p = curNode._parent as TreeNodeEnableIndex<K, V>;\n          while (p !== this._header) {\n            p._subTreeSize += 1;\n            p = p._parent as TreeNodeEnableIndex<K, V>;\n          }\n          const nodeList = this._insertNodeSelfBalance(curNode);\n          if (nodeList) {\n            const {\n              parentNode,\n              grandParent,\n              curNode\n            } = nodeList as unknown as Record<string, TreeNodeEnableIndex<K, V>>;\n            parentNode._recount();\n            grandParent._recount();\n            curNode._recount();\n          }\n        }\n        return this._length;\n      };\n      this._eraseNode = function (curNode) {\n        let p = this._preEraseNode(curNode) as TreeNodeEnableIndex<K, V>;\n        while (p !== this._header) {\n          p._subTreeSize -= 1;\n          p = p._parent as TreeNodeEnableIndex<K, V>;\n        }\n      };\n    } else {\n      this._TreeNodeClass = TreeNode;\n      this._set = function (key, value, hint) {\n        const curNode = this._preSet(key, value, hint);\n        if (curNode) this._insertNodeSelfBalance(curNode);\n        return this._length;\n      };\n      this._eraseNode = this._preEraseNode;\n    }\n    this._header = new this._TreeNodeClass();\n  }\n  /**\n   * @internal\n   */\n  protected _lowerBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        resNode = curNode;\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _upperBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult <= 0) {\n        curNode = curNode._right;\n      } else {\n        resNode = curNode;\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _reverseLowerBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _reverseUpperBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else {\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _eraseNodeSelfBalance(curNode: TreeNode<K, V>) {\n    while (true) {\n      const parentNode = curNode._parent!;\n      if (parentNode === this._header) return;\n      if (curNode._color === TreeNodeColor.RED) {\n        curNode._color = TreeNodeColor.BLACK;\n        return;\n      }\n      if (curNode === parentNode._left) {\n        const brother = parentNode._right!;\n        if (brother._color === TreeNodeColor.RED) {\n          brother._color = TreeNodeColor.BLACK;\n          parentNode._color = TreeNodeColor.RED;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateLeft();\n          } else parentNode._rotateLeft();\n        } else {\n          if (brother._right && brother._right._color === TreeNodeColor.RED) {\n            brother._color = parentNode._color;\n            parentNode._color = TreeNodeColor.BLACK;\n            brother._right._color = TreeNodeColor.BLACK;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateLeft();\n            } else parentNode._rotateLeft();\n            return;\n          } else if (brother._left && brother._left._color === TreeNodeColor.RED) {\n            brother._color = TreeNodeColor.RED;\n            brother._left._color = TreeNodeColor.BLACK;\n            brother._rotateRight();\n          } else {\n            brother._color = TreeNodeColor.RED;\n            curNode = parentNode;\n          }\n        }\n      } else {\n        const brother = parentNode._left!;\n        if (brother._color === TreeNodeColor.RED) {\n          brother._color = TreeNodeColor.BLACK;\n          parentNode._color = TreeNodeColor.RED;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateRight();\n          } else parentNode._rotateRight();\n        } else {\n          if (brother._left && brother._left._color === TreeNodeColor.RED) {\n            brother._color = parentNode._color;\n            parentNode._color = TreeNodeColor.BLACK;\n            brother._left._color = TreeNodeColor.BLACK;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateRight();\n            } else parentNode._rotateRight();\n            return;\n          } else if (brother._right && brother._right._color === TreeNodeColor.RED) {\n            brother._color = TreeNodeColor.RED;\n            brother._right._color = TreeNodeColor.BLACK;\n            brother._rotateLeft();\n          } else {\n            brother._color = TreeNodeColor.RED;\n            curNode = parentNode;\n          }\n        }\n      }\n    }\n  }\n  /**\n   * @internal\n   */\n  protected _preEraseNode(curNode: TreeNode<K, V>) {\n    if (this._length === 1) {\n      this.clear();\n      return this._header;\n    }\n    let swapNode = curNode;\n    while (swapNode._left || swapNode._right) {\n      if (swapNode._right) {\n        swapNode = swapNode._right;\n        while (swapNode._left) swapNode = swapNode._left;\n      } else {\n        swapNode = swapNode._left!;\n      }\n      [curNode._key, swapNode._key] = [swapNode._key, curNode._key];\n      [curNode._value, swapNode._value] = [swapNode._value, curNode._value];\n      curNode = swapNode;\n    }\n    if (this._header._left === swapNode) {\n      this._header._left = swapNode._parent;\n    } else if (this._header._right === swapNode) {\n      this._header._right = swapNode._parent;\n    }\n    this._eraseNodeSelfBalance(swapNode);\n    const _parent = swapNode._parent!;\n    if (swapNode === _parent._left) {\n      _parent._left = undefined;\n    } else _parent._right = undefined;\n    this._length -= 1;\n    this._root!._color = TreeNodeColor.BLACK;\n    return _parent;\n  }\n  /**\n   * @internal\n   */\n  protected _inOrderTraversal(\n    curNode: TreeNode<K, V> | undefined,\n    callback: (curNode: TreeNode<K, V>) => boolean\n  ): boolean {\n    if (curNode === undefined) return false;\n    const ifReturn = this._inOrderTraversal(curNode._left, callback);\n    if (ifReturn) return true;\n    if (callback(curNode)) return true;\n    return this._inOrderTraversal(curNode._right, callback);\n  }\n  /**\n   * @internal\n   */\n  protected _insertNodeSelfBalance(curNode: TreeNode<K, V>) {\n    while (true) {\n      const parentNode = curNode._parent!;\n      if (parentNode._color === TreeNodeColor.BLACK) return;\n      const grandParent = parentNode._parent!;\n      if (parentNode === grandParent._left) {\n        const uncle = grandParent._right;\n        if (uncle && uncle._color === TreeNodeColor.RED) {\n          uncle._color = parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) return;\n          grandParent._color = TreeNodeColor.RED;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._right) {\n          curNode._color = TreeNodeColor.BLACK;\n          if (curNode._left) curNode._left._parent = parentNode;\n          if (curNode._right) curNode._right._parent = grandParent;\n          parentNode._right = curNode._left;\n          grandParent._left = curNode._right;\n          curNode._left = parentNode;\n          curNode._right = grandParent;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent!;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = TreeNodeColor.RED;\n          return { parentNode, grandParent, curNode };\n        } else {\n          parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateRight();\n          } else grandParent._rotateRight();\n          grandParent._color = TreeNodeColor.RED;\n        }\n      } else {\n        const uncle = grandParent._left;\n        if (uncle && uncle._color === TreeNodeColor.RED) {\n          uncle._color = parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) return;\n          grandParent._color = TreeNodeColor.RED;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._left) {\n          curNode._color = TreeNodeColor.BLACK;\n          if (curNode._left) curNode._left._parent = grandParent;\n          if (curNode._right) curNode._right._parent = parentNode;\n          grandParent._right = curNode._left;\n          parentNode._left = curNode._right;\n          curNode._left = grandParent;\n          curNode._right = parentNode;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent!;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = TreeNodeColor.RED;\n          return { parentNode, grandParent, curNode };\n        } else {\n          parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateLeft();\n          } else grandParent._rotateLeft();\n          grandParent._color = TreeNodeColor.RED;\n        }\n      }\n      return;\n    }\n  }\n  /**\n   * @internal\n   */\n  protected _preSet(key: K, value?: V, hint?: TreeIterator<K, V>) {\n    if (this._root === undefined) {\n      this._length += 1;\n      this._root = new this._TreeNodeClass(key, value);\n      this._root._color = TreeNodeColor.BLACK;\n      this._root._parent = this._header;\n      this._header._parent = this._root;\n      this._header._left = this._root;\n      this._header._right = this._root;\n      return;\n    }\n    let curNode;\n    const minNode = this._header._left!;\n    const compareToMin = this._cmp(minNode._key!, key);\n    if (compareToMin === 0) {\n      minNode._value = value;\n      return;\n    } else if (compareToMin > 0) {\n      minNode._left = new this._TreeNodeClass(key, value);\n      minNode._left._parent = minNode;\n      curNode = minNode._left;\n      this._header._left = curNode;\n    } else {\n      const maxNode = this._header._right!;\n      const compareToMax = this._cmp(maxNode._key!, key);\n      if (compareToMax === 0) {\n        maxNode._value = value;\n        return;\n      } else if (compareToMax < 0) {\n        maxNode._right = new this._TreeNodeClass(key, value);\n        maxNode._right._parent = maxNode;\n        curNode = maxNode._right;\n        this._header._right = curNode;\n      } else {\n        if (hint !== undefined) {\n          const iterNode = hint._node;\n          if (iterNode !== this._header) {\n            const iterCmpRes = this._cmp(iterNode._key!, key);\n            if (iterCmpRes === 0) {\n              iterNode._value = value;\n              return;\n            } else /* istanbul ignore else */ if (iterCmpRes > 0) {\n              const preNode = iterNode._pre();\n              const preCmpRes = this._cmp(preNode._key!, key);\n              if (preCmpRes === 0) {\n                preNode._value = value;\n                return;\n              } else if (preCmpRes < 0) {\n                curNode = new this._TreeNodeClass(key, value);\n                if (preNode._right === undefined) {\n                  preNode._right = curNode;\n                  curNode._parent = preNode;\n                } else {\n                  iterNode._left = curNode;\n                  curNode._parent = iterNode;\n                }\n              }\n            }\n          }\n        }\n        if (curNode === undefined) {\n          curNode = this._root;\n          while (true) {\n            const cmpResult = this._cmp(curNode._key!, key);\n            if (cmpResult > 0) {\n              if (curNode._left === undefined) {\n                curNode._left = new this._TreeNodeClass(key, value);\n                curNode._left._parent = curNode;\n                curNode = curNode._left;\n                break;\n              }\n              curNode = curNode._left;\n            } else if (cmpResult < 0) {\n              if (curNode._right === undefined) {\n                curNode._right = new this._TreeNodeClass(key, value);\n                curNode._right._parent = curNode;\n                curNode = curNode._right;\n                break;\n              }\n              curNode = curNode._right;\n            } else {\n              curNode._value = value;\n              return;\n            }\n          }\n        }\n      }\n    }\n    this._length += 1;\n    return curNode;\n  }\n  /**\n   * @internal\n   */\n  protected _findElementNode(curNode: TreeNode<K, V> | undefined, key: K) {\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return curNode || this._header;\n  }\n  clear() {\n    this._length = 0;\n    this._root = undefined;\n    this._header._parent = undefined;\n    this._header._left = this._header._right = undefined;\n  }\n  /**\n   * @description Update node's key by iterator.\n   * @param iter - The iterator you want to change.\n   * @param key - The key you want to update.\n   * @returns Whether the modification is successful.\n   * @example\n   * const st = new orderedSet([1, 2, 5]);\n   * const iter = st.find(2);\n   * st.updateKeyByIterator(iter, 3); // then st will become [1, 3, 5]\n   */\n  updateKeyByIterator(iter: TreeIterator<K, V>, key: K): boolean {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    if (this._length === 1) {\n      node._key = key;\n      return true;\n    }\n    if (node === this._header._left) {\n      if (this._cmp(node._next()._key!, key) > 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    if (node === this._header._right) {\n      if (this._cmp(node._pre()._key!, key) < 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    const preKey = node._pre()._key!;\n    if (this._cmp(preKey, key) >= 0) return false;\n    const nextKey = node._next()._key!;\n    if (this._cmp(nextKey, key) <= 0) return false;\n    node._key = key;\n    return true;\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let index = 0;\n    const self = this;\n    this._inOrderTraversal(\n      this._root,\n      function (curNode) {\n        if (pos === index) {\n          self._eraseNode(curNode);\n          return true;\n        }\n        index += 1;\n        return false;\n      });\n    return this._length;\n  }\n  /**\n   * @description Remove the element of the specified key.\n   * @param key - The key you want to remove.\n   * @returns Whether erase successfully.\n   */\n  eraseElementByKey(key: K) {\n    if (this._length === 0) return false;\n    const curNode = this._findElementNode(this._root, key);\n    if (curNode === this._header) return false;\n    this._eraseNode(curNode);\n    return true;\n  }\n  eraseElementByIterator(iter: TreeIterator<K, V>) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    const hasNoRight = node._right === undefined;\n    const isNormal = iter.iteratorType === IteratorType.NORMAL;\n    // For the normal iterator, the `next` node will be swapped to `this` node when has right.\n    if (isNormal) {\n      // So we should move it to next when it's right is null.\n      if (hasNoRight) iter.next();\n    } else {\n      // For the reverse iterator, only when it doesn't have right and has left the `next` node will be swapped.\n      // So when it has right, or it is a leaf node we should move it to `next`.\n      if (!hasNoRight || node._left === undefined) iter.next();\n    }\n    this._eraseNode(node);\n    return iter;\n  }\n  forEach(callback: (element: K | [K, V], index: number, tree: TreeContainer<K, V>) => void) {\n    let index = 0;\n    for (const element of this) callback(element, index++, this);\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let res;\n    let index = 0;\n    for (const element of this) {\n      if (index === pos) {\n        res = element;\n        break;\n      }\n      index += 1;\n    }\n    return <K | [K, V]>res;\n  }\n  /**\n   * @description Get the height of the tree.\n   * @returns Number about the height of the RB-tree.\n   */\n  getHeight() {\n    if (this._length === 0) return 0;\n    const traversal =\n      function (curNode: TreeNode<K, V> | undefined): number {\n        if (!curNode) return 0;\n        return Math.max(traversal(curNode._left), traversal(curNode._right)) + 1;\n      };\n    return traversal(this._root);\n  }\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element less than the given key.\n   */\n  abstract reverseUpperBound(key: K): TreeIterator<K, V>;\n  /**\n   * @description Union the other tree to self.\n   * @param other - The other tree container you want to merge.\n   * @returns The size of the tree after union.\n   */\n  abstract union(other: TreeContainer<K, V>): number;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element not greater than the given key.\n   */\n  abstract reverseLowerBound(key: K): TreeIterator<K, V>;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element not less than the given key.\n   */\n  abstract lowerBound(key: K): TreeIterator<K, V>;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element greater than the given key.\n   */\n  abstract upperBound(key: K): TreeIterator<K, V>;\n}\n\nexport default TreeContainer;\n"]}
{"version": 3, "sources": ["container/TreeContainer/OrderedMap.js", "../../src/container/TreeContainer/OrderedMap.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_Base", "_interopRequireDefault", "require", "_TreeIterator", "_throwError", "obj", "__esModule", "OrderedMapIterator", "TreeIterator", "constructor", "node", "header", "container", "iteratorType", "super", "this", "pointer", "_node", "_header", "throwIteratorAccessError", "self", "Proxy", "get", "_", "props", "_key", "_value", "set", "newValue", "TypeError", "copy", "OrderedMap", "TreeC<PERSON>r", "cmp", "enableIndex", "for<PERSON>ach", "el", "setElement", "K", "curNode", "undefined", "_iterationFunc", "_left", "_right", "begin", "end", "rBegin", "rEnd", "front", "_length", "minNode", "back", "maxNode", "lowerBound", "key", "resNode", "_lowerBound", "_root", "upperBound", "_upperBound", "reverseLowerBound", "_reverseLowerBound", "reverseUpperBound", "_reverseUpperBound", "hint", "_set", "find", "_findElementNode", "getElement<PERSON>y<PERSON>ey", "union", "other", "Symbol", "iterator", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,QAAAC,uBAAAC,QAAA;;AACA,IAAAC,gBAAAF,uBAAAC,QAAA;;AAGA,IAAAE,cAAAF,QAAA;;AAA8D,SAAAD,uBAAAI;IAAA,OAAAA,KAAAA,EAAAC,IAAAD,IAAA;QAAAN,SAAAM;;AAAA;;AAE9D,MAAME,2BAAiCC,cAAAA;IAErCC,YACEC,GACAC,GACAC,GACAC;QAEAC,MAAMJ,GAAMC,GAAQE;QACpBE,KAAKH,YAAYA;ADRjB;ICUEI;QACF,IAAID,KAAKE,MAAUF,KAAKG,GAAS;aAC/B,GAAAC,YAAAA;ADRE;QCUJ,MAAMC,IAAOL;QACb,OAAO,IAAIM,MAAuB,IAAI;YACpCC,IAAIC,GAAGC;gBACL,IAAIA,MAAU,KAAK,OAAOJ,EAAKH,EAAMQ,QAChC,IAAID,MAAU,KAAK,OAAOJ,EAAKH,EAAMS;ADNtC;YCQNC,IAAIJ,GAAGC,GAAYI;gBACjB,IAAIJ,MAAU,KAAK;oBACjB,MAAM,IAAIK,UAAU;ADNd;gBCQRT,EAAKH,EAAMS,IAASE;gBACpB,OAAO;ADNH;;AAER;ICQFE;QACE,OAAO,IAAIvB,mBACTQ,KAAKE,GACLF,KAAKG,GACLH,KAAKH,WACLG,KAAKF;ADVP;;;ACmBJ,MAAMkB,mBAAyBC,MAAAA;IAW7BvB,YACEG,IAAmC,IACnCqB,GACAC;QAEApB,MAAMmB,GAAKC;QACX,MAAMd,IAAOL;QACbH,EAAUuB,SAAQ,SAAUC;YAC1BhB,EAAKiB,WAAWD,EAAG,IAAIA,EAAG;ADpBxB;AACJ;ICyBME,IACNC;QAEA,IAAIA,MAAYC,WAAW;eACnBzB,KAAK0B,EAAeF,EAAQG;cACtB,EAACH,EAAQd,GAAMc,EAAQb;eAC7BX,KAAK0B,EAAeF,EAAQI;ADrBpC;ICuBFC;QACE,OAAO,IAAIrC,mBAAyBQ,KAAKG,EAAQwB,KAAS3B,KAAKG,GAASH,KAAKG,GAASH;ADrBtF;ICuBF8B;QACE,OAAO,IAAItC,mBAAyBQ,KAAKG,GAASH,KAAKG,GAASH;ADrBhE;ICuBF+B;QACE,OAAO,IAAIvC,mBACTQ,KAAKG,EAAQyB,KAAU5B,KAAKG,GAC5BH,KAAKG,GACLH,MAAI;ADxBN;IC4BFgC;QACE,OAAO,IAAIxC,mBAAyBQ,KAAKG,GAASH,KAAKG,GAASH,MAAI;AD1BpE;IC4BFiC;QACE,IAAIjC,KAAKkC,MAAY,GAAG;QACxB,MAAMC,IAAUnC,KAAKG,EAAQwB;QAC7B,OAAe,EAACQ,EAAQzB,GAAMyB,EAAQxB;ADzBtC;IC2BFyB;QACE,IAAIpC,KAAKkC,MAAY,GAAG;QACxB,MAAMG,IAAUrC,KAAKG,EAAQyB;QAC7B,OAAe,EAACS,EAAQ3B,GAAM2B,EAAQ1B;ADxBtC;IC0BF2B,WAAWC;QACT,MAAMC,IAAUxC,KAAKyC,EAAYzC,KAAK0C,GAAOH;QAC7C,OAAO,IAAI/C,mBAAyBgD,GAASxC,KAAKG,GAASH;ADxB3D;IC0BF2C,WAAWJ;QACT,MAAMC,IAAUxC,KAAK4C,EAAY5C,KAAK0C,GAAOH;QAC7C,OAAO,IAAI/C,mBAAyBgD,GAASxC,KAAKG,GAASH;ADxB3D;IC0BF6C,kBAAkBN;QAChB,MAAMC,IAAUxC,KAAK8C,EAAmB9C,KAAK0C,GAAOH;QACpD,OAAO,IAAI/C,mBAAyBgD,GAASxC,KAAKG,GAASH;ADxB3D;IC0BF+C,kBAAkBR;QAChB,MAAMC,IAAUxC,KAAKgD,GAAmBhD,KAAK0C,GAAOH;QACpD,OAAO,IAAI/C,mBAAyBgD,GAASxC,KAAKG,GAASH;ADxB3D;ICsCFsB,WAAWiB,GAAQxD,GAAUkE;QAC3B,OAAOjD,KAAKkD,EAAKX,GAAKxD,GAAOkE;ADxB7B;IC0BFE,KAAKZ;QACH,MAAMf,IAAUxB,KAAKoD,EAAiBpD,KAAK0C,GAAOH;QAClD,OAAO,IAAI/C,mBAAyBgC,GAASxB,KAAKG,GAASH;ADxB3D;ICgCFqD,gBAAgBd;QACd,MAAMf,IAAUxB,KAAKoD,EAAiBpD,KAAK0C,GAAOH;QAClD,OAAOf,EAAQb;ADxBf;IC0BF2C,MAAMC;QACJ,MAAMlD,IAAOL;QACbuD,EAAMnC,SAAQ,SAAUC;YACtBhB,EAAKiB,WAAWD,EAAG,IAAIA,EAAG;ADxBxB;QC0BJ,OAAOrB,KAAKkC;ADxBZ;IC0BF,CAACsB,OAAOC;QACN,OAAOzD,KAAK0B,EAAe1B,KAAK0C;ADxBhC;;;ACgCH,IAAAgB,WAEc1C;;AAAUlC,QAAAE,UAAA0E", "file": "OrderedMap.js", "sourcesContent": ["import TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nclass OrderedMapIterator extends TreeIterator {\n    constructor(node, header, container, iteratorType) {\n        super(node, header, iteratorType);\n        this.container = container;\n    }\n    get pointer() {\n        if (this._node === this._header) {\n            throwIteratorAccessError();\n        }\n        const self = this;\n        return new Proxy([], {\n            get(_, props) {\n                if (props === '0')\n                    return self._node._key;\n                else if (props === '1')\n                    return self._node._value;\n            },\n            set(_, props, newValue) {\n                if (props !== '1') {\n                    throw new TypeError('props must be 1');\n                }\n                self._node._value = newValue;\n                return true;\n            }\n        });\n    }\n    copy() {\n        return new OrderedMapIterator(this._node, this._header, this.container, this.iteratorType);\n    }\n}\nclass OrderedMap extends TreeContainer {\n    /**\n     * @param container - The initialization container.\n     * @param cmp - The compare function.\n     * @param enableIndex - Whether to enable iterator indexing function.\n     * @example\n     * new OrderedMap();\n     * new OrderedMap([[0, 1], [2, 1]]);\n     * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y);\n     * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y, true);\n     */\n    constructor(container = [], cmp, enableIndex) {\n        super(cmp, enableIndex);\n        const self = this;\n        container.forEach(function (el) {\n            self.setElement(el[0], el[1]);\n        });\n    }\n    /**\n     * @internal\n     */\n    *_iterationFunc(curNode) {\n        if (curNode === undefined)\n            return;\n        yield* this._iterationFunc(curNode._left);\n        yield [curNode._key, curNode._value];\n        yield* this._iterationFunc(curNode._right);\n    }\n    begin() {\n        return new OrderedMapIterator(this._header._left || this._header, this._header, this);\n    }\n    end() {\n        return new OrderedMapIterator(this._header, this._header, this);\n    }\n    rBegin() {\n        return new OrderedMapIterator(this._header._right || this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    rEnd() {\n        return new OrderedMapIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    }\n    front() {\n        if (this._length === 0)\n            return;\n        const minNode = this._header._left;\n        return [minNode._key, minNode._value];\n    }\n    back() {\n        if (this._length === 0)\n            return;\n        const maxNode = this._header._right;\n        return [maxNode._key, maxNode._value];\n    }\n    lowerBound(key) {\n        const resNode = this._lowerBound(this._root, key);\n        return new OrderedMapIterator(resNode, this._header, this);\n    }\n    upperBound(key) {\n        const resNode = this._upperBound(this._root, key);\n        return new OrderedMapIterator(resNode, this._header, this);\n    }\n    reverseLowerBound(key) {\n        const resNode = this._reverseLowerBound(this._root, key);\n        return new OrderedMapIterator(resNode, this._header, this);\n    }\n    reverseUpperBound(key) {\n        const resNode = this._reverseUpperBound(this._root, key);\n        return new OrderedMapIterator(resNode, this._header, this);\n    }\n    /**\n     * @description Insert a key-value pair or set value by the given key.\n     * @param key - The key want to insert.\n     * @param value - The value want to set.\n     * @param hint - You can give an iterator hint to improve insertion efficiency.\n     * @return The size of container after setting.\n     * @example\n     * const mp = new OrderedMap([[2, 0], [4, 0], [5, 0]]);\n     * const iter = mp.begin();\n     * mp.setElement(1, 0);\n     * mp.setElement(3, 0, iter);  // give a hint will be faster.\n     */\n    setElement(key, value, hint) {\n        return this._set(key, value, hint);\n    }\n    find(key) {\n        const curNode = this._findElementNode(this._root, key);\n        return new OrderedMapIterator(curNode, this._header, this);\n    }\n    /**\n     * @description Get the value of the element of the specified key.\n     * @param key - The specified key you want to get.\n     * @example\n     * const val = container.getElementByKey(1);\n     */\n    getElementByKey(key) {\n        const curNode = this._findElementNode(this._root, key);\n        return curNode._value;\n    }\n    union(other) {\n        const self = this;\n        other.forEach(function (el) {\n            self.setElement(el[0], el[1]);\n        });\n        return this._length;\n    }\n    [Symbol.iterator]() {\n        return this._iterationFunc(this._root);\n    }\n}\nexport default OrderedMap;\n", "import TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { TreeNode } from './Base/TreeNode';\nimport { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass OrderedMapIterator<K, V> extends TreeIterator<K, V> {\n  container: OrderedMap<K, V>;\n  constructor(\n    node: TreeNode<K, V>,\n    header: TreeNode<K, V>,\n    container: OrderedMap<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    const self = this;\n    return new Proxy(<[K, V]><unknown>[], {\n      get(_, props: '0' | '1') {\n        if (props === '0') return self._node._key;\n        else if (props === '1') return self._node._value;\n      },\n      set(_, props: '1', newValue: V) {\n        if (props !== '1') {\n          throw new TypeError('props must be 1');\n        }\n        self._node._value = newValue;\n        return true;\n      }\n    });\n  }\n  copy() {\n    return new OrderedMapIterator<K, V>(\n      this._node,\n      this._header,\n      this.container,\n      this.iteratorType\n    );\n  }\n  // @ts-ignore\n  equals(iter: OrderedMapIterator<K, V>): boolean;\n}\n\nexport type { OrderedMapIterator };\n\nclass OrderedMap<K, V> extends TreeContainer<K, V> {\n  /**\n   * @param container - The initialization container.\n   * @param cmp - The compare function.\n   * @param enableIndex - Whether to enable iterator indexing function.\n   * @example\n   * new OrderedMap();\n   * new OrderedMap([[0, 1], [2, 1]]);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y, true);\n   */\n  constructor(\n    container: initContainer<[K, V]> = [],\n    cmp?: (x: K, y: K) => number,\n    enableIndex?: boolean\n  ) {\n    super(cmp, enableIndex);\n    const self = this;\n    container.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n  }\n  /**\n   * @internal\n   */\n  private * _iterationFunc(\n    curNode: TreeNode<K, V> | undefined\n  ): Generator<[K, V], void> {\n    if (curNode === undefined) return;\n    yield * this._iterationFunc(curNode._left);\n    yield <[K, V]>[curNode._key, curNode._value];\n    yield * this._iterationFunc(curNode._right);\n  }\n  begin() {\n    return new OrderedMapIterator<K, V>(this._header._left || this._header, this._header, this);\n  }\n  end() {\n    return new OrderedMapIterator<K, V>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new OrderedMapIterator<K, V>(\n      this._header._right || this._header,\n      this._header,\n      this,\n      IteratorType.REVERSE\n    );\n  }\n  rEnd() {\n    return new OrderedMapIterator<K, V>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front() {\n    if (this._length === 0) return;\n    const minNode = this._header._left!;\n    return <[K, V]>[minNode._key, minNode._value];\n  }\n  back() {\n    if (this._length === 0) return;\n    const maxNode = this._header._right!;\n    return <[K, V]>[maxNode._key, maxNode._value];\n  }\n  lowerBound(key: K) {\n    const resNode = this._lowerBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  upperBound(key: K) {\n    const resNode = this._upperBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  reverseLowerBound(key: K) {\n    const resNode = this._reverseLowerBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  reverseUpperBound(key: K) {\n    const resNode = this._reverseUpperBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  /**\n   * @description Insert a key-value pair or set value by the given key.\n   * @param key - The key want to insert.\n   * @param value - The value want to set.\n   * @param hint - You can give an iterator hint to improve insertion efficiency.\n   * @return The size of container after setting.\n   * @example\n   * const mp = new OrderedMap([[2, 0], [4, 0], [5, 0]]);\n   * const iter = mp.begin();\n   * mp.setElement(1, 0);\n   * mp.setElement(3, 0, iter);  // give a hint will be faster.\n   */\n  setElement(key: K, value: V, hint?: OrderedMapIterator<K, V>) {\n    return this._set(key, value, hint);\n  }\n  find(key: K) {\n    const curNode = this._findElementNode(this._root, key);\n    return new OrderedMapIterator<K, V>(curNode, this._header, this);\n  }\n  /**\n   * @description Get the value of the element of the specified key.\n   * @param key - The specified key you want to get.\n   * @example\n   * const val = container.getElementByKey(1);\n   */\n  getElementByKey(key: K) {\n    const curNode = this._findElementNode(this._root, key);\n    return curNode._value;\n  }\n  union(other: OrderedMap<K, V>) {\n    const self = this;\n    other.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n    return this._length;\n  }\n  [Symbol.iterator]() {\n    return this._iterationFunc(this._root);\n  }\n  // @ts-ignore\n  eraseElementByIterator(iter: OrderedMapIterator<K, V>): OrderedMapIterator<K, V>;\n  // @ts-ignore\n  forEach(callback: (element: [K, V], index: number, map: OrderedMap<K, V>) => void): void;\n  // @ts-ignore\n  getElementByPos(pos: number): [K, V];\n}\n\nexport default OrderedMap;\n"]}
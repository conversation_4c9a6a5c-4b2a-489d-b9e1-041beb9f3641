{"version": 3, "sources": ["utils/checkObject.js", "../../src/utils/checkObject.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "checkObject", "key", "t"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,UAAUC;;ACCJ,SAAUA,YAAeC;IACrC,MAAMC,WAAWD;IACjB,OAAQC,MAAM,YAAYD,MAAQ,QAASC,MAAM;ADCnD", "file": "checkObject.js", "sourcesContent": ["/**\n * @description Determine whether the type of key is `object`.\n * @param key - The key want to check.\n * @returns Whether the type of key is `object`.\n * @internal\n */\nexport default function checkObject(key) {\n    const t = typeof key;\n    return (t === 'object' && key !== null) || t === 'function';\n}\n", "/**\n * @description Determine whether the type of key is `object`.\n * @param key - The key want to check.\n * @returns Whether the type of key is `object`.\n * @internal\n */\nexport default function checkObject<T>(key: T) {\n  const t = typeof key;\n  return (t === 'object' && key !== null) || t === 'function';\n}\n"]}
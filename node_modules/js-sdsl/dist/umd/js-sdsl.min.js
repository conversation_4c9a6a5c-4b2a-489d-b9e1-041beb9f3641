/*!
 * js-sdsl v4.3.0
 * https://github.com/js-sdsl/js-sdsl
 * (c) 2021-present ZLY201 <<EMAIL>>
 * MIT license
 */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?i(exports):"function"==typeof define&&define.amd?define(["exports"],i):i((t="undefined"!=typeof globalThis?globalThis:t||self).sdsl={})}(this,function(t){"use strict";var L=function(t,i){return(L=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,i){t.__proto__=i}:function(t,i){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}))(t,i)};function i(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function r(){this.constructor=t}L(t,i),t.prototype=null===i?Object.create(i):(r.prototype=i.prototype,new r)}function r(e,n){var o,s,h,u={label:0,sent:function(){if(1&h[0])throw h[1];return h[1]},trys:[],ops:[]},f={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(f[Symbol.iterator]=function(){return this}),f;function t(r){return function(t){var i=[r,t];if(o)throw new TypeError("Generator is already executing.");for(;u=f&&i[f=0]?0:u;)try{if(o=1,s&&(h=2&i[0]?s.return:i[0]?s.throw||((h=s.return)&&h.call(s),0):s.next)&&!(h=h.call(s,i[1])).done)return h;switch(s=0,(i=h?[2&i[0],h.value]:i)[0]){case 0:case 1:h=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,s=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(h=0<(h=u.trys).length&&h[h.length-1])&&(6===i[0]||2===i[0])){u=0;continue}if(3===i[0]&&(!h||i[1]>h[0]&&i[1]<h[3]))u.label=i[1];else if(6===i[0]&&u.label<h[1])u.label=h[1],h=i;else{if(!(h&&u.label<h[2])){h[2]&&u.ops.pop(),u.trys.pop();continue}u.label=h[2],u.ops.push(i)}}i=n.call(e,u)}catch(t){i=[6,t],s=0}finally{o=h=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}}}function u(t){var i="function"==typeof Symbol&&Symbol.iterator,r=i&&t[i],e=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return{value:(t=t&&e>=t.length?void 0:t)&&t[e++],done:!t}}};throw new TypeError(i?"Object is not iterable.":"Symbol.iterator is not defined.")}function h(t,i){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var e,n,o=r.call(t),s=[];try{for(;(void 0===i||0<i--)&&!(e=o.next()).done;)s.push(e.value)}catch(t){n={error:t}}finally{try{e&&!e.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return s}function f(t,i,r){if(r||2===arguments.length)for(var e,n=0,o=i.length;n<o;n++)!e&&n in i||((e=e||Array.prototype.slice.call(i,0,n))[n]=i[n]);return t.concat(e||Array.prototype.slice.call(i))}M.prototype.equals=function(t){return this.t===t.t};var e=M;function M(t){this.iteratorType=t=void 0===t?0:t}Object.defineProperty(V.prototype,"length",{get:function(){return this.i},enumerable:!1,configurable:!0}),V.prototype.size=function(){return this.i},V.prototype.empty=function(){return 0===this.i};var n=V;function V(){this.i=0}i(j,R=n);var R,_=j;function j(){return null!==R&&R.apply(this,arguments)||this}i(o,q=n),o.prototype.clear=function(){this.i=0,this.h=[]},o.prototype.push=function(t){return this.h.push(t),this.i+=1,this.i},o.prototype.pop=function(){if(0!==this.i)return--this.i,this.h.pop()},o.prototype.top=function(){return this.h[this.i-1]};var q,C=o;function o(t){void 0===t&&(t=[]);var i=q.call(this)||this,r=(i.h=[],i);return t.forEach(function(t){r.push(t)}),i}i(s,D=n),s.prototype.clear=function(){this.o=[],this.i=this.u=0},s.prototype.push=function(t){var i=this.o.length;if(.5<this.u/i&&this.u+this.i>=i&&4096<i){for(var r=this.i,e=0;e<r;++e)this.o[e]=this.o[this.u+e];this.u=0,this.o[this.i]=t}else this.o[this.u+this.i]=t;return++this.i},s.prototype.pop=function(){var t;if(0!==this.i)return t=this.o[this.u++],--this.i,t},s.prototype.front=function(){if(0!==this.i)return this.o[this.u]};var D,K=s;function s(t){void 0===t&&(t=[]);var i=D.call(this)||this,r=(i.u=0,i.o=[],i);return t.forEach(function(t){r.push(t)}),i}i(p,U=n),p.prototype.p=function(t){for(var i=this.l[t];0<t;){var r=t-1>>1,e=this.l[r];if(this.v(e,i)<=0)break;this.l[t]=e,t=r}this.l[t]=i},p.prototype._=function(t,i){for(var r=this.l[t];t<i;){var e=t<<1|1,n=e+1,o=this.l[e];if(n<this.i&&0<this.v(o,this.l[n])&&(o=this.l[e=n]),0<=this.v(o,r))break;this.l[t]=o,t=e}this.l[t]=r},p.prototype.clear=function(){this.i=0,this.l.length=0},p.prototype.push=function(t){this.l.push(t),this.p(this.i),this.i+=1},p.prototype.pop=function(){var t,i;if(0!==this.i)return t=this.l[0],i=this.l.pop(),--this.i,this.i&&(this.l[0]=i,this._(0,this.i>>1)),t},p.prototype.top=function(){return this.l[0]},p.prototype.find=function(t){return 0<=this.l.indexOf(t)},p.prototype.remove=function(t){t=this.l.indexOf(t);return!(t<0||(0===t?this.pop():t===this.i-1?(this.l.pop(),--this.i):(this.l.splice(t,1,this.l.pop()),--this.i,this.p(t),this._(t,this.i>>1)),0))},p.prototype.updateItem=function(t){t=this.l.indexOf(t);return!(t<0||(this.p(t),this._(t,this.i>>1),0))},p.prototype.toArray=function(){return f([],h(this.l),!1)};var U,n=p;function p(t,i,r){void 0===t&&(t=[]),void 0===i&&(i=function(t,i){return i<t?-1:t<i?1:0}),void 0===r&&(r=!0);for(var e,n=U.call(this)||this,o=(n.v=i,Array.isArray(t)?n.l=r?f([],h(t),!1):t:(n.l=[],e=n,t.forEach(function(t){e.l.push(t)})),n.i=n.l.length,n.i>>1),s=n.i-1>>1;0<=s;--s)n._(s,o);return n}i(Y,J=_);var J,c=Y;function Y(){return null!==J&&J.apply(this,arguments)||this}function a(){throw new RangeError("Iterator access denied!")}i(Z,z=e),Object.defineProperty(Z.prototype,"pointer",{get:function(){return this.container.getElementByPos(this.t)},set:function(t){this.container.setElementByPos(this.t,t)},enumerable:!1,configurable:!0});var z,W=Z;function Z(t,i){i=z.call(this,i)||this;return i.t=t,0===i.iteratorType?(i.pre=function(){return 0===this.t&&a(),--this.t,this},i.next=function(){return this.t===this.container.size()&&a(),this.t+=1,this}):(i.pre=function(){return this.t===this.container.size()-1&&a(),this.t+=1,this},i.next=function(){return-1===this.t&&a(),--this.t,this}),i}i(Q,$=W),Q.prototype.copy=function(){return new Q(this.t,this.container,this.iteratorType)};var $,y=Q;function Q(t,i,r){t=$.call(this,t,r)||this;return t.container=i,t}i(l,tt=c),l.prototype.clear=function(){this.i=0,this.I.length=0},l.prototype.begin=function(){return new y(0,this)},l.prototype.end=function(){return new y(this.i,this)},l.prototype.rBegin=function(){return new y(this.i-1,this,1)},l.prototype.rEnd=function(){return new y(-1,this,1)},l.prototype.front=function(){return this.I[0]},l.prototype.back=function(){return this.I[this.i-1]},l.prototype.getElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;return this.I[t]},l.prototype.eraseElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;return this.I.splice(t,1),--this.i,this.i},l.prototype.eraseElementByValue=function(t){for(var i=0,r=0;r<this.i;++r)this.I[r]!==t&&(this.I[i++]=this.I[r]);return this.i=this.I.length=i,this.i},l.prototype.eraseElementByIterator=function(t){var i=t.t;return t=t.next(),this.eraseElementByPos(i),t},l.prototype.pushBack=function(t){return this.I.push(t),this.i+=1,this.i},l.prototype.popBack=function(){if(0!==this.i)return--this.i,this.I.pop()},l.prototype.setElementByPos=function(t,i){if(t<0||t>this.i-1)throw new RangeError;this.I[t]=i},l.prototype.insert=function(t,i,r){var e;if(void 0===r&&(r=1),t<0||t>this.i)throw new RangeError;return(e=this.I).splice.apply(e,f([t,0],h(new Array(r).fill(i)),!1)),this.i+=r,this.i},l.prototype.find=function(t){for(var i=0;i<this.i;++i)if(this.I[i]===t)return new y(i,this);return this.end()},l.prototype.reverse=function(){this.I.reverse()},l.prototype.unique=function(){for(var t=1,i=1;i<this.i;++i)this.I[i]!==this.I[i-1]&&(this.I[t++]=this.I[i]);return this.i=this.I.length=t,this.i},l.prototype.sort=function(t){this.I.sort(t)},l.prototype.forEach=function(t){for(var i=0;i<this.i;++i)t(this.I[i],i,this)},l.prototype[Symbol.iterator]=function(){return function(){return r(this,function(t){switch(t.label){case 0:return[5,u(this.I)];case 1:return t.sent(),[2]}})}.bind(this)()};var tt,it=l;function l(t,i){void 0===t&&(t=[]),void 0===i&&(i=!0);var r,e=tt.call(this)||this;return Array.isArray(t)?(e.I=i?f([],h(t),!1):t,e.i=t.length):(e.I=[],r=e,t.forEach(function(t){r.pushBack(t)})),e}i(d,rt=e),Object.defineProperty(d.prototype,"pointer",{get:function(){return this.t===this.S&&a(),this.t.k},set:function(t){this.t===this.S&&a(),this.t.k=t},enumerable:!1,configurable:!0}),d.prototype.copy=function(){return new d(this.t,this.S,this.container,this.iteratorType)};var rt,v=d;function d(t,i,r,e){e=rt.call(this,e)||this;return e.t=t,e.S=i,e.container=r,0===e.iteratorType?(e.pre=function(){return this.t.L===this.S&&a(),this.t=this.t.L,this},e.next=function(){return this.t===this.S&&a(),this.t=this.t.O,this}):(e.pre=function(){return this.t.O===this.S&&a(),this.t=this.t.O,this},e.next=function(){return this.t===this.S&&a(),this.t=this.t.L,this}),e}i(S,et=c),S.prototype.M=function(t){var i=t.L,r=t.O;(i.O=r).L=i,t===this.H&&(this.H=r),t===this.g&&(this.g=i),--this.i},S.prototype.A=function(t,i){var r=i.O,t={k:t,L:i,O:r};i.O=t,r.L=t,i===this.S&&(this.H=t),r===this.S&&(this.g=t),this.i+=1},S.prototype.clear=function(){this.i=0,this.H=this.g=this.S.L=this.S.O=this.S},S.prototype.begin=function(){return new v(this.H,this.S,this)},S.prototype.end=function(){return new v(this.S,this.S,this)},S.prototype.rBegin=function(){return new v(this.g,this.S,this,1)},S.prototype.rEnd=function(){return new v(this.S,this.S,this,1)},S.prototype.front=function(){return this.H.k},S.prototype.back=function(){return this.g.k},S.prototype.getElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;for(var i=this.H;t--;)i=i.O;return i.k},S.prototype.eraseElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;for(var i=this.H;t--;)i=i.O;return this.M(i),this.i},S.prototype.eraseElementByValue=function(t){for(var i=this.H;i!==this.S;)i.k===t&&this.M(i),i=i.O;return this.i},S.prototype.eraseElementByIterator=function(t){var i=t.t;return i===this.S&&a(),t=t.next(),this.M(i),t},S.prototype.pushBack=function(t){return this.A(t,this.g),this.i},S.prototype.popBack=function(){var t;if(0!==this.i)return t=this.g.k,this.M(this.g),t},S.prototype.pushFront=function(t){return this.A(t,this.S),this.i},S.prototype.popFront=function(){var t;if(0!==this.i)return t=this.H.k,this.M(this.H),t},S.prototype.setElementByPos=function(t,i){if(t<0||t>this.i-1)throw new RangeError;for(var r=this.H;t--;)r=r.O;r.k=i},S.prototype.insert=function(t,i,r){if(void 0===r&&(r=1),t<0||t>this.i)throw new RangeError;if(!(r<=0))if(0===t)for(;r--;)this.pushFront(i);else if(t===this.i)for(;r--;)this.pushBack(i);else{for(var e=this.H,n=1;n<t;++n)e=e.O;var o=e.O;for(this.i+=r;r--;)e.O={k:i,L:e},e=(e.O.L=e).O;(e.O=o).L=e}return this.i},S.prototype.find=function(t){for(var i=this.H;i!==this.S;){if(i.k===t)return new v(i,this.S,this);i=i.O}return this.end()},S.prototype.reverse=function(){if(!(this.i<=1))for(var t=this.H,i=this.g,r=0;r<<1<this.i;){var e=t.k;t.k=i.k,i.k=e,t=t.O,i=i.L,r+=1}},S.prototype.unique=function(){if(!(this.i<=1))for(var t=this.H;t!==this.S;){for(var i=t;i.O!==this.S&&i.k===i.O.k;)i=i.O,--this.i;t.O=i.O,t=(t.O.L=t).O}return this.i},S.prototype.sort=function(t){var i,r;this.i<=1||(i=[],this.forEach(function(t){i.push(t)}),i.sort(t),r=this.H,i.forEach(function(t){r.k=t,r=r.O}))},S.prototype.merge=function(t){var i,r=this;return 0===this.i?t.forEach(function(t){r.pushBack(t)}):(i=this.H,t.forEach(function(t){for(;i!==r.S&&i.k<=t;)i=i.O;r.A(t,i.L)})),this.i},S.prototype.forEach=function(t){for(var i=this.H,r=0;i!==this.S;)t(i.k,r++,this),i=i.O},S.prototype[Symbol.iterator]=function(){return function(){var i;return r(this,function(t){switch(t.label){case 0:if(0===this.i)return[2];i=this.H,t.label=1;case 1:return i===this.S?[3,3]:[4,i.k];case 2:return t.sent(),i=i.O,[3,1];case 3:return[2]}})}.bind(this)()};var et,nt=S;function S(t){void 0===t&&(t=[]);var i=et.call(this)||this,r=(i.S={},i.H=i.g=i.S.L=i.S.O=i.S,i);return t.forEach(function(t){r.pushBack(t)}),i}i(st,ot=W),st.prototype.copy=function(){return new st(this.t,this.container,this.iteratorType)};var ot,B=st;function st(t,i,r){t=ot.call(this,t,r)||this;return t.container=i,t}i(w,ht=c),w.prototype.j=function(){for(var t=[],i=Math.max(this.D>>1,1),r=0;r<i;++r)t[r]=new Array(this.V);for(r=this.u;r<this.D;++r)t[t.length]=this.m[r];for(r=0;r<this.C;++r)t[t.length]=this.m[r];t[t.length]=f([],h(this.m[this.C]),!1),this.u=i,this.C=t.length-1;for(r=0;r<i;++r)t[t.length]=new Array(this.V);this.m=t,this.D=t.length},w.prototype.R=function(t){var t=this.T+t+1,i=t%this.V,r=i-1,t=this.u+(t-i)/this.V;return 0==i&&--t,t%=this.D,r<0&&(r+=this.V),{curNodeBucketIndex:t,curNodePointerIndex:r}},w.prototype.clear=function(){this.m=[new Array(this.V)],this.D=1,this.u=this.C=this.i=0,this.T=this.q=this.V>>1},w.prototype.begin=function(){return new B(0,this)},w.prototype.end=function(){return new B(this.i,this)},w.prototype.rBegin=function(){return new B(this.i-1,this,1)},w.prototype.rEnd=function(){return new B(-1,this,1)},w.prototype.front=function(){if(0!==this.i)return this.m[this.u][this.T]},w.prototype.back=function(){if(0!==this.i)return this.m[this.C][this.q]},w.prototype.pushBack=function(t){return this.i&&(this.q<this.V-1?this.q+=1:(this.C<this.D-1?this.C+=1:this.C=0,this.q=0),this.C===this.u)&&this.q===this.T&&this.j(),this.i+=1,this.m[this.C][this.q]=t,this.i},w.prototype.popBack=function(){var t;if(0!==this.i)return t=this.m[this.C][this.q],1!==this.i&&(0<this.q?--this.q:(0<this.C?--this.C:this.C=this.D-1,this.q=this.V-1)),--this.i,t},w.prototype.pushFront=function(t){return this.i&&(0<this.T?--this.T:(0<this.u?--this.u:this.u=this.D-1,this.T=this.V-1),this.u===this.C)&&this.T===this.q&&this.j(),this.i+=1,this.m[this.u][this.T]=t,this.i},w.prototype.popFront=function(){var t;if(0!==this.i)return t=this.m[this.u][this.T],1!==this.i&&(this.T<this.V-1?this.T+=1:(this.u<this.D-1?this.u+=1:this.u=0,this.T=0)),--this.i,t},w.prototype.getElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;var t=this.R(t),i=t.curNodeBucketIndex,t=t.curNodePointerIndex;return this.m[i][t]},w.prototype.setElementByPos=function(t,i){if(t<0||t>this.i-1)throw new RangeError;var t=this.R(t),r=t.curNodeBucketIndex,t=t.curNodePointerIndex;this.m[r][t]=i},w.prototype.insert=function(t,i,r){if(void 0===r&&(r=1),t<0||t>this.i)throw new RangeError;if(0===t)for(;r--;)this.pushFront(i);else if(t===this.i)for(;r--;)this.pushBack(i);else{for(var e=[],n=t;n<this.i;++n)e.push(this.getElementByPos(n));this.cut(t-1);for(n=0;n<r;++n)this.pushBack(i);for(n=0;n<e.length;++n)this.pushBack(e[n])}return this.i},w.prototype.cut=function(t){var i,r;return t<0?(this.clear(),0):(i=(r=this.R(t)).curNodeBucketIndex,r=r.curNodePointerIndex,this.C=i,this.q=r,this.i=t+1,this.i)},w.prototype.eraseElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;if(0===t)this.popFront();else if(t===this.i-1)this.popBack();else{for(var i=[],r=t+1;r<this.i;++r)i.push(this.getElementByPos(r));this.cut(t),this.popBack();var e=this;i.forEach(function(t){e.pushBack(t)})}return this.i},w.prototype.eraseElementByValue=function(t){if(0===this.i)return 0;for(var i=[],r=0;r<this.i;++r){var e=this.getElementByPos(r);e!==t&&i.push(e)}for(var n=i.length,r=0;r<n;++r)this.setElementByPos(r,i[r]);return this.cut(n-1)},w.prototype.eraseElementByIterator=function(t){var i=t.t;return this.eraseElementByPos(i),t=t.next()},w.prototype.find=function(t){for(var i=0;i<this.i;++i)if(this.getElementByPos(i)===t)return new B(i,this);return this.end()},w.prototype.reverse=function(){for(var t=0,i=this.i-1;t<i;){var r=this.getElementByPos(t);this.setElementByPos(t,this.getElementByPos(i)),this.setElementByPos(i,r),t+=1,--i}},w.prototype.unique=function(){if(!(this.i<=1)){for(var t=1,i=this.getElementByPos(0),r=1;r<this.i;++r){var e=this.getElementByPos(r);e!==i&&this.setElementByPos(t++,i=e)}for(;this.i>t;)this.popBack()}return this.i},w.prototype.sort=function(t){for(var i=[],r=0;r<this.i;++r)i.push(this.getElementByPos(r));i.sort(t);for(r=0;r<this.i;++r)this.setElementByPos(r,i[r])},w.prototype.shrinkToFit=function(){if(0!==this.i){var i=[];this.forEach(function(t){i.push(t)}),this.D=Math.max(Math.ceil(this.i/this.V),1),this.i=this.u=this.C=this.T=this.q=0,this.m=[];for(var t=0;t<this.D;++t)this.m.push(new Array(this.V));for(t=0;t<i.length;++t)this.pushBack(i[t])}},w.prototype.forEach=function(t){for(var i=0;i<this.i;++i)t(this.getElementByPos(i),i,this)},w.prototype[Symbol.iterator]=function(){return function(){var i;return r(this,function(t){switch(t.label){case 0:i=0,t.label=1;case 1:return i<this.i?[4,this.getElementByPos(i)]:[3,4];case 2:t.sent(),t.label=3;case 3:return++i,[3,1];case 4:return[2]}})}.bind(this)()};var ht,W=w;function w(t,i){void 0===t&&(t=[]),void 0===i&&(i=4096);var r=ht.call(this)||this,e=(r.u=0,r.T=0,r.C=0,r.q=0,r.D=0,r.m=[],function(){if("number"==typeof t.length)return t.length;if("number"==typeof t.size)return t.size;if("function"==typeof t.size)return t.size();throw new TypeError("Cannot get the length or size of the container")}());r.V=i,r.D=Math.max(Math.ceil(e/r.V),1);for(var n=0;n<r.D;++n)r.m.push(new Array(r.V));var i=Math.ceil(e/r.V),o=(r.u=r.C=(r.D>>1)-(i>>1),r.T=r.q=r.V-e%r.V>>1,r);return t.forEach(function(t){o.pushBack(t)}),r}g.prototype.L=function(){var t=this;if(1===t.N&&t.F.F===t)t=t.G;else if(t.B)for(t=t.B;t.G;)t=t.G;else{for(var i=t.F;i.B===t;)i=(t=i).F;t=i}return t},g.prototype.O=function(){var t=this;if(t.G){for(t=t.G;t.B;)t=t.B;return t}for(var i=t.F;i.G===t;)i=(t=i).F;return t.G!==i?i:t},g.prototype.J=function(){var t=this.F,i=this.G,r=i.B;return t.F===this?t.F=i:t.B===this?t.B=i:t.G=i,i.F=t,(i.B=this).F=i,(this.G=r)&&(r.F=this),i},g.prototype.K=function(){var t=this.F,i=this.B,r=i.G;return t.F===this?t.F=i:t.B===this?t.B=i:t.G=i,i.F=t,(i.G=this).F=i,(this.B=r)&&(r.F=this),i};var ut=g;function g(t,i){this.N=1,this.P=void 0,this.k=void 0,this.B=void 0,this.G=void 0,this.F=void 0,this.P=t,this.k=i}i(m,E=ut),m.prototype.J=function(){var t=E.prototype.J.call(this);return this.W(),t.W(),t},m.prototype.K=function(){var t=E.prototype.K.call(this);return this.W(),t.W(),t},m.prototype.W=function(){this.U=1,this.B&&(this.U+=this.B.U),this.G&&(this.U+=this.G.U)};var E,ft=m;function m(){var t=null!==E&&E.apply(this,arguments)||this;return t.U=1,t}i(P,pt=_),P.prototype.rt=function(t,i){for(var r=this.S;t;){var e=this.v(t.P,i);if(e<0)t=t.G;else{if(!(0<e))return t;t=(r=t).B}}return r},P.prototype.et=function(t,i){for(var r=this.S;t;)t=this.v(t.P,i)<=0?t.G:(r=t).B;return r},P.prototype.nt=function(t,i){for(var r=this.S;t;){var e=this.v(t.P,i);if(e<0)t=(r=t).G;else{if(!(0<e))return t;t=t.B}}return r},P.prototype.st=function(t,i){for(var r=this.S;t;)t=this.v(t.P,i)<0?(r=t).G:t.B;return r},P.prototype.ht=function(t){for(;;){var i,r=t.F;if(r===this.S)return;if(1===t.N)return void(t.N=0);if(t===r.B)if(1===(i=r.G).N)i.N=0,r.N=1,r===this.X?this.X=r.J():r.J();else{if(i.G&&1===i.G.N)return i.N=r.N,r.N=0,i.G.N=0,void(r===this.X?this.X=r.J():r.J());i.B&&1===i.B.N?(i.N=1,i.B.N=0,i.K()):(i.N=1,t=r)}else if(1===(i=r.B).N)i.N=0,r.N=1,r===this.X?this.X=r.K():r.K();else{if(i.B&&1===i.B.N)return i.N=r.N,r.N=0,i.B.N=0,void(r===this.X?this.X=r.K():r.K());i.G&&1===i.G.N?(i.N=1,i.G.N=0,i.J()):(i.N=1,t=r)}}},P.prototype.it=function(t){var i;if(1===this.i)return this.clear(),this.S;for(var r=t;r.B||r.G;){if(r.G)for(r=r.G;r.B;)r=r.B;else r=r.B;i=h([r.P,t.P],2),t.P=i[0],r.P=i[1],i=h([r.k,t.k],2),t.k=i[0],r.k=i[1],t=r}this.S.B===r?this.S.B=r.F:this.S.G===r&&(this.S.G=r.F),this.ht(r);var e=r.F;return r===e.B?e.B=void 0:e.G=void 0,--this.i,this.X.N=0,e},P.prototype.ut=function(t,i){return void 0!==t&&(!!this.ut(t.B,i)||!!i(t)||this.ut(t.G,i))},P.prototype.tt=function(t){for(;;){var i=t.F;if(0===i.N)return;var r,e,n=i.F;if(i===n.B){if((r=n.G)&&1===r.N){if(r.N=i.N=0,n===this.X)return;n.N=1,t=n;continue}if(t===i.G)return t.N=0,t.B&&(t.B.F=i),t.G&&(t.G.F=n),i.G=t.B,n.B=t.G,t.B=i,(t.G=n)===this.X?(this.X=t,this.S.F=t):(e=n.F).B===n?e.B=t:e.G=t,t.F=n.F,i.F=t,n.F=t,n.N=1,{parentNode:i,grandParent:n,curNode:t};i.N=0,n===this.X?this.X=n.K():n.K()}else{if((r=n.B)&&1===r.N){if(r.N=i.N=0,n===this.X)return;n.N=1,t=n;continue}if(t===i.B)return t.N=0,t.B&&(t.B.F=n),t.G&&(t.G.F=i),n.G=t.B,i.B=t.G,t.B=n,t.G=i,n===this.X?(this.X=t,this.S.F=t):(e=n.F).B===n?e.B=t:e.G=t,t.F=n.F,i.F=t,n.F=t,n.N=1,{parentNode:i,grandParent:n,curNode:t};i.N=0,n===this.X?this.X=n.J():n.J()}return void(n.N=1)}},P.prototype.$=function(t,i,r){if(void 0===this.X)this.i+=1,this.X=new this.Y(t,i),this.X.N=0,this.X.F=this.S,this.S.F=this.X,this.S.B=this.X,this.S.G=this.X;else{var e,n=this.S.B,o=this.v(n.P,t);if(0!==o){if(0<o)n.B=new this.Y(t,i),e=(n.B.F=n).B,this.S.B=e;else{var o=this.S.G,s=this.v(o.P,t);if(0===s)return void(o.k=i);if(s<0)o.G=new this.Y(t,i),e=(o.G.F=o).G,this.S.G=e;else{if(void 0!==r){s=r.t;if(s!==this.S){o=this.v(s.P,t);if(0===o)return void(s.k=i);if(0<o){r=s.L(),o=this.v(r.P,t);if(0===o)return void(r.k=i);o<0&&(e=new this.Y(t,i),void 0===r.G?(r.G=e).F=r:(s.B=e).F=s)}}}if(void 0===e)for(e=this.X;;){var h=this.v(e.P,t);if(0<h){if(void 0===e.B){e.B=new this.Y(t,i),e=(e.B.F=e).B;break}e=e.B}else{if(!(h<0))return void(e.k=i);if(void 0===e.G){e.G=new this.Y(t,i),e=(e.G.F=e).G;break}e=e.G}}}}return this.i+=1,e}n.k=i}},P.prototype.ot=function(t,i){for(;t;){var r=this.v(t.P,i);if(r<0)t=t.G;else{if(!(0<r))return t;t=t.B}}return t||this.S},P.prototype.clear=function(){this.i=0,this.X=void 0,this.S.F=void 0,this.S.B=this.S.G=void 0},P.prototype.updateKeyByIterator=function(t,i){t=t.t;if(t===this.S&&a(),1!==this.i){if(t===this.S.B)return 0<this.v(t.O().P,i)&&(t.P=i,!0);if(t===this.S.G)return this.v(t.L().P,i)<0&&(t.P=i,!0);var r=t.L().P;if(0<=this.v(r,i))return!1;if(r=t.O().P,this.v(r,i)<=0)return!1}return t.P=i,!0},P.prototype.eraseElementByPos=function(i){if(i<0||i>this.i-1)throw new RangeError;var r=0,e=this;return this.ut(this.X,function(t){return i===r?(e.M(t),!0):(r+=1,!1)}),this.i},P.prototype.eraseElementByKey=function(t){return 0!==this.i&&(t=this.ot(this.X,t))!==this.S&&(this.M(t),!0)},P.prototype.eraseElementByIterator=function(t){var i=t.t,r=(i===this.S&&a(),void 0===i.G);return 0===t.iteratorType?r&&t.next():r&&void 0!==i.B||t.next(),this.M(i),t},P.prototype.forEach=function(t){var i,r,e=0;try{for(var n=u(this),o=n.next();!o.done;o=n.next())t(o.value,e++,this)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}},P.prototype.getElementByPos=function(t){var i,r,e;if(t<0||t>this.i-1)throw new RangeError;var n=0;try{for(var o=u(this),s=o.next();!s.done;s=o.next()){var h=s.value;if(n===t){e=h;break}n+=1}}catch(t){i={error:t}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return e},P.prototype.getHeight=function(){var i;return 0===this.i?0:(i=function(t){return t?Math.max(i(t.B),i(t.G))+1:0})(this.X)};var pt,c=P;function P(t,i){void 0===t&&(t=function(t,i){return t<i?-1:i<t?1:0}),void 0===i&&(i=!1);var r=pt.call(this)||this;return r.X=void 0,r.v=t,i?(r.Y=ft,r.Z=function(t,i,r){t=this.$(t,i,r);if(t){for(var e=t.F;e!==this.S;)e.U+=1,e=e.F;var i=this.tt(t);i&&(r=i.parentNode,t=i.grandParent,i=i.curNode,r.W(),t.W(),i.W())}return this.i},r.M=function(t){for(var i=this.it(t);i!==this.S;)--i.U,i=i.F}):(r.Y=ut,r.Z=function(t,i,r){t=this.$(t,i,r);return t&&this.tt(t),this.i},r.M=r.it),r.S=new r.Y,r}i(yt,ct=e),Object.defineProperty(yt.prototype,"index",{get:function(){var t=this.t,i=this.S.F;if(t===this.S)return i?i.U-1:0;var r=0;for(t.B&&(r+=t.B.U);t!==i;){var e=t.F;t===e.G&&(r+=1,e.B)&&(r+=e.B.U),t=e}return r},enumerable:!1,configurable:!0});var ct,at=yt;function yt(t,i,r){r=ct.call(this,r)||this;return r.t=t,r.S=i,0===r.iteratorType?(r.pre=function(){return this.t===this.S.B&&a(),this.t=this.t.L(),this},r.next=function(){return this.t===this.S&&a(),this.t=this.t.O(),this}):(r.pre=function(){return this.t===this.S.G&&a(),this.t=this.t.O(),this},r.next=function(){return this.t===this.S&&a(),this.t=this.t.L(),this}),r}i(k,lt=at),Object.defineProperty(k.prototype,"pointer",{get:function(){return this.t===this.S&&a(),this.t.P},enumerable:!1,configurable:!0}),k.prototype.copy=function(){return new k(this.t,this.S,this.container,this.iteratorType)};var lt,b=k;function k(t,i,r,e){t=lt.call(this,t,i,e)||this;return t.container=r,t}i(G,vt=c),G.prototype.ft=function(i){return r(this,function(t){switch(t.label){case 0:return void 0===i?[2]:[5,u(this.ft(i.B))];case 1:return t.sent(),[4,i.P];case 2:return t.sent(),[5,u(this.ft(i.G))];case 3:return t.sent(),[2]}})},G.prototype.begin=function(){return new b(this.S.B||this.S,this.S,this)},G.prototype.end=function(){return new b(this.S,this.S,this)},G.prototype.rBegin=function(){return new b(this.S.G||this.S,this.S,this,1)},G.prototype.rEnd=function(){return new b(this.S,this.S,this,1)},G.prototype.front=function(){return this.S.B?this.S.B.P:void 0},G.prototype.back=function(){return this.S.G?this.S.G.P:void 0},G.prototype.insert=function(t,i){return this.Z(t,void 0,i)},G.prototype.find=function(t){t=this.ot(this.X,t);return new b(t,this.S,this)},G.prototype.lowerBound=function(t){t=this.rt(this.X,t);return new b(t,this.S,this)},G.prototype.upperBound=function(t){t=this.et(this.X,t);return new b(t,this.S,this)},G.prototype.reverseLowerBound=function(t){t=this.nt(this.X,t);return new b(t,this.S,this)},G.prototype.reverseUpperBound=function(t){t=this.st(this.X,t);return new b(t,this.S,this)},G.prototype.union=function(t){var i=this;return t.forEach(function(t){i.insert(t)}),this.i},G.prototype[Symbol.iterator]=function(){return this.ft(this.X)};var vt,dt=G;function G(t,i,r){void 0===t&&(t=[]);var i=vt.call(this,i,r)||this,e=i;return t.forEach(function(t){e.insert(t)}),i}i(F,St=at),Object.defineProperty(F.prototype,"pointer",{get:function(){this.t===this.S&&a();var e=this;return new Proxy([],{get:function(t,i){return"0"===i?e.t.P:"1"===i?e.t.k:void 0},set:function(t,i,r){if("1"!==i)throw new TypeError("props must be 1");return e.t.k=r,!0}})},enumerable:!1,configurable:!0}),F.prototype.copy=function(){return new F(this.t,this.S,this.container,this.iteratorType)};var St,O=F;function F(t,i,r,e){t=St.call(this,t,i,e)||this;return t.container=r,t}i(N,Bt=c),N.prototype.ft=function(i){return r(this,function(t){switch(t.label){case 0:return void 0===i?[2]:[5,u(this.ft(i.B))];case 1:return t.sent(),[4,[i.P,i.k]];case 2:return t.sent(),[5,u(this.ft(i.G))];case 3:return t.sent(),[2]}})},N.prototype.begin=function(){return new O(this.S.B||this.S,this.S,this)},N.prototype.end=function(){return new O(this.S,this.S,this)},N.prototype.rBegin=function(){return new O(this.S.G||this.S,this.S,this,1)},N.prototype.rEnd=function(){return new O(this.S,this.S,this,1)},N.prototype.front=function(){var t;if(0!==this.i)return[(t=this.S.B).P,t.k]},N.prototype.back=function(){var t;if(0!==this.i)return[(t=this.S.G).P,t.k]},N.prototype.lowerBound=function(t){t=this.rt(this.X,t);return new O(t,this.S,this)},N.prototype.upperBound=function(t){t=this.et(this.X,t);return new O(t,this.S,this)},N.prototype.reverseLowerBound=function(t){t=this.nt(this.X,t);return new O(t,this.S,this)},N.prototype.reverseUpperBound=function(t){t=this.st(this.X,t);return new O(t,this.S,this)},N.prototype.setElement=function(t,i,r){return this.Z(t,i,r)},N.prototype.find=function(t){t=this.ot(this.X,t);return new O(t,this.S,this)},N.prototype.getElementByKey=function(t){return this.ot(this.X,t).k},N.prototype.union=function(t){var i=this;return t.forEach(function(t){i.setElement(t[0],t[1])}),this.i},N.prototype[Symbol.iterator]=function(){return this.ft(this.X)};var Bt,at=N;function N(t,i,r){void 0===t&&(t=[]);var i=Bt.call(this,i,r)||this,e=i;return t.forEach(function(t){e.setElement(t[0],t[1])}),i}function wt(t){var i=typeof t;return"object"==i&&null!==t||"function"==i}i(Et,gt=e);var gt,c=Et;function Et(t,i,r){r=gt.call(this,r)||this;return r.t=t,r.S=i,0===r.iteratorType?(r.pre=function(){return this.t.L===this.S&&a(),this.t=this.t.L,this},r.next=function(){return this.t===this.S&&a(),this.t=this.t.O,this}):(r.pre=function(){return this.t.O===this.S&&a(),this.t=this.t.O,this},r.next=function(){return this.t===this.S&&a(),this.t=this.t.L,this}),r}i(H,mt=_),H.prototype.M=function(t){var i=t.L,r=t.O;(i.O=r).L=i,t===this.H&&(this.H=r),t===this.g&&(this.g=i),--this.i},H.prototype.Z=function(t,i,r){var e;if(r=void 0===r?wt(t):r){r=t[this.HASH_TAG];if(void 0!==r)return this.ct[r].k=i,this.i;Object.defineProperty(t,this.HASH_TAG,{value:this.ct.length,configurable:!0}),e={P:t,k:i,L:this.g,O:this.S},this.ct.push(e)}else{r=this.vt[t];if(r)return r.k=i,this.i;e={P:t,k:i,L:this.g,O:this.S},this.vt[t]=e}return 0===this.i?(this.H=e,this.S.O=e):this.g.O=e,this.g=e,this.S.L=e,++this.i},H.prototype.ot=function(t,i){return(i=void 0===i?wt(t):i)?void 0===(i=t[this.HASH_TAG])?this.S:this.ct[i]:this.vt[t]||this.S},H.prototype.clear=function(){var i=this.HASH_TAG;this.ct.forEach(function(t){delete t.P[i]}),this.ct=[],this.vt={},Object.setPrototypeOf(this.vt,null),this.i=0,this.H=this.g=this.S.L=this.S.O=this.S},H.prototype.eraseElementByKey=function(t,i){var r;if(i=void 0===i?wt(t):i){i=t[this.HASH_TAG];if(void 0===i)return!1;delete t[this.HASH_TAG],r=this.ct[i],delete this.ct[i]}else{if(void 0===(r=this.vt[t]))return!1;delete this.vt[t]}return this.M(r),!0},H.prototype.eraseElementByIterator=function(t){var i=t.t;return i===this.S&&a(),this.M(i),t.next()},H.prototype.eraseElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;for(var i=this.H;t--;)i=i.O;return this.M(i),this.i};var mt,e=H;function H(){var t=mt.call(this)||this;return t.ct=[],t.vt={},t.HASH_TAG=Symbol("@@HASH_TAG"),Object.setPrototypeOf(t.vt,null),t.S={},t.S.L=t.S.O=t.H=t.g=t.S,t}i(x,Pt=c),Object.defineProperty(x.prototype,"pointer",{get:function(){return this.t===this.S&&a(),this.t.P},enumerable:!1,configurable:!0}),x.prototype.copy=function(){return new x(this.t,this.S,this.container,this.iteratorType)};var Pt,T=x;function x(t,i,r,e){t=Pt.call(this,t,i,e)||this;return t.container=r,t}i(X,bt=e),X.prototype.begin=function(){return new T(this.H,this.S,this)},X.prototype.end=function(){return new T(this.S,this.S,this)},X.prototype.rBegin=function(){return new T(this.g,this.S,this,1)},X.prototype.rEnd=function(){return new T(this.S,this.S,this,1)},X.prototype.front=function(){return this.H.P},X.prototype.back=function(){return this.g.P},X.prototype.insert=function(t,i){return this.Z(t,void 0,i)},X.prototype.getElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;for(var i=this.H;t--;)i=i.O;return i.P},X.prototype.find=function(t,i){t=this.ot(t,i);return new T(t,this.S,this)},X.prototype.forEach=function(t){for(var i=0,r=this.H;r!==this.S;)t(r.P,i++,this),r=r.O},X.prototype[Symbol.iterator]=function(){return function(){var i;return r(this,function(t){switch(t.label){case 0:i=this.H,t.label=1;case 1:return i===this.S?[3,3]:[4,i.P];case 2:return t.sent(),i=i.O,[3,1];case 3:return[2]}})}.bind(this)()};var bt,_=X;function X(t){void 0===t&&(t=[]);var i=bt.call(this)||this,r=i;return t.forEach(function(t){r.insert(t)}),i}i(Gt,kt=c),Object.defineProperty(Gt.prototype,"pointer",{get:function(){this.t===this.S&&a();var e=this;return new Proxy([],{get:function(t,i){return"0"===i?e.t.P:"1"===i?e.t.k:void 0},set:function(t,i,r){if("1"!==i)throw new TypeError("props must be 1");return e.t.k=r,!0}})},enumerable:!1,configurable:!0}),Gt.prototype.copy=function(){return new Gt(this.t,this.S,this.container,this.iteratorType)};var kt,I=Gt;function Gt(t,i,r,e){t=kt.call(this,t,i,e)||this;return t.container=r,t}i(A,Ot=e),A.prototype.begin=function(){return new I(this.H,this.S,this)},A.prototype.end=function(){return new I(this.S,this.S,this)},A.prototype.rBegin=function(){return new I(this.g,this.S,this,1)},A.prototype.rEnd=function(){return new I(this.S,this.S,this,1)},A.prototype.front=function(){if(0!==this.i)return[this.H.P,this.H.k]},A.prototype.back=function(){if(0!==this.i)return[this.g.P,this.g.k]},A.prototype.setElement=function(t,i,r){return this.Z(t,i,r)},A.prototype.getElementByKey=function(t,i){return(i=void 0===i?wt(t):i)?void 0!==(i=t[this.HASH_TAG])?this.ct[i].k:void 0:(i=this.vt[t])?i.k:void 0},A.prototype.getElementByPos=function(t){if(t<0||t>this.i-1)throw new RangeError;for(var i=this.H;t--;)i=i.O;return[i.P,i.k]},A.prototype.find=function(t,i){t=this.ot(t,i);return new I(t,this.S,this)},A.prototype.forEach=function(t){for(var i=0,r=this.H;r!==this.S;)t([r.P,r.k],i++,this),r=r.O},A.prototype[Symbol.iterator]=function(){return function(){var i;return r(this,function(t){switch(t.label){case 0:i=this.H,t.label=1;case 1:return i===this.S?[3,3]:[4,[i.P,i.k]];case 2:return t.sent(),i=i.O,[3,1];case 3:return[2]}})}.bind(this)()};var Ot,c=A;function A(t){void 0===t&&(t=[]);var i=Ot.call(this)||this,r=i;return t.forEach(function(t){r.setElement(t[0],t[1])}),i}t.Deque=W,t.HashMap=c,t.HashSet=_,t.LinkList=nt,t.OrderedMap=at,t.OrderedSet=dt,t.PriorityQueue=n,t.Queue=K,t.Stack=C,t.Vector=it,Object.defineProperty(t,"dt",{value:!0})});
//# sourceMappingURL=js-sdsl.min.js.map

{"version": 3, "sources": ["container/HashContainer/HashMap.js", "../../src/container/HashContainer/HashMap.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "step", "op", "done", "value", "pop", "length", "push", "e", "HashC<PERSON>r", "HashContainerIterator", "checkObject", "throwIteratorAccessError", "HashMapIterator", "_super", "node", "header", "container", "iteratorType", "_this", "defineProperty", "get", "_node", "_header", "self", "Proxy", "props", "_key", "_value", "set", "newValue", "enumerable", "configurable", "copy", "HashMap", "for<PERSON>ach", "el", "setElement", "begin", "_head", "end", "rBegin", "_tail", "rEnd", "front", "_length", "back", "key", "isObject", "_set", "getElement<PERSON>y<PERSON>ey", "undefined", "index", "HASH_TAG", "_objMap", "_originMap", "getElementByPos", "pos", "RangeError", "_next", "find", "_findElementNode", "callback", "_a", "bind"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,cAAejB,QAAQA,KAAKiB,KAAgB,SAAUC,GAASC;IAC/D,IAAIC,IAAI;QAAEC,OAAO;QAAGC,MAAM;YAAa,IAAIC,EAAE,KAAK,GAAG,MAAMA,EAAE;YAAI,OAAOA,EAAE;AAAI;QAAGC,MAAM;QAAIC,KAAK;OAAMC,GAAGC,GAAGJ,GAAGK;IAC/G,OAAOA,IAAI;QAAEC,MAAMC,KAAK;QAAIC,OAASD,KAAK;QAAIE,QAAUF,KAAK;cAAaG,WAAW,eAAeL,EAAEK,OAAOC,YAAY;QAAa,OAAOlC;AAAM,QAAI4B;IACvJ,SAASE,KAAKK;QAAK,OAAO,SAAUC;YAAK,OAAOC,KAAK,EAACF,GAAGC;AAAK;AAAG;IACjE,SAASC,KAAKC;QACV,IAAIZ,GAAG,MAAM,IAAId,UAAU;QAC3B,OAAOQ;YACH,IAAIM,IAAI,GAAGC,MAAMJ,IAAIe,EAAG,KAAK,IAAIX,EAAE,YAAYW,EAAG,KAAKX,EAAE,cAAcJ,IAAII,EAAE,cAAcJ,EAAEZ,KAAKgB;YAAI,KAAKA,EAAEE,WAAWN,IAAIA,EAAEZ,KAAKgB,GAAGW,EAAG,KAAKC,MAAM,OAAOhB;YAC3J,IAAII,IAAI,GAAGJ,GAAGe,IAAK,EAACA,EAAG,KAAK,GAAGf,EAAEiB;YACjC,QAAQF,EAAG;cACP,KAAK;cAAG,KAAK;gBAAGf,IAAIe;gBAAI;;cACxB,KAAK;gBAAGlB,EAAEC;gBAAS,OAAO;oBAAEmB,OAAOF,EAAG;oBAAIC,MAAM;;;cAChD,KAAK;gBAAGnB,EAAEC;gBAASM,IAAIW,EAAG;gBAAIA,IAAK,EAAC;gBAAI;;cACxC,KAAK;gBAAGA,IAAKlB,EAAEK,IAAIgB;gBAAOrB,EAAEI,KAAKiB;gBAAO;;cACxC;gBACI,MAAMlB,IAAIH,EAAEI,MAAMD,IAAIA,EAAEmB,SAAS,KAAKnB,EAAEA,EAAEmB,SAAS,QAAQJ,EAAG,OAAO,KAAKA,EAAG,OAAO,IAAI;oBAAElB,IAAI;oBAAG;AAAU;gBAC3G,IAAIkB,EAAG,OAAO,OAAOf,KAAMe,EAAG,KAAKf,EAAE,MAAMe,EAAG,KAAKf,EAAE,KAAM;oBAAEH,EAAEC,QAAQiB,EAAG;oBAAI;AAAO;gBACrF,IAAIA,EAAG,OAAO,KAAKlB,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIA,IAAIe;oBAAI;AAAO;gBACpE,IAAIf,KAAKH,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIH,EAAEK,IAAIkB,KAAKL;oBAAK;AAAO;gBAClE,IAAIf,EAAE,IAAIH,EAAEK,IAAIgB;gBAChBrB,EAAEI,KAAKiB;gBAAO;;YAEtBH,IAAKnB,EAAKR,KAAKO,GAASE;UAC1B,OAAOwB;YAAKN,IAAK,EAAC,GAAGM;YAAIjB,IAAI;AAAG,UAAC;YAAWD,IAAIH,IAAI;AAAG;QACzD,IAAIe,EAAG,KAAK,GAAG,MAAMA,EAAG;QAAI,OAAO;YAAEE,OAAOF,EAAG,KAAKA,EAAG,UAAU;YAAGC,MAAM;;AAC9E;AACJ;;SCxCSM,eAAeC,6BAAqC;;OACtDC,iBAAW;;SAETC,gCAA0B;;AAEnC,IAAAC,kBAAA,SAAAC;IAAoCnD,UAAAkD,iBAAAC;IAElC,SAAAD,gBACEE,GACAC,GACAC,GACAC;QAJF,IAAAC,IAMEL,EAAAvC,KAAAX,MAAMmD,GAAMC,GAAQE,MAAatD;QACjCuD,EAAKF,YAAYA;QDoCb,OAAOE;AACX;ICnCFnD,OAAAoD,eAAIP,gBAAAxC,WAAA,WAAO;QDqCLgD,KCrCN;YACE,IAAIzD,KAAK0D,MAAU1D,KAAK2D,GAAS;gBAC/BX;ADsCM;YCpCR,IAAMY,IAAO5D;YACb,OAAO,IAAI6D,MAAuB,IAAI;gBACpCJ,KAAA,SAAIrC,GAAG0C;oBACL,IAAIA,MAAU,KAAK,OAAOF,EAAKF,EAAMK,QAChC,IAAID,MAAU,KAAK,OAAOF,EAAKF,EAAMM;ADwClC;gBCtCVC,KAAA,SAAI7C,GAAG0C,GAAYI;oBACjB,IAAIJ,MAAU,KAAK;wBACjB,MAAM,IAAIlD,UAAU;ADwCV;oBCtCZgD,EAAKF,EAAMM,IAASE;oBACpB,OAAO;ADwCC;;AAER;QACAC,YAAY;QACZC,cAAc;;ICxCpBnB,gBAAAxC,UAAA4D,OAAA;QACE,OAAO,IAAIpB,gBAAsBjD,KAAK0D,GAAO1D,KAAK2D,GAAS3D,KAAKqD,WAAWrD,KAAKsD;AD2ChF;ICvCJ,OAAAL;AAAA,CAnCA,CAAoCH;;AAuCpC,IAAAwB,UAAA,SAAApB;IAA4BnD,UAAAuE,SAAApB;IAC1B,SAAAoB,QAAYjB;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAA;AAAqC;QAAjD,IAAAE,IACEL,EAAAvC,KAAAX,SAAOA;QACP,IAAM4D,IAAOL;QACbF,EAAUkB,SAAQ,SAAUC;YAC1BZ,EAAKa,WAAWD,EAAG,IAAIA,EAAG;ADyCxB;QACA,OAAOjB;AACX;ICxCFe,QAAA7D,UAAAiE,QAAA;QACE,OAAO,IAAIzB,gBAAsBjD,KAAK2E,GAAO3E,KAAK2D,GAAS3D;AD0C3D;ICxCFsE,QAAA7D,UAAAmE,MAAA;QACE,OAAO,IAAI3B,gBAAsBjD,KAAK2D,GAAS3D,KAAK2D,GAAS3D;AD0C7D;ICxCFsE,QAAA7D,UAAAoE,SAAA;QACE,OAAO,IAAI5B,gBAAsBjD,KAAK8E,GAAO9E,KAAK2D,GAAS3D,MAAI;AD0C/D;ICxCFsE,QAAA7D,UAAAsE,OAAA;QACE,OAAO,IAAI9B,gBAAsBjD,KAAK2D,GAAS3D,KAAK2D,GAAS3D,MAAI;AD0CjE;ICxCFsE,QAAA7D,UAAAuE,QAAA;QACE,IAAIhF,KAAKiF,MAAY,GAAG;QACxB,OAAe,EAACjF,KAAK2E,EAAMZ,GAAM/D,KAAK2E,EAAMX;AD2C5C;ICzCFM,QAAA7D,UAAAyE,OAAA;QACE,IAAIlF,KAAKiF,MAAY,GAAG;QACxB,OAAe,EAACjF,KAAK8E,EAAMf,GAAM/D,KAAK8E,EAAMd;AD4C5C;IClCFM,QAAA7D,UAAAgE,aAAA,SAAWU,GAAQ3C,GAAU4C;QAC3B,OAAOpF,KAAKqF,EAAKF,GAAK3C,GAAO4C;AD4C7B;IClCFd,QAAA7D,UAAA6E,kBAAA,SAAgBH,GAAQC;QACtB,IAAIA,MAAaG,WAAWH,IAAWrC,YAAYoC;QACnD,IAAIC,GAAU;YACZ,IAAMI,IAA0CL,EAAKnF,KAAKyF;YAC1D,OAAOD,MAAUD,YAAYvF,KAAK0F,EAAQF,GAAOxB,IAASuB;AD6CxD;QC3CJ,IAAMpC,IAAOnD,KAAK2F,EAA4BR;QAC9C,OAAOhC,IAAOA,EAAKa,IAASuB;AD6C5B;IC3CFjB,QAAA7D,UAAAmF,kBAAA,SAAgBC;QD6CV,IC5CsBA,IAAG,KAAHA,IAAQ7F,KAAKiF,IAAO,GAhG3C;YAAE,MAAU,IAAIa;AD8If;QC7CJ,IAAI3C,IAAOnD,KAAK2E;QAChB,OAAOkB,KAAO;YACZ1C,IAAOA,EAAK4C;AD+CV;QC7CJ,OAAe,EAAC5C,EAAKY,GAAMZ,EAAKa;AD+ChC;ICtCFM,QAAA7D,UAAAuF,OAAA,SAAKb,GAAQC;QACX,IAAMjC,IAAOnD,KAAKiG,EAAiBd,GAAKC;QACxC,OAAO,IAAInC,gBAAsBE,GAAMnD,KAAK2D,GAAS3D;AD+CrD;IC7CFsE,QAAA7D,UAAA8D,UAAA,SAAQ2B;QACN,IAAIV,IAAQ;QACZ,IAAIrC,IAAOnD,KAAK2E;QAChB,OAAOxB,MAASnD,KAAK2D,GAAS;YAC5BuC,EAAiB,EAAC/C,EAAKY,GAAMZ,EAAKa,KAASwB,KAASxF;YACpDmD,IAAOA,EAAK4C;AD+CV;AACJ;IC7CFzB,QAAA7D,UAACwB,OAAOC,YAAR;QACE,OAAO;YD+CC,IAAIiB;YACJ,OAAOlC,YAAYjB,OAAM,SAAUmG;gBAC/B,QAAQA,EAAG9E;kBACP,KAAK;oBCjDf8B,IAAOnD,KAAK2E;oBDmDEwB,EAAG9E,QAAQ;;kBACf,KAAK;oBACD,MCpDX8B,MAASnD,KAAK2D,IAAO,OAAA,EAAA,GAAA;oBAC1B,OAAA,EAAA,GAAc,EAACR,EAAKY,GAAMZ,EAAKa;;kBDqDnB,KAAK;oBCrDjBmC,EAAA7E;oBACA6B,IAAOA,EAAK4C;oBDuDI,OAAO,EAAC,GAAa;;kBACzB,KAAK;oBAAG,OAAO,EAAC;;AAExB;AACJ,UCzDFK,KAAKpG,KANA;ADgEP;ICxDJ,OAAAsE;AAAA,CA5FA,CAA4BzB;;eA8FbyB", "file": "HashMap.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport { HashContainer, HashContainerIterator } from \"./Base\";\nimport checkObject from \"../../utils/checkObject\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nimport { throwIteratorAccessError } from \"../../utils/throwError\";\nvar HashMapIterator = /** @class */ (function (_super) {\n    __extends(HashMapIterator, _super);\n    function HashMapIterator(node, header, container, iteratorType) {\n        var _this = _super.call(this, node, header, iteratorType) || this;\n        _this.container = container;\n        return _this;\n    }\n    Object.defineProperty(HashMapIterator.prototype, \"pointer\", {\n        get: function () {\n            if (this._node === this._header) {\n                throwIteratorAccessError();\n            }\n            var self = this;\n            return new Proxy([], {\n                get: function (_, props) {\n                    if (props === '0')\n                        return self._node._key;\n                    else if (props === '1')\n                        return self._node._value;\n                },\n                set: function (_, props, newValue) {\n                    if (props !== '1') {\n                        throw new TypeError('props must be 1');\n                    }\n                    self._node._value = newValue;\n                    return true;\n                }\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    HashMapIterator.prototype.copy = function () {\n        return new HashMapIterator(this._node, this._header, this.container, this.iteratorType);\n    };\n    return HashMapIterator;\n}(HashContainerIterator));\nvar HashMap = /** @class */ (function (_super) {\n    __extends(HashMap, _super);\n    function HashMap(container) {\n        if (container === void 0) { container = []; }\n        var _this = _super.call(this) || this;\n        var self = _this;\n        container.forEach(function (el) {\n            self.setElement(el[0], el[1]);\n        });\n        return _this;\n    }\n    HashMap.prototype.begin = function () {\n        return new HashMapIterator(this._head, this._header, this);\n    };\n    HashMap.prototype.end = function () {\n        return new HashMapIterator(this._header, this._header, this);\n    };\n    HashMap.prototype.rBegin = function () {\n        return new HashMapIterator(this._tail, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    HashMap.prototype.rEnd = function () {\n        return new HashMapIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n    };\n    HashMap.prototype.front = function () {\n        if (this._length === 0)\n            return;\n        return [this._head._key, this._head._value];\n    };\n    HashMap.prototype.back = function () {\n        if (this._length === 0)\n            return;\n        return [this._tail._key, this._tail._value];\n    };\n    /**\n     * @description Insert a key-value pair or set value by the given key.\n     * @param key - The key want to insert.\n     * @param value - The value want to set.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns The size of container after setting.\n     */\n    HashMap.prototype.setElement = function (key, value, isObject) {\n        return this._set(key, value, isObject);\n    };\n    /**\n     * @description Get the value of the element of the specified key.\n     * @param key - The key want to search.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @example\n     * const val = container.getElementByKey(1);\n     */\n    HashMap.prototype.getElementByKey = function (key, isObject) {\n        if (isObject === undefined)\n            isObject = checkObject(key);\n        if (isObject) {\n            var index = key[this.HASH_TAG];\n            return index !== undefined ? this._objMap[index]._value : undefined;\n        }\n        var node = this._originMap[key];\n        return node ? node._value : undefined;\n    };\n    HashMap.prototype.getElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var node = this._head;\n        while (pos--) {\n            node = node._next;\n        }\n        return [node._key, node._value];\n    };\n    /**\n     * @description Check key if exist in container.\n     * @param key - The element you want to search.\n     * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n     *                   If a `undefined` value is passed in, the type will be automatically judged.\n     * @returns An iterator pointing to the element if found, or super end if not found.\n     */\n    HashMap.prototype.find = function (key, isObject) {\n        var node = this._findElementNode(key, isObject);\n        return new HashMapIterator(node, this._header, this);\n    };\n    HashMap.prototype.forEach = function (callback) {\n        var index = 0;\n        var node = this._head;\n        while (node !== this._header) {\n            callback([node._key, node._value], index++, this);\n            node = node._next;\n        }\n    };\n    HashMap.prototype[Symbol.iterator] = function () {\n        return function () {\n            var node;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        node = this._head;\n                        _a.label = 1;\n                    case 1:\n                        if (!(node !== this._header)) return [3 /*break*/, 3];\n                        return [4 /*yield*/, [node._key, node._value]];\n                    case 2:\n                        _a.sent();\n                        node = node._next;\n                        return [3 /*break*/, 1];\n                    case 3: return [2 /*return*/];\n                }\n            });\n        }.bind(this)();\n    };\n    return HashMap;\n}(HashContainer));\nexport default HashMap;\n", "import { initContainer, IteratorType } from '@/container/ContainerBase';\nimport { Hash<PERSON>ontainer, HashContainerIterator, HashLinkNode } from '@/container/HashContainer/Base';\nimport checkObject from '@/utils/checkObject';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass HashMapIterator<K, V> extends HashContainerIterator<K, V> {\n  readonly container: HashMap<K, V>;\n  constructor(\n    node: HashLinkNode<K, V>,\n    header: HashLinkNode<K, V>,\n    container: HashMap<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    const self = this;\n    return new Proxy(<[K, V]><unknown>[], {\n      get(_, props: '0' | '1') {\n        if (props === '0') return self._node._key;\n        else if (props === '1') return self._node._value;\n      },\n      set(_, props: '1', newValue: V) {\n        if (props !== '1') {\n          throw new TypeError('props must be 1');\n        }\n        self._node._value = newValue;\n        return true;\n      }\n    });\n  }\n  copy() {\n    return new HashMapIterator<K, V>(this._node, this._header, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: HashMapIterator<K, V>): boolean;\n}\n\nexport type { HashMapIterator };\n\nclass HashMap<K, V> extends HashContainer<K, V> {\n  constructor(container: initContainer<[K, V]> = []) {\n    super();\n    const self = this;\n    container.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n  }\n  begin() {\n    return new HashMapIterator<K, V>(this._head, this._header, this);\n  }\n  end() {\n    return new HashMapIterator<K, V>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new HashMapIterator<K, V>(this._tail, this._header, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new HashMapIterator<K, V>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front() {\n    if (this._length === 0) return;\n    return <[K, V]>[this._head._key, this._head._value];\n  }\n  back() {\n    if (this._length === 0) return;\n    return <[K, V]>[this._tail._key, this._tail._value];\n  }\n  /**\n   * @description Insert a key-value pair or set value by the given key.\n   * @param key - The key want to insert.\n   * @param value - The value want to set.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns The size of container after setting.\n   */\n  setElement(key: K, value: V, isObject?: boolean) {\n    return this._set(key, value, isObject);\n  }\n  /**\n   * @description Get the value of the element of the specified key.\n   * @param key - The key want to search.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @example\n   * const val = container.getElementByKey(1);\n   */\n  getElementByKey(key: K, isObject?: boolean) {\n    if (isObject === undefined) isObject = checkObject(key);\n    if (isObject) {\n      const index = (<Record<symbol, number>><unknown>key)[this.HASH_TAG];\n      return index !== undefined ? this._objMap[index]._value : undefined;\n    }\n    const node = this._originMap[<string><unknown>key];\n    return node ? node._value : undefined;\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    let node = this._head;\n    while (pos--) {\n      node = node._next;\n    }\n    return <[K, V]>[node._key, node._value];\n  }\n  /**\n   * @description Check key if exist in container.\n   * @param key - The element you want to search.\n   * @param isObject - Tell us if the type of inserted key is `object` to improve efficiency.<br/>\n   *                   If a `undefined` value is passed in, the type will be automatically judged.\n   * @returns An iterator pointing to the element if found, or super end if not found.\n   */\n  find(key: K, isObject?: boolean) {\n    const node = this._findElementNode(key, isObject);\n    return new HashMapIterator<K, V>(node, this._header, this);\n  }\n  forEach(callback: (element: [K, V], index: number, hashMap: HashMap<K, V>) => void) {\n    let index = 0;\n    let node = this._head;\n    while (node !== this._header) {\n      callback(<[K, V]>[node._key, node._value], index++, this);\n      node = node._next;\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: HashMap<K, V>) {\n      let node = this._head;\n      while (node !== this._header) {\n        yield <[K, V]>[node._key, node._value];\n        node = node._next;\n      }\n    }.bind(this)();\n  }\n}\n\nexport default HashMap;\n"]}
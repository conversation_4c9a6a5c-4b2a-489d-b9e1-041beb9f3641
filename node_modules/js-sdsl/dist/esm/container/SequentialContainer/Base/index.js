var __extends = this && this.t || function() {
    var extendStatics = function(n, t) {
        extendStatics = Object.setPrototypeOf || {
            __proto__: []
        } instanceof Array && function(n, t) {
            n.__proto__ = t;
        } || function(n, t) {
            for (var e in t) if (Object.prototype.hasOwnProperty.call(t, e)) n[e] = t[e];
        };
        return extendStatics(n, t);
    };
    return function(n, t) {
        if (typeof t !== "function" && t !== null) throw new TypeError("Class extends value " + String(t) + " is not a constructor or null");
        extendStatics(n, t);
        function __() {
            this.constructor = n;
        }
        n.prototype = t === null ? Object.create(t) : (__.prototype = t.prototype, new __);
    };
}();

import { Container } from "../../ContainerBase";

var SequentialContainer = function(n) {
    __extends(SequentialContainer, n);
    function SequentialContainer() {
        return n !== null && n.apply(this, arguments) || this;
    }
    return SequentialContainer;
}(Container);

export default SequentialContainer;
//# sourceMappingURL=index.js.map

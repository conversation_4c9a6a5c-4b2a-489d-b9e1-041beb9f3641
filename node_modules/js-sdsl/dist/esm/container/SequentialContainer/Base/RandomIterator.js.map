{"version": 3, "sources": ["container/SequentialContainer/Base/RandomIterator.js", "../../src/container/SequentialContainer/Base/RandomIterator.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "ContainerIterator", "throwIteratorAccessError", "RandomIterator", "_super", "index", "iteratorType", "_this", "_node", "pre", "next", "container", "size", "defineProperty", "get", "getElementByPos", "set", "newValue", "setElementByPos", "enumerable", "configurable"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;SCApCG,yBAAiC;;SAEjCC,gCAA0B;;AAEnC,IAAAC,iBAAA,SAAAC;IAAgDrB,UAAAoB,gBAAAC;IAS9C,SAAAD,eACEE,GACAC;QAFF,IAAAC,IAIEH,EAAAT,KAAAX,MAAMsB,MAAatB;QACnBuB,EAAKC,IAAQH;QACb,IAAIE,EAAKD,iBAAY,GAA0B;YAC7CC,EAAKE,MAAM;gBACT,IAAIzB,KAAKwB,MAAU,GAAG;oBACpBN;ADOM;gBCLRlB,KAAKwB,KAAS;gBACd,OAAOxB;ADOH;YCLNuB,EAAKG,OAAO;gBACV,IAAI1B,KAAKwB,MAAUxB,KAAK2B,UAAUC,QAAQ;oBACxCV;ADOM;gBCLRlB,KAAKwB,KAAS;gBACd,OAAOxB;ADOH;AACJ,eCNG;YACLuB,EAAKE,MAAM;gBACT,IAAIzB,KAAKwB,MAAUxB,KAAK2B,UAAUC,SAAS,GAAG;oBAC5CV;ADQM;gBCNRlB,KAAKwB,KAAS;gBACd,OAAOxB;ADQH;YCNNuB,EAAKG,OAAO;gBACV,IAAI1B,KAAKwB,OAAW,GAAG;oBACrBN;ADQM;gBCNRlB,KAAKwB,KAAS;gBACd,OAAOxB;ADQH;AACJ;QACA,OAAOuB;AACX;ICPFnB,OAAAyB,eAAIV,eAAAV,WAAA,WAAO;QDSLqB,KCTN;YACE,OAAO9B,KAAK2B,UAAUI,gBAAgB/B,KAAKwB;ADUvC;QACAQ,KCTN,SAAYC;YACVjC,KAAK2B,UAAUO,gBAAgBlC,KAAKwB,GAAOS;ADUvC;QACAE,YAAY;QACZC,cAAc;;ICNtB,OAAAjB;AAAA,CAzDA,CAAgDF;;SDmEvCE", "file": "RandomIterator.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { ContainerIterator } from \"../../ContainerBase\";\nimport { throwIteratorAccessError } from \"../../../utils/throwError\";\nvar RandomIterator = /** @class */ (function (_super) {\n    __extends(RandomIterator, _super);\n    /**\n     * @internal\n     */\n    function RandomIterator(index, iteratorType) {\n        var _this = _super.call(this, iteratorType) || this;\n        _this._node = index;\n        if (_this.iteratorType === 0 /* IteratorType.NORMAL */) {\n            _this.pre = function () {\n                if (this._node === 0) {\n                    throwIteratorAccessError();\n                }\n                this._node -= 1;\n                return this;\n            };\n            _this.next = function () {\n                if (this._node === this.container.size()) {\n                    throwIteratorAccessError();\n                }\n                this._node += 1;\n                return this;\n            };\n        }\n        else {\n            _this.pre = function () {\n                if (this._node === this.container.size() - 1) {\n                    throwIteratorAccessError();\n                }\n                this._node += 1;\n                return this;\n            };\n            _this.next = function () {\n                if (this._node === -1) {\n                    throwIteratorAccessError();\n                }\n                this._node -= 1;\n                return this;\n            };\n        }\n        return _this;\n    }\n    Object.defineProperty(RandomIterator.prototype, \"pointer\", {\n        get: function () {\n            return this.container.getElementByPos(this._node);\n        },\n        set: function (newValue) {\n            this.container.setElementByPos(this._node, newValue);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return RandomIterator;\n}(ContainerIterator));\nexport { RandomIterator };\n", "import { ContainerIterator, IteratorType } from '@/container/ContainerBase';\nimport SequentialContainer from '@/container/SequentialContainer/Base/index';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nexport abstract class RandomIterator<T> extends ContainerIterator<T> {\n  abstract readonly container: SequentialContainer<T>;\n  /**\n   * @internal\n   */\n  _node: number;\n  /**\n   * @internal\n   */\n  protected constructor(\n    index: number,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = index;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node === 0) {\n          throwIteratorAccessError();\n        }\n        this._node -= 1;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === this.container.size()) {\n          throwIteratorAccessError();\n        }\n        this._node += 1;\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node === this.container.size() - 1) {\n          throwIteratorAccessError();\n        }\n        this._node += 1;\n        return this;\n      };\n      this.next = function () {\n        if (this._node === -1) {\n          throwIteratorAccessError();\n        }\n        this._node -= 1;\n        return this;\n      };\n    }\n  }\n  get pointer() {\n    return this.container.getElementByPos(this._node);\n  }\n  set pointer(newValue: T) {\n    this.container.setElementByPos(this._node, newValue);\n  }\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n"]}
{"version": 3, "sources": ["container/SequentialContainer/Base/index.js", "../../src/container/SequentialContainer/Base/index.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "Container", "SequentialContainer", "_super", "apply", "arguments"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;SCApCG,iBAAW;;AAEpB,IAAAC,sBAAA,SAAAC;IAA8CpB,UAAAmB,qBAAAC;IAA9C,SAAAD;QDiBQ,OAAOC,MAAW,QAAQA,EAAOC,MAAMpB,MAAMqB,cAAcrB;AC+CnE;IAAA,OAAAkB;AAAA,CAhEA,CAA8CD;;eAkE/BC", "file": "index.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { Container } from \"../../ContainerBase\";\nvar SequentialContainer = /** @class */ (function (_super) {\n    __extends(Sequential<PERSON>ontainer, _super);\n    function SequentialContainer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return SequentialContainer;\n}(Container));\nexport default SequentialContainer;\n", "import { Container } from '@/container/ContainerBase';\n\nabstract class SequentialContainer<T> extends Container<T> {\n  /**\n   * @description Push the element to the back.\n   * @param element - The element you want to push.\n   * @returns The size of container after pushing.\n   */\n  abstract pushBack(element: T): number;\n  /**\n   * @description Removes the last element.\n   * @returns The element you popped.\n   */\n  abstract popBack(): T | undefined;\n  /**\n   * @description Sets element by position.\n   * @param pos - The position you want to change.\n   * @param element - The element's value you want to update.\n   * @example\n   * container.setElementByPos(-1, 1); // throw a RangeError\n   */\n  abstract setElementByPos(pos: number, element: T): void;\n  /**\n   * @description Removes the elements of the specified value.\n   * @param value - The value you want to remove.\n   * @returns The size of container after erasing.\n   * @example\n   * container.eraseElementByValue(-1);\n   */\n  abstract eraseElementByValue(value: T): number;\n  /**\n   * @description Insert several elements after the specified position.\n   * @param pos - The position you want to insert.\n   * @param element - The element you want to insert.\n   * @param num - The number of elements you want to insert (default 1).\n   * @returns The size of container after inserting.\n   * @example\n   * const container = new Vector([1, 2, 3]);\n   * container.insert(1, 4);  // [1, 4, 2, 3]\n   * container.insert(1, 5, 3); // [1, 5, 5, 5, 4, 2, 3]\n   */\n  abstract insert(pos: number, element: T, num?: number): number;\n  /**\n   * @description Reverses the container.\n   * @example\n   * const container = new Vector([1, 2, 3]);\n   * container.reverse(); // [3, 2, 1]\n   */\n  abstract reverse(): void;\n  /**\n   * @description Removes the duplication of elements in the container.\n   * @returns The size of container after inserting.\n   * @example\n   * const container = new Vector([1, 1, 3, 2, 2, 5, 5, 2]);\n   * container.unique(); // [1, 3, 2, 5, 2]\n   */\n  abstract unique(): number;\n  /**\n   * @description Sort the container.\n   * @param cmp - Comparison function to sort.\n   * @example\n   * const container = new Vector([3, 1, 10]);\n   * container.sort();  // [1, 10, 3]\n   * container.sort((x, y) => x - y); // [1, 3, 10]\n   */\n  abstract sort(cmp?: (x: T, y: T) => number): void;\n}\n\nexport default SequentialContainer;\n"]}
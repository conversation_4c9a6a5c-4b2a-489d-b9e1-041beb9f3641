{"version": 3, "sources": ["container/SequentialContainer/Deque.js", "../../src/container/SequentialContainer/Deque.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "step", "op", "done", "value", "pop", "length", "push", "e", "__read", "o", "m", "i", "r", "ar", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "l", "slice", "concat", "SequentialContainer", "RandomIterator", "DequeIterator", "_super", "node", "container", "iteratorType", "_this", "copy", "_node", "<PERSON><PERSON>", "_bucketSize", "_first", "_curFirst", "_last", "_curLast", "_bucketNum", "_map", "_length", "size", "Math", "max", "ceil", "needBucketNum", "self", "for<PERSON>ach", "element", "pushBack", "_reAllocate", "newMap", "addBucketNum", "_getElementIndex", "pos", "offset", "offsetRemainder", "curNodePointerIndex", "curNodeBucketIndex", "clear", "begin", "end", "rBegin", "rEnd", "front", "back", "popBack", "pushFront", "popFront", "getElementByPos", "RangeError", "_a", "setElementByPos", "insert", "num", "arr", "cut", "eraseElementByPos", "self_1", "el", "eraseElementByValue", "eraseElementByIterator", "iter", "find", "reverse", "tmp", "unique", "index", "pre", "cur", "sort", "cmp", "shrinkToFit", "callback", "bind"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;AAe7C,IAAIG,cAAejB,QAAQA,KAAKiB,KAAgB,SAAUC,GAASC;IAC/D,IAAIC,IAAI;QAAEC,OAAO;QAAGC,MAAM;YAAa,IAAIC,EAAE,KAAK,GAAG,MAAMA,EAAE;YAAI,OAAOA,EAAE;AAAI;QAAGC,MAAM;QAAIC,KAAK;OAAMC,GAAGC,GAAGJ,GAAGK;IAC/G,OAAOA,IAAI;QAAEC,MAAMC,KAAK;QAAIC,OAASD,KAAK;QAAIE,QAAUF,KAAK;cAAaG,WAAW,eAAeL,EAAEK,OAAOC,YAAY;QAAa,OAAOlC;AAAM,QAAI4B;IACvJ,SAASE,KAAKK;QAAK,OAAO,SAAUC;YAAK,OAAOC,KAAK,EAACF,GAAGC;AAAK;AAAG;IACjE,SAASC,KAAKC;QACV,IAAIZ,GAAG,MAAM,IAAId,UAAU;QAC3B,OAAOQ;YACH,IAAIM,IAAI,GAAGC,MAAMJ,IAAIe,EAAG,KAAK,IAAIX,EAAE,YAAYW,EAAG,KAAKX,EAAE,cAAcJ,IAAII,EAAE,cAAcJ,EAAEZ,KAAKgB;YAAI,KAAKA,EAAEE,WAAWN,IAAIA,EAAEZ,KAAKgB,GAAGW,EAAG,KAAKC,MAAM,OAAOhB;YAC3J,IAAII,IAAI,GAAGJ,GAAGe,IAAK,EAACA,EAAG,KAAK,GAAGf,EAAEiB;YACjC,QAAQF,EAAG;cACP,KAAK;cAAG,KAAK;gBAAGf,IAAIe;gBAAI;;cACxB,KAAK;gBAAGlB,EAAEC;gBAAS,OAAO;oBAAEmB,OAAOF,EAAG;oBAAIC,MAAM;;;cAChD,KAAK;gBAAGnB,EAAEC;gBAASM,IAAIW,EAAG;gBAAIA,IAAK,EAAC;gBAAI;;cACxC,KAAK;gBAAGA,IAAKlB,EAAEK,IAAIgB;gBAAOrB,EAAEI,KAAKiB;gBAAO;;cACxC;gBACI,MAAMlB,IAAIH,EAAEI,MAAMD,IAAIA,EAAEmB,SAAS,KAAKnB,EAAEA,EAAEmB,SAAS,QAAQJ,EAAG,OAAO,KAAKA,EAAG,OAAO,IAAI;oBAAElB,IAAI;oBAAG;AAAU;gBAC3G,IAAIkB,EAAG,OAAO,OAAOf,KAAMe,EAAG,KAAKf,EAAE,MAAMe,EAAG,KAAKf,EAAE,KAAM;oBAAEH,EAAEC,QAAQiB,EAAG;oBAAI;AAAO;gBACrF,IAAIA,EAAG,OAAO,KAAKlB,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIA,IAAIe;oBAAI;AAAO;gBACpE,IAAIf,KAAKH,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIH,EAAEK,IAAIkB,KAAKL;oBAAK;AAAO;gBAClE,IAAIf,EAAE,IAAIH,EAAEK,IAAIgB;gBAChBrB,EAAEI,KAAKiB;gBAAO;;YAEtBH,IAAKnB,EAAKR,KAAKO,GAASE;UAC1B,OAAOwB;YAAKN,IAAK,EAAC,GAAGM;YAAIjB,IAAI;AAAG,UAAC;YAAWD,IAAIH,IAAI;AAAG;QACzD,IAAIe,EAAG,KAAK,GAAG,MAAMA,EAAG;QAAI,OAAO;YAAEE,OAAOF,EAAG,KAAKA,EAAG,UAAU;YAAGC,MAAM;;AAC9E;AACJ;;AACA,IAAIM,SAAU7C,QAAQA,KAAK6C,KAAW,SAAUC,GAAGX;IAC/C,IAAIY,WAAWd,WAAW,cAAca,EAAEb,OAAOC;IACjD,KAAKa,GAAG,OAAOD;IACf,IAAIE,IAAID,EAAEpC,KAAKmC,IAAIG,GAAGC,IAAK,IAAIN;IAC/B;QACI,QAAQT,WAAW,KAAKA,MAAM,QAAQc,IAAID,EAAEnB,QAAQU,MAAMW,EAAGP,KAAKM,EAAET;AAQxE,MANA,OAAOW;QAASP,IAAI;YAAEO,OAAOA;;AAAS,MAAC;QAEnC;YACI,IAAIF,MAAMA,EAAEV,SAASQ,IAAIC,EAAE,YAAYD,EAAEpC,KAAKqC;AAElB,UAD/B;YACS,IAAIJ,GAAG,MAAMA,EAAEO;AAAO;AACpC;IACA,OAAOD;AACX;;AACA,IAAIE,gBAAiBpD,QAAQA,KAAKoD,KAAkB,SAAUC,GAAIC,GAAMC;IACpE,IAAIA,KAAQC,UAAUd,WAAW,GAAG,KAAK,IAAIM,IAAI,GAAGS,IAAIH,EAAKZ,QAAQQ,GAAIF,IAAIS,GAAGT,KAAK;QACjF,IAAIE,OAAQF,KAAKM,IAAO;YACpB,KAAKJ,GAAIA,IAAK3C,MAAME,UAAUiD,MAAM/C,KAAK2C,GAAM,GAAGN;YAClDE,EAAGF,KAAKM,EAAKN;AACjB;AACJ;IACA,OAAOK,EAAGM,OAAOT,KAAM3C,MAAME,UAAUiD,MAAM/C,KAAK2C;AACtD;;OClEOM,yBAAyB;;SAEvBC,sBAAgB;;AAIzB,IAAAC,gBAAA,SAAAC;IAA+BhE,UAAA+D,eAAAC;IAE7B,SAAAD,cAAYE,GAAcC,GAAqBC;QAA/C,IAAAC,IACEJ,EAAApD,KAAAX,MAAMgE,GAAME,MAAalE;QACzBmE,EAAKF,YAAYA;QDkEb,OAAOE;AACX;ICjEFL,cAAArD,UAAA2D,OAAA;QACE,OAAO,IAAIN,cAAiB9D,KAAKqE,GAAOrE,KAAKiE,WAAWjE,KAAKkE;ADmE7D;IC/DJ,OAAAJ;AAAA,CAXA,CAA+BD;;AAe/B,IAAAS,QAAA,SAAAP;IAAuBhE,UAAAuE,OAAAP;IA6BrB,SAAAO,MAAYL,GAAkCM;QAAlC,IAAAN,WAAA,GAAA;YAAAA,IAAA;AAAgC;QAAE,IAAAM,WAAA,GAAA;YAAAA,IAAe,KAAK;AAAG;QAArE,IAAAJ,IACEJ,EAAApD,KAAAX,SAAOA;QA1BDmE,EAAAK,IAAS;QAITL,EAAAM,IAAY;QAIZN,EAAAO,IAAQ;QAIRP,EAAAQ,IAAW;QAIXR,EAAAS,IAAa;QAQbT,EAAAU,IAAc;QAGpB,IAAMC,IAAO;YD8DL,WC/G6Cb,EACvDvB,WAAM,UAAc,OAgDiBuB,EAhDCvB;YDgH5B,WChHgDuB,EAEtDc,SAAI,UAAc,OA8Ced,EA9CGc;YDgH9B,WC/GHd,EAAWc,SAAI,YAChB,OA4C+Bd,EA5Cbc;YAAO,MAAQ,IAAInE,UAAU;ADiH/C,SCrES;QACbuD,EAAKI,IAAcA;QACnBJ,EAAKS,IAAaI,KAAKC,IAAID,KAAKE,KAAKJ,IAAUX,EAAKI,IAAc;QAClE,KAAK,IAAIvB,IAAI,GAAGA,IAAImB,EAAKS,KAAc5B,GAAG;YACxCmB,EAAKU,EAAKlC,KAAK,IAAIpC,MAAM4D,EAAKI;ADsE5B;QCpEJ,IAAMY,IAAgBH,KAAKE,KAAKJ,IAAUX,EAAKI;QAC/CJ,EAAKK,IAASL,EAAKO,KAASP,EAAKS,KAAc,MAAMO,KAAiB;QACtEhB,EAAKM,IAAYN,EAAKQ,IAAYR,EAAKI,IAAcO,IAAUX,EAAKI,KAAgB;QACpF,IAAMa,IAAOjB;QACbF,EAAUoB,SAAQ,SAAUC;YAC1BF,EAAKG,SAASD;ADsEZ;QACA,OAAOnB;AACX;ICjEMG,MAAA7D,UAAA+E,IAAR;QACE,IAAMC,IAAS;QACf,IAAMC,IAAeV,KAAKC,IAAIjF,KAAK4E,KAAc,GAAG;QACpD,KAAK,IAAI5B,IAAI,GAAGA,IAAI0C,KAAgB1C,GAAG;YACrCyC,EAAOzC,KAAK,IAAIzC,MAAMP,KAAKuE;ADuEzB;QCrEJ,KAAK,IAAIvB,IAAIhD,KAAKwE,GAAQxB,IAAIhD,KAAK4E,KAAc5B,GAAG;YAClDyC,EAAOA,EAAO/C,UAAU1C,KAAK6E,EAAK7B;ADuEhC;QCrEJ,KAAK,IAAIA,IAAI,GAAGA,IAAIhD,KAAK0E,KAAS1B,GAAG;YACnCyC,EAAOA,EAAO/C,UAAU1C,KAAK6E,EAAK7B;ADuEhC;QCrEJyC,EAAOA,EAAO/C,UAAOU,cAAA,IAAAP,OAAO7C,KAAK6E,EAAK7E,KAAK0E,KAAM;QACjD1E,KAAKwE,IAASkB;QACd1F,KAAK0E,IAAQe,EAAO/C,SAAS;QAC7B,KAAK,IAAIM,IAAI,GAAGA,IAAI0C,KAAgB1C,GAAG;YACrCyC,EAAOA,EAAO/C,UAAU,IAAInC,MAAMP,KAAKuE;ADuErC;QCrEJvE,KAAK6E,IAAOY;QACZzF,KAAK4E,IAAaa,EAAO/C;ADuEzB;IChEM4B,MAAA7D,UAAAkF,IAAR,SAAyBC;QACvB,IAAMC,IAAS7F,KAAKyE,IAAYmB,IAAM;QACtC,IAAME,IAAkBD,IAAS7F,KAAKuE;QACtC,IAAIwB,IAAsBD,IAAkB;QAC5C,IAAIE,IAAqBhG,KAAKwE,KAAUqB,IAASC,KAAmB9F,KAAKuE;QACzE,IAAIuB,MAAoB,GAAGE,KAAsB;QACjDA,KAAsBhG,KAAK4E;QAC3B,IAAImB,IAAsB,GAAGA,KAAuB/F,KAAKuE;QACzD,OAAO;YAAEyB,oBAAkBA;YAAED,qBAAmBA;;ADyEhD;ICvEFzB,MAAA7D,UAAAwF,QAAA;QACEjG,KAAK6E,IAAO,EAAC,IAAItE,MAAMP,KAAKuE;QAC5BvE,KAAK4E,IAAa;QAClB5E,KAAKwE,IAASxE,KAAK0E,IAAQ1E,KAAK8E,IAAU;QAC1C9E,KAAKyE,IAAYzE,KAAK2E,IAAW3E,KAAKuE,KAAe;ADyErD;ICvEFD,MAAA7D,UAAAyF,QAAA;QACE,OAAO,IAAIpC,cAAiB,GAAG9D;ADyE/B;ICvEFsE,MAAA7D,UAAA0F,MAAA;QACE,OAAO,IAAIrC,cAAiB9D,KAAK8E,GAAS9E;ADyE1C;ICvEFsE,MAAA7D,UAAA2F,SAAA;QACE,OAAO,IAAItC,cAAiB9D,KAAK8E,IAAU,GAAG9E,MAAI;ADyElD;ICvEFsE,MAAA7D,UAAA4F,OAAA;QACE,OAAO,IAAIvC,eAAkB,GAAG9D,MAAI;ADyEpC;ICvEFsE,MAAA7D,UAAA6F,QAAA;QACE,IAAItG,KAAK8E,MAAY,GAAG;QACxB,OAAO9E,KAAK6E,EAAK7E,KAAKwE,GAAQxE,KAAKyE;AD0EnC;ICxEFH,MAAA7D,UAAA8F,OAAA;QACE,IAAIvG,KAAK8E,MAAY,GAAG;QACxB,OAAO9E,KAAK6E,EAAK7E,KAAK0E,GAAO1E,KAAK2E;AD2ElC;ICzEFL,MAAA7D,UAAA8E,WAAA,SAASD;QACP,IAAItF,KAAK8E,GAAS;YAChB,IAAI9E,KAAK2E,IAAW3E,KAAKuE,IAAc,GAAG;gBACxCvE,KAAK2E,KAAY;AD2Eb,mBC1EC,IAAI3E,KAAK0E,IAAQ1E,KAAK4E,IAAa,GAAG;gBAC3C5E,KAAK0E,KAAS;gBACd1E,KAAK2E,IAAW;AD4EZ,mBC3EC;gBACL3E,KAAK0E,IAAQ;gBACb1E,KAAK2E,IAAW;AD6EZ;YC3EN,IACE3E,KAAK0E,MAAU1E,KAAKwE,KACpBxE,KAAK2E,MAAa3E,KAAKyE,GACvBzE,KAAKwF;AD4EL;QC1EJxF,KAAK8E,KAAW;QAChB9E,KAAK6E,EAAK7E,KAAK0E,GAAO1E,KAAK2E,KAAYW;QACvC,OAAOtF,KAAK8E;AD4EZ;IC1EFR,MAAA7D,UAAA+F,UAAA;QACE,IAAIxG,KAAK8E,MAAY,GAAG;QACxB,IAAMtC,IAAQxC,KAAK6E,EAAK7E,KAAK0E,GAAO1E,KAAK2E;QACzC,IAAI3E,KAAK8E,MAAY,GAAG;YACtB,IAAI9E,KAAK2E,IAAW,GAAG;gBACrB3E,KAAK2E,KAAY;AD6Eb,mBC5EC,IAAI3E,KAAK0E,IAAQ,GAAG;gBACzB1E,KAAK0E,KAAS;gBACd1E,KAAK2E,IAAW3E,KAAKuE,IAAc;AD8E/B,mBC7EC;gBACLvE,KAAK0E,IAAQ1E,KAAK4E,IAAa;gBAC/B5E,KAAK2E,IAAW3E,KAAKuE,IAAc;AD+E/B;AACJ;QC7EJvE,KAAK8E,KAAW;QAChB,OAAOtC;AD+EP;ICxEF8B,MAAA7D,UAAAgG,YAAA,SAAUnB;QACR,IAAItF,KAAK8E,GAAS;YAChB,IAAI9E,KAAKyE,IAAY,GAAG;gBACtBzE,KAAKyE,KAAa;AD+Ed,mBC9EC,IAAIzE,KAAKwE,IAAS,GAAG;gBAC1BxE,KAAKwE,KAAU;gBACfxE,KAAKyE,IAAYzE,KAAKuE,IAAc;ADgFhC,mBC/EC;gBACLvE,KAAKwE,IAASxE,KAAK4E,IAAa;gBAChC5E,KAAKyE,IAAYzE,KAAKuE,IAAc;ADiFhC;YC/EN,IACEvE,KAAKwE,MAAWxE,KAAK0E,KACrB1E,KAAKyE,MAAczE,KAAK2E,GACxB3E,KAAKwF;ADgFL;QC9EJxF,KAAK8E,KAAW;QAChB9E,KAAK6E,EAAK7E,KAAKwE,GAAQxE,KAAKyE,KAAaa;QACzC,OAAOtF,KAAK8E;ADgFZ;IC1EFR,MAAA7D,UAAAiG,WAAA;QACE,IAAI1G,KAAK8E,MAAY,GAAG;QACxB,IAAMtC,IAAQxC,KAAK6E,EAAK7E,KAAKwE,GAAQxE,KAAKyE;QAC1C,IAAIzE,KAAK8E,MAAY,GAAG;YACtB,IAAI9E,KAAKyE,IAAYzE,KAAKuE,IAAc,GAAG;gBACzCvE,KAAKyE,KAAa;ADiFd,mBChFC,IAAIzE,KAAKwE,IAASxE,KAAK4E,IAAa,GAAG;gBAC5C5E,KAAKwE,KAAU;gBACfxE,KAAKyE,IAAY;ADkFb,mBCjFC;gBACLzE,KAAKwE,IAAS;gBACdxE,KAAKyE,IAAY;ADmFb;AACJ;QCjFJzE,KAAK8E,KAAW;QAChB,OAAOtC;ADmFP;ICjFF8B,MAAA7D,UAAAkG,kBAAA,SAAgBf;QDmFV,IClFsBA,IAAG,KAAHA,IAAQ5F,KAAK8E,IAAO,GAlNxB;YAAE,MAAU,IAAI8B;ADsSlC;QCnFE,IAAAC,IAGF7G,KAAK2F,EAAiBC,IAFxBI,IAAkBa,EAAAb,oBAClBD,IAAmBc,EAAAd;QAErB,OAAO/F,KAAK6E,EAAKmB,GAAoBD;ADkFrC;IChFFzB,MAAA7D,UAAAqG,kBAAA,SAAgBlB,GAAaN;QDkFvB,ICjFsBM,IAAG,KAAHA,IAAQ5F,KAAK8E,IAAO,GA1NxB;YAAE,MAAU,IAAI8B;AD6SlC;QClFE,IAAAC,IAGF7G,KAAK2F,EAAiBC,IAFxBI,IAAkBa,EAAAb,oBAClBD,IAAmBc,EAAAd;QAErB/F,KAAK6E,EAAKmB,GAAoBD,KAAuBT;ADiFrD;IC/EFhB,MAAA7D,UAAAsG,SAAA,SAAOnB,GAAaN,GAAY0B;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAA;AAAO;QDkFjC,ICjFsBpB,IAAG,KAAHA,IAAQ5F,KAAK8E,GAlOjB;YAAE,MAAU,IAAI8B;ADqTlC;QClFJ,IAAIhB,MAAQ,GAAG;YACb,OAAOoB,KAAOhH,KAAKyG,UAAUnB;ADqF3B,eCpFG,IAAIM,MAAQ5F,KAAK8E,GAAS;YAC/B,OAAOkC,KAAOhH,KAAKuF,SAASD;ADuF1B,eCtFG;YACL,IAAM2B,IAAW;YACjB,KAAK,IAAIjE,IAAI4C,GAAK5C,IAAIhD,KAAK8E,KAAW9B,GAAG;gBACvCiE,EAAItE,KAAK3C,KAAK2G,gBAAgB3D;ADwF1B;YCtFNhD,KAAKkH,IAAItB,IAAM;YACf,KAAK,IAAI5C,IAAI,GAAGA,IAAIgE,KAAOhE,GAAGhD,KAAKuF,SAASD;YAC5C,KAAK,IAAItC,IAAI,GAAGA,IAAIiE,EAAIvE,UAAUM,GAAGhD,KAAKuF,SAAS0B,EAAIjE;AD0FrD;QCxFJ,OAAOhD,KAAK8E;AD0FZ;ICjFFR,MAAA7D,UAAAyG,MAAA,SAAItB;QACF,IAAIA,IAAM,GAAG;YACX5F,KAAKiG;YACL,OAAO;AD0FL;QCxFE,IAAAY,IAGF7G,KAAK2F,EAAiBC,IAFxBI,IAAkBa,EAAAb,oBAClBD,IAAmBc,EAAAd;QAErB/F,KAAK0E,IAAQsB;QACbhG,KAAK2E,IAAWoB;QAChB/F,KAAK8E,IAAUc,IAAM;QACrB,OAAO5F,KAAK8E;ADuFZ;ICrFFR,MAAA7D,UAAA0G,oBAAA,SAAkBvB;QDuFZ,ICtFsBA,IAAG,KAAHA,IAAQ5F,KAAK8E,IAAO,GAxQxB;YAAE,MAAU,IAAI8B;ADgWlC;QCvFJ,IAAIhB,MAAQ,GAAG5F,KAAK0G,iBACf,IAAId,MAAQ5F,KAAK8E,IAAU,GAAG9E,KAAKwG,gBACnC;YACH,IAAMS,IAAM;YACZ,KAAK,IAAIjE,IAAI4C,IAAM,GAAG5C,IAAIhD,KAAK8E,KAAW9B,GAAG;gBAC3CiE,EAAItE,KAAK3C,KAAK2G,gBAAgB3D;AD2F1B;YCzFNhD,KAAKkH,IAAItB;YACT5F,KAAKwG;YACL,IAAMY,IAAOpH;YACbiH,EAAI5B,SAAQ,SAAUgC;gBACpBD,EAAK7B,SAAS8B;AD2FV;AACJ;QCzFJ,OAAOrH,KAAK8E;AD2FZ;ICzFFR,MAAA7D,UAAA6G,sBAAA,SAAoB9E;QAClB,IAAIxC,KAAK8E,MAAY,GAAG,OAAO;QAC/B,IAAMmC,IAAW;QACjB,KAAK,IAAIjE,IAAI,GAAGA,IAAIhD,KAAK8E,KAAW9B,GAAG;YACrC,IAAMsC,IAAUtF,KAAK2G,gBAAgB3D;YACrC,IAAIsC,MAAY9C,GAAOyE,EAAItE,KAAK2C;AD6F9B;QC3FJ,IAAMR,IAAUmC,EAAIvE;QACpB,KAAK,IAAIM,IAAI,GAAGA,IAAI8B,KAAW9B,GAAGhD,KAAK8G,gBAAgB9D,GAAGiE,EAAIjE;QAC9D,OAAOhD,KAAKkH,IAAIpC,IAAU;AD8F1B;IC5FFR,MAAA7D,UAAA8G,yBAAA,SAAuBC;QACrB,IAAMnD,IAAQmD,EAAKnD;QACnBrE,KAAKmH,kBAAkB9C;QACvBmD,IAAOA,EAAK3F;QACZ,OAAO2F;AD8FP;IC5FFlD,MAAA7D,UAAAgH,OAAA,SAAKnC;QACH,KAAK,IAAItC,IAAI,GAAGA,IAAIhD,KAAK8E,KAAW9B,GAAG;YACrC,IAAIhD,KAAK2G,gBAAgB3D,OAAOsC,GAAS;gBACvC,OAAO,IAAIxB,cAAiBd,GAAGhD;AD8F3B;AACJ;QC5FJ,OAAOA,KAAKmG;AD8FZ;IC5FF7B,MAAA7D,UAAAiH,UAAA;QACE,IAAIjE,IAAI;QAAG,IAAIR,IAAIjD,KAAK8E,IAAU;QAClC,OAAOrB,IAAIR,GAAG;YACZ,IAAM0E,IAAM3H,KAAK2G,gBAAgBlD;YACjCzD,KAAK8G,gBAAgBrD,GAAGzD,KAAK2G,gBAAgB1D;YAC7CjD,KAAK8G,gBAAgB7D,GAAG0E;YACxBlE,KAAK;YACLR,KAAK;AD+FH;AACJ;IC7FFqB,MAAA7D,UAAAmH,SAAA;QACE,IAAI5H,KAAK8E,KAAW,GAAG;YACrB,OAAO9E,KAAK8E;AD+FV;QC7FJ,IAAI+C,IAAQ;QACZ,IAAIC,IAAM9H,KAAK2G,gBAAgB;QAC/B,KAAK,IAAI3D,IAAI,GAAGA,IAAIhD,KAAK8E,KAAW9B,GAAG;YACrC,IAAM+E,IAAM/H,KAAK2G,gBAAgB3D;YACjC,IAAI+E,MAAQD,GAAK;gBACfA,IAAMC;gBACN/H,KAAK8G,gBAAgBe,KAASE;AD+F1B;AACJ;QC7FJ,OAAO/H,KAAK8E,IAAU+C,GAAO7H,KAAKwG;QAClC,OAAOxG,KAAK8E;ADgGZ;IC9FFR,MAAA7D,UAAAuH,OAAA,SAAKC;QACH,IAAMhB,IAAW;QACjB,KAAK,IAAIjE,IAAI,GAAGA,IAAIhD,KAAK8E,KAAW9B,GAAG;YACrCiE,EAAItE,KAAK3C,KAAK2G,gBAAgB3D;ADgG5B;QC9FJiE,EAAIe,KAAKC;QACT,KAAK,IAAIjF,IAAI,GAAGA,IAAIhD,KAAK8E,KAAW9B,GAAGhD,KAAK8G,gBAAgB9D,GAAGiE,EAAIjE;ADiGnE;IC5FFsB,MAAA7D,UAAAyH,cAAA;QACE,IAAIlI,KAAK8E,MAAY,GAAG;QACxB,IAAMmC,IAAW;QACjBjH,KAAKqF,SAAQ,SAAUgC;YACrBJ,EAAItE,KAAK0E;ADkGP;QChGJrH,KAAK4E,IAAaI,KAAKC,IAAID,KAAKE,KAAKlF,KAAK8E,IAAU9E,KAAKuE,IAAc;QACvEvE,KAAK8E,IAAU9E,KAAKwE,IAASxE,KAAK0E,IAAQ1E,KAAKyE,IAAYzE,KAAK2E,IAAW;QAC3E3E,KAAK6E,IAAO;QACZ,KAAK,IAAI7B,IAAI,GAAGA,IAAIhD,KAAK4E,KAAc5B,GAAG;YACxChD,KAAK6E,EAAKlC,KAAK,IAAIpC,MAAMP,KAAKuE;ADkG5B;QChGJ,KAAK,IAAIvB,IAAI,GAAGA,IAAIiE,EAAIvE,UAAUM,GAAGhD,KAAKuF,SAAS0B,EAAIjE;ADmGvD;ICjGFsB,MAAA7D,UAAA4E,UAAA,SAAQ8C;QACN,KAAK,IAAInF,IAAI,GAAGA,IAAIhD,KAAK8E,KAAW9B,GAAG;YACrCmF,EAASnI,KAAK2G,gBAAgB3D,IAAIA,GAAGhD;ADmGnC;AACJ;ICjGFsE,MAAA7D,UAACwB,OAAOC,YAAR;QACE,OAAO;YDmGC,IAAIc;YACJ,OAAO/B,YAAYjB,OAAM,SAAU6G;gBAC/B,QAAQA,EAAGxF;kBACP,KAAK;oBCrGV2B,IAAI;oBDuGK6D,EAAGxF,QAAQ;;kBACf,KAAK;oBACD,MCzGF2B,IAAIhD,KAAK8E,IAAO,OAAA,EAAA,GAAA;oBAC9B,OAAA,EAAA,GAAM9E,KAAK2G,gBAAgB3D;;kBD0Gf,KAAK;oBC1GjB6D,EAAAvF;oBD4GgBuF,EAAGxF,QAAQ;;kBACf,KAAK;sBC9GiB2B;oBDgHlB,OAAO,EAAC,GAAa;;kBACzB,KAAK;oBAAG,OAAO,EAAC;;AAExB;AACJ,UCjHFoF,KAAKpI,KAJA;ADsHP;IChHJ,OAAAsE;AAAA,CAlWA,CAAuBV;;eAoWRU", "file": "Deque.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport SequentialContainer from './Base';\nimport { RandomIterator } from \"./Base/RandomIterator\";\nimport $checkWithinAccessParams from \"../../utils/checkParams.macro\";\nimport $getContainerSize from \"../../utils/getContainerSize.macro\";\nvar DequeIterator = /** @class */ (function (_super) {\n    __extends(DequeIterator, _super);\n    function DequeIterator(node, container, iteratorType) {\n        var _this = _super.call(this, node, iteratorType) || this;\n        _this.container = container;\n        return _this;\n    }\n    DequeIterator.prototype.copy = function () {\n        return new DequeIterator(this._node, this.container, this.iteratorType);\n    };\n    return DequeIterator;\n}(RandomIterator));\nvar Deque = /** @class */ (function (_super) {\n    __extends(Deque, _super);\n    function Deque(container, _bucketSize) {\n        if (container === void 0) { container = []; }\n        if (_bucketSize === void 0) { _bucketSize = (1 << 12); }\n        var _this = _super.call(this) || this;\n        /**\n         * @internal\n         */\n        _this._first = 0;\n        /**\n         * @internal\n         */\n        _this._curFirst = 0;\n        /**\n         * @internal\n         */\n        _this._last = 0;\n        /**\n         * @internal\n         */\n        _this._curLast = 0;\n        /**\n         * @internal\n         */\n        _this._bucketNum = 0;\n        /**\n         * @internal\n         */\n        _this._map = [];\n        var _length = (function () {\n            if (typeof container.length === \"number\")\n                return container.length;\n            if (typeof container.size === \"number\")\n                return container.size;\n            if (typeof container.size === \"function\")\n                return container.size();\n            throw new TypeError(\"Cannot get the length or size of the container\");\n        })();\n        _this._bucketSize = _bucketSize;\n        _this._bucketNum = Math.max(Math.ceil(_length / _this._bucketSize), 1);\n        for (var i = 0; i < _this._bucketNum; ++i) {\n            _this._map.push(new Array(_this._bucketSize));\n        }\n        var needBucketNum = Math.ceil(_length / _this._bucketSize);\n        _this._first = _this._last = (_this._bucketNum >> 1) - (needBucketNum >> 1);\n        _this._curFirst = _this._curLast = (_this._bucketSize - _length % _this._bucketSize) >> 1;\n        var self = _this;\n        container.forEach(function (element) {\n            self.pushBack(element);\n        });\n        return _this;\n    }\n    /**\n     * @description Growth the Deque.\n     * @internal\n     */\n    Deque.prototype._reAllocate = function () {\n        var newMap = [];\n        var addBucketNum = Math.max(this._bucketNum >> 1, 1);\n        for (var i = 0; i < addBucketNum; ++i) {\n            newMap[i] = new Array(this._bucketSize);\n        }\n        for (var i = this._first; i < this._bucketNum; ++i) {\n            newMap[newMap.length] = this._map[i];\n        }\n        for (var i = 0; i < this._last; ++i) {\n            newMap[newMap.length] = this._map[i];\n        }\n        newMap[newMap.length] = __spreadArray([], __read(this._map[this._last]), false);\n        this._first = addBucketNum;\n        this._last = newMap.length - 1;\n        for (var i = 0; i < addBucketNum; ++i) {\n            newMap[newMap.length] = new Array(this._bucketSize);\n        }\n        this._map = newMap;\n        this._bucketNum = newMap.length;\n    };\n    /**\n     * @description Get the bucket position of the element and the pointer position by index.\n     * @param pos - The element's index.\n     * @internal\n     */\n    Deque.prototype._getElementIndex = function (pos) {\n        var offset = this._curFirst + pos + 1;\n        var offsetRemainder = offset % this._bucketSize;\n        var curNodePointerIndex = offsetRemainder - 1;\n        var curNodeBucketIndex = this._first + (offset - offsetRemainder) / this._bucketSize;\n        if (offsetRemainder === 0)\n            curNodeBucketIndex -= 1;\n        curNodeBucketIndex %= this._bucketNum;\n        if (curNodePointerIndex < 0)\n            curNodePointerIndex += this._bucketSize;\n        return { curNodeBucketIndex: curNodeBucketIndex, curNodePointerIndex: curNodePointerIndex };\n    };\n    Deque.prototype.clear = function () {\n        this._map = [new Array(this._bucketSize)];\n        this._bucketNum = 1;\n        this._first = this._last = this._length = 0;\n        this._curFirst = this._curLast = this._bucketSize >> 1;\n    };\n    Deque.prototype.begin = function () {\n        return new DequeIterator(0, this);\n    };\n    Deque.prototype.end = function () {\n        return new DequeIterator(this._length, this);\n    };\n    Deque.prototype.rBegin = function () {\n        return new DequeIterator(this._length - 1, this, 1 /* IteratorType.REVERSE */);\n    };\n    Deque.prototype.rEnd = function () {\n        return new DequeIterator(-1, this, 1 /* IteratorType.REVERSE */);\n    };\n    Deque.prototype.front = function () {\n        if (this._length === 0)\n            return;\n        return this._map[this._first][this._curFirst];\n    };\n    Deque.prototype.back = function () {\n        if (this._length === 0)\n            return;\n        return this._map[this._last][this._curLast];\n    };\n    Deque.prototype.pushBack = function (element) {\n        if (this._length) {\n            if (this._curLast < this._bucketSize - 1) {\n                this._curLast += 1;\n            }\n            else if (this._last < this._bucketNum - 1) {\n                this._last += 1;\n                this._curLast = 0;\n            }\n            else {\n                this._last = 0;\n                this._curLast = 0;\n            }\n            if (this._last === this._first &&\n                this._curLast === this._curFirst)\n                this._reAllocate();\n        }\n        this._length += 1;\n        this._map[this._last][this._curLast] = element;\n        return this._length;\n    };\n    Deque.prototype.popBack = function () {\n        if (this._length === 0)\n            return;\n        var value = this._map[this._last][this._curLast];\n        if (this._length !== 1) {\n            if (this._curLast > 0) {\n                this._curLast -= 1;\n            }\n            else if (this._last > 0) {\n                this._last -= 1;\n                this._curLast = this._bucketSize - 1;\n            }\n            else {\n                this._last = this._bucketNum - 1;\n                this._curLast = this._bucketSize - 1;\n            }\n        }\n        this._length -= 1;\n        return value;\n    };\n    /**\n     * @description Push the element to the front.\n     * @param element - The element you want to push.\n     * @returns The size of queue after pushing.\n     */\n    Deque.prototype.pushFront = function (element) {\n        if (this._length) {\n            if (this._curFirst > 0) {\n                this._curFirst -= 1;\n            }\n            else if (this._first > 0) {\n                this._first -= 1;\n                this._curFirst = this._bucketSize - 1;\n            }\n            else {\n                this._first = this._bucketNum - 1;\n                this._curFirst = this._bucketSize - 1;\n            }\n            if (this._first === this._last &&\n                this._curFirst === this._curLast)\n                this._reAllocate();\n        }\n        this._length += 1;\n        this._map[this._first][this._curFirst] = element;\n        return this._length;\n    };\n    /**\n     * @description Remove the _first element.\n     * @returns The element you popped.\n     */\n    Deque.prototype.popFront = function () {\n        if (this._length === 0)\n            return;\n        var value = this._map[this._first][this._curFirst];\n        if (this._length !== 1) {\n            if (this._curFirst < this._bucketSize - 1) {\n                this._curFirst += 1;\n            }\n            else if (this._first < this._bucketNum - 1) {\n                this._first += 1;\n                this._curFirst = 0;\n            }\n            else {\n                this._first = 0;\n                this._curFirst = 0;\n            }\n        }\n        this._length -= 1;\n        return value;\n    };\n    Deque.prototype.getElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var _a = this._getElementIndex(pos), curNodeBucketIndex = _a.curNodeBucketIndex, curNodePointerIndex = _a.curNodePointerIndex;\n        return this._map[curNodeBucketIndex][curNodePointerIndex];\n    };\n    Deque.prototype.setElementByPos = function (pos, element) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        var _a = this._getElementIndex(pos), curNodeBucketIndex = _a.curNodeBucketIndex, curNodePointerIndex = _a.curNodePointerIndex;\n        this._map[curNodeBucketIndex][curNodePointerIndex] = element;\n    };\n    Deque.prototype.insert = function (pos, element, num) {\n        if (num === void 0) { num = 1; }\n        if (pos < 0 || pos > this._length) {\n            throw new RangeError();\n        }\n        if (pos === 0) {\n            while (num--)\n                this.pushFront(element);\n        }\n        else if (pos === this._length) {\n            while (num--)\n                this.pushBack(element);\n        }\n        else {\n            var arr = [];\n            for (var i = pos; i < this._length; ++i) {\n                arr.push(this.getElementByPos(i));\n            }\n            this.cut(pos - 1);\n            for (var i = 0; i < num; ++i)\n                this.pushBack(element);\n            for (var i = 0; i < arr.length; ++i)\n                this.pushBack(arr[i]);\n        }\n        return this._length;\n    };\n    /**\n     * @description Remove all elements after the specified position (excluding the specified position).\n     * @param pos - The previous position of the first removed element.\n     * @returns The size of the container after cutting.\n     * @example\n     * deque.cut(1); // Then deque's size will be 2. deque -> [0, 1]\n     */\n    Deque.prototype.cut = function (pos) {\n        if (pos < 0) {\n            this.clear();\n            return 0;\n        }\n        var _a = this._getElementIndex(pos), curNodeBucketIndex = _a.curNodeBucketIndex, curNodePointerIndex = _a.curNodePointerIndex;\n        this._last = curNodeBucketIndex;\n        this._curLast = curNodePointerIndex;\n        this._length = pos + 1;\n        return this._length;\n    };\n    Deque.prototype.eraseElementByPos = function (pos) {\n        if (pos < 0 || pos > this._length - 1) {\n            throw new RangeError();\n        }\n        if (pos === 0)\n            this.popFront();\n        else if (pos === this._length - 1)\n            this.popBack();\n        else {\n            var arr = [];\n            for (var i = pos + 1; i < this._length; ++i) {\n                arr.push(this.getElementByPos(i));\n            }\n            this.cut(pos);\n            this.popBack();\n            var self_1 = this;\n            arr.forEach(function (el) {\n                self_1.pushBack(el);\n            });\n        }\n        return this._length;\n    };\n    Deque.prototype.eraseElementByValue = function (value) {\n        if (this._length === 0)\n            return 0;\n        var arr = [];\n        for (var i = 0; i < this._length; ++i) {\n            var element = this.getElementByPos(i);\n            if (element !== value)\n                arr.push(element);\n        }\n        var _length = arr.length;\n        for (var i = 0; i < _length; ++i)\n            this.setElementByPos(i, arr[i]);\n        return this.cut(_length - 1);\n    };\n    Deque.prototype.eraseElementByIterator = function (iter) {\n        var _node = iter._node;\n        this.eraseElementByPos(_node);\n        iter = iter.next();\n        return iter;\n    };\n    Deque.prototype.find = function (element) {\n        for (var i = 0; i < this._length; ++i) {\n            if (this.getElementByPos(i) === element) {\n                return new DequeIterator(i, this);\n            }\n        }\n        return this.end();\n    };\n    Deque.prototype.reverse = function () {\n        var l = 0;\n        var r = this._length - 1;\n        while (l < r) {\n            var tmp = this.getElementByPos(l);\n            this.setElementByPos(l, this.getElementByPos(r));\n            this.setElementByPos(r, tmp);\n            l += 1;\n            r -= 1;\n        }\n    };\n    Deque.prototype.unique = function () {\n        if (this._length <= 1) {\n            return this._length;\n        }\n        var index = 1;\n        var pre = this.getElementByPos(0);\n        for (var i = 1; i < this._length; ++i) {\n            var cur = this.getElementByPos(i);\n            if (cur !== pre) {\n                pre = cur;\n                this.setElementByPos(index++, cur);\n            }\n        }\n        while (this._length > index)\n            this.popBack();\n        return this._length;\n    };\n    Deque.prototype.sort = function (cmp) {\n        var arr = [];\n        for (var i = 0; i < this._length; ++i) {\n            arr.push(this.getElementByPos(i));\n        }\n        arr.sort(cmp);\n        for (var i = 0; i < this._length; ++i)\n            this.setElementByPos(i, arr[i]);\n    };\n    /**\n     * @description Remove as much useless space as possible.\n     */\n    Deque.prototype.shrinkToFit = function () {\n        if (this._length === 0)\n            return;\n        var arr = [];\n        this.forEach(function (el) {\n            arr.push(el);\n        });\n        this._bucketNum = Math.max(Math.ceil(this._length / this._bucketSize), 1);\n        this._length = this._first = this._last = this._curFirst = this._curLast = 0;\n        this._map = [];\n        for (var i = 0; i < this._bucketNum; ++i) {\n            this._map.push(new Array(this._bucketSize));\n        }\n        for (var i = 0; i < arr.length; ++i)\n            this.pushBack(arr[i]);\n    };\n    Deque.prototype.forEach = function (callback) {\n        for (var i = 0; i < this._length; ++i) {\n            callback(this.getElementByPos(i), i, this);\n        }\n    };\n    Deque.prototype[Symbol.iterator] = function () {\n        return function () {\n            var i;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        i = 0;\n                        _a.label = 1;\n                    case 1:\n                        if (!(i < this._length)) return [3 /*break*/, 4];\n                        return [4 /*yield*/, this.getElementByPos(i)];\n                    case 2:\n                        _a.sent();\n                        _a.label = 3;\n                    case 3:\n                        ++i;\n                        return [3 /*break*/, 1];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        }.bind(this)();\n    };\n    return Deque;\n}(SequentialContainer));\nexport default Deque;\n", "import SequentialContainer from './Base';\nimport { IteratorType, initContainer } from '@/container/ContainerBase';\nimport { RandomIterator } from '@/container/SequentialContainer/Base/RandomIterator';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport $getContainerSize from '@/utils/getContainerSize.macro';\n\nclass DequeIterator<T> extends RandomIterator<T> {\n  readonly container: Deque<T>;\n  constructor(node: number, container: Deque<T>, iteratorType?: IteratorType) {\n    super(node, iteratorType);\n    this.container = container;\n  }\n  copy() {\n    return new DequeIterator<T>(this._node, this.container, this.iteratorType);\n  }\n  // @ts-ignore\n  equals(iter: DequeIterator<T>): boolean;\n}\n\nexport type { DequeIterator };\n\nclass Deque<T> extends SequentialContainer<T> {\n  /**\n   * @internal\n   */\n  private _first = 0;\n  /**\n   * @internal\n   */\n  private _curFirst = 0;\n  /**\n   * @internal\n   */\n  private _last = 0;\n  /**\n   * @internal\n   */\n  private _curLast = 0;\n  /**\n   * @internal\n   */\n  private _bucketNum = 0;\n  /**\n   * @internal\n   */\n  private readonly _bucketSize: number;\n  /**\n   * @internal\n   */\n  private _map: T[][] = [];\n  constructor(container: initContainer<T> = [], _bucketSize = (1 << 12)) {\n    super();\n    const _length = $getContainerSize!(container);\n    this._bucketSize = _bucketSize;\n    this._bucketNum = Math.max(Math.ceil(_length / this._bucketSize), 1);\n    for (let i = 0; i < this._bucketNum; ++i) {\n      this._map.push(new Array(this._bucketSize));\n    }\n    const needBucketNum = Math.ceil(_length / this._bucketSize);\n    this._first = this._last = (this._bucketNum >> 1) - (needBucketNum >> 1);\n    this._curFirst = this._curLast = (this._bucketSize - _length % this._bucketSize) >> 1;\n    const self = this;\n    container.forEach(function (element) {\n      self.pushBack(element);\n    });\n  }\n  /**\n   * @description Growth the Deque.\n   * @internal\n   */\n  private _reAllocate() {\n    const newMap = [];\n    const addBucketNum = Math.max(this._bucketNum >> 1, 1);\n    for (let i = 0; i < addBucketNum; ++i) {\n      newMap[i] = new Array(this._bucketSize);\n    }\n    for (let i = this._first; i < this._bucketNum; ++i) {\n      newMap[newMap.length] = this._map[i];\n    }\n    for (let i = 0; i < this._last; ++i) {\n      newMap[newMap.length] = this._map[i];\n    }\n    newMap[newMap.length] = [...this._map[this._last]];\n    this._first = addBucketNum;\n    this._last = newMap.length - 1;\n    for (let i = 0; i < addBucketNum; ++i) {\n      newMap[newMap.length] = new Array(this._bucketSize);\n    }\n    this._map = newMap;\n    this._bucketNum = newMap.length;\n  }\n  /**\n   * @description Get the bucket position of the element and the pointer position by index.\n   * @param pos - The element's index.\n   * @internal\n   */\n  private _getElementIndex(pos: number) {\n    const offset = this._curFirst + pos + 1;\n    const offsetRemainder = offset % this._bucketSize;\n    let curNodePointerIndex = offsetRemainder - 1;\n    let curNodeBucketIndex = this._first + (offset - offsetRemainder) / this._bucketSize;\n    if (offsetRemainder === 0) curNodeBucketIndex -= 1;\n    curNodeBucketIndex %= this._bucketNum;\n    if (curNodePointerIndex < 0) curNodePointerIndex += this._bucketSize;\n    return { curNodeBucketIndex, curNodePointerIndex };\n  }\n  clear() {\n    this._map = [new Array(this._bucketSize)];\n    this._bucketNum = 1;\n    this._first = this._last = this._length = 0;\n    this._curFirst = this._curLast = this._bucketSize >> 1;\n  }\n  begin() {\n    return new DequeIterator<T>(0, this);\n  }\n  end() {\n    return new DequeIterator<T>(this._length, this);\n  }\n  rBegin() {\n    return new DequeIterator<T>(this._length - 1, this, IteratorType.REVERSE);\n  }\n  rEnd() {\n    return new DequeIterator<T>(-1, this, IteratorType.REVERSE);\n  }\n  front(): T | undefined {\n    if (this._length === 0) return;\n    return this._map[this._first][this._curFirst];\n  }\n  back(): T | undefined {\n    if (this._length === 0) return;\n    return this._map[this._last][this._curLast];\n  }\n  pushBack(element: T) {\n    if (this._length) {\n      if (this._curLast < this._bucketSize - 1) {\n        this._curLast += 1;\n      } else if (this._last < this._bucketNum - 1) {\n        this._last += 1;\n        this._curLast = 0;\n      } else {\n        this._last = 0;\n        this._curLast = 0;\n      }\n      if (\n        this._last === this._first &&\n        this._curLast === this._curFirst\n      ) this._reAllocate();\n    }\n    this._length += 1;\n    this._map[this._last][this._curLast] = element;\n    return this._length;\n  }\n  popBack() {\n    if (this._length === 0) return;\n    const value = this._map[this._last][this._curLast];\n    if (this._length !== 1) {\n      if (this._curLast > 0) {\n        this._curLast -= 1;\n      } else if (this._last > 0) {\n        this._last -= 1;\n        this._curLast = this._bucketSize - 1;\n      } else {\n        this._last = this._bucketNum - 1;\n        this._curLast = this._bucketSize - 1;\n      }\n    }\n    this._length -= 1;\n    return value;\n  }\n  /**\n   * @description Push the element to the front.\n   * @param element - The element you want to push.\n   * @returns The size of queue after pushing.\n   */\n  pushFront(element: T) {\n    if (this._length) {\n      if (this._curFirst > 0) {\n        this._curFirst -= 1;\n      } else if (this._first > 0) {\n        this._first -= 1;\n        this._curFirst = this._bucketSize - 1;\n      } else {\n        this._first = this._bucketNum - 1;\n        this._curFirst = this._bucketSize - 1;\n      }\n      if (\n        this._first === this._last &&\n        this._curFirst === this._curLast\n      ) this._reAllocate();\n    }\n    this._length += 1;\n    this._map[this._first][this._curFirst] = element;\n    return this._length;\n  }\n  /**\n   * @description Remove the _first element.\n   * @returns The element you popped.\n   */\n  popFront() {\n    if (this._length === 0) return;\n    const value = this._map[this._first][this._curFirst];\n    if (this._length !== 1) {\n      if (this._curFirst < this._bucketSize - 1) {\n        this._curFirst += 1;\n      } else if (this._first < this._bucketNum - 1) {\n        this._first += 1;\n        this._curFirst = 0;\n      } else {\n        this._first = 0;\n        this._curFirst = 0;\n      }\n    }\n    this._length -= 1;\n    return value;\n  }\n  getElementByPos(pos: number): T {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    const {\n      curNodeBucketIndex,\n      curNodePointerIndex\n    } = this._getElementIndex(pos);\n    return this._map[curNodeBucketIndex][curNodePointerIndex]!;\n  }\n  setElementByPos(pos: number, element: T) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    const {\n      curNodeBucketIndex,\n      curNodePointerIndex\n    } = this._getElementIndex(pos);\n    this._map[curNodeBucketIndex][curNodePointerIndex] = element;\n  }\n  insert(pos: number, element: T, num = 1) {\n    $checkWithinAccessParams!(pos, 0, this._length);\n    if (pos === 0) {\n      while (num--) this.pushFront(element);\n    } else if (pos === this._length) {\n      while (num--) this.pushBack(element);\n    } else {\n      const arr: T[] = [];\n      for (let i = pos; i < this._length; ++i) {\n        arr.push(this.getElementByPos(i));\n      }\n      this.cut(pos - 1);\n      for (let i = 0; i < num; ++i) this.pushBack(element);\n      for (let i = 0; i < arr.length; ++i) this.pushBack(arr[i]);\n    }\n    return this._length;\n  }\n  /**\n   * @description Remove all elements after the specified position (excluding the specified position).\n   * @param pos - The previous position of the first removed element.\n   * @returns The size of the container after cutting.\n   * @example\n   * deque.cut(1); // Then deque's size will be 2. deque -> [0, 1]\n   */\n  cut(pos: number) {\n    if (pos < 0) {\n      this.clear();\n      return 0;\n    }\n    const {\n      curNodeBucketIndex,\n      curNodePointerIndex\n    } = this._getElementIndex(pos);\n    this._last = curNodeBucketIndex;\n    this._curLast = curNodePointerIndex;\n    this._length = pos + 1;\n    return this._length;\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    if (pos === 0) this.popFront();\n    else if (pos === this._length - 1) this.popBack();\n    else {\n      const arr = [];\n      for (let i = pos + 1; i < this._length; ++i) {\n        arr.push(this.getElementByPos(i));\n      }\n      this.cut(pos);\n      this.popBack();\n      const self = this;\n      arr.forEach(function (el) {\n        self.pushBack(el);\n      });\n    }\n    return this._length;\n  }\n  eraseElementByValue(value: T) {\n    if (this._length === 0) return 0;\n    const arr: T[] = [];\n    for (let i = 0; i < this._length; ++i) {\n      const element = this.getElementByPos(i);\n      if (element !== value) arr.push(element);\n    }\n    const _length = arr.length;\n    for (let i = 0; i < _length; ++i) this.setElementByPos(i, arr[i]);\n    return this.cut(_length - 1);\n  }\n  eraseElementByIterator(iter: DequeIterator<T>) {\n    const _node = iter._node;\n    this.eraseElementByPos(_node);\n    iter = iter.next();\n    return iter;\n  }\n  find(element: T) {\n    for (let i = 0; i < this._length; ++i) {\n      if (this.getElementByPos(i) === element) {\n        return new DequeIterator<T>(i, this);\n      }\n    }\n    return this.end();\n  }\n  reverse() {\n    let l = 0; let r = this._length - 1;\n    while (l < r) {\n      const tmp = this.getElementByPos(l);\n      this.setElementByPos(l, this.getElementByPos(r));\n      this.setElementByPos(r, tmp);\n      l += 1;\n      r -= 1;\n    }\n  }\n  unique() {\n    if (this._length <= 1) {\n      return this._length;\n    }\n    let index = 1;\n    let pre = this.getElementByPos(0);\n    for (let i = 1; i < this._length; ++i) {\n      const cur = this.getElementByPos(i);\n      if (cur !== pre) {\n        pre = cur;\n        this.setElementByPos(index++, cur);\n      }\n    }\n    while (this._length > index) this.popBack();\n    return this._length;\n  }\n  sort(cmp?: (x: T, y: T) => number) {\n    const arr: T[] = [];\n    for (let i = 0; i < this._length; ++i) {\n      arr.push(this.getElementByPos(i));\n    }\n    arr.sort(cmp);\n    for (let i = 0; i < this._length; ++i) this.setElementByPos(i, arr[i]);\n  }\n  /**\n   * @description Remove as much useless space as possible.\n   */\n  shrinkToFit() {\n    if (this._length === 0) return;\n    const arr: T[] = [];\n    this.forEach(function (el) {\n      arr.push(el);\n    });\n    this._bucketNum = Math.max(Math.ceil(this._length / this._bucketSize), 1);\n    this._length = this._first = this._last = this._curFirst = this._curLast = 0;\n    this._map = [];\n    for (let i = 0; i < this._bucketNum; ++i) {\n      this._map.push(new Array(this._bucketSize));\n    }\n    for (let i = 0; i < arr.length; ++i) this.pushBack(arr[i]);\n  }\n  forEach(callback: (element: T, index: number, deque: Deque<T>) => void) {\n    for (let i = 0; i < this._length; ++i) {\n      callback(this.getElementByPos(i), i, this);\n    }\n  }\n  [Symbol.iterator]() {\n    return function * (this: Deque<T>) {\n      for (let i = 0; i < this._length; ++i) {\n        yield this.getElementByPos(i);\n      }\n    }.bind(this)();\n  }\n}\n\nexport default Deque;\n"]}
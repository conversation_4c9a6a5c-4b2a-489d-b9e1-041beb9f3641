{"version": 3, "sources": ["container/TreeContainer/Base/TreeIterator.js", "../../src/container/TreeContainer/Base/TreeIterator.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "ContainerIterator", "throwIteratorAccessError", "TreeIterator", "_super", "node", "header", "iteratorType", "_this", "_node", "_header", "pre", "_left", "_pre", "next", "_next", "_right", "defineProperty", "get", "root", "_parent", "_subTreeSize", "index", "enumerable", "configurable"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;SCEpCG,yBAAiC;;SAEjCC,gCAA0B;;AAEnC,IAAAC,eAAA,SAAAC;IAA0CrB,UAAAoB,cAAAC;IAaxC,SAAAD,aACEE,GACAC,GACAC;QAHF,IAAAC,IAKEJ,EAAAT,KAAAX,MAAMuB,MAAavB;QACnBwB,EAAKC,IAAQJ;QACbG,EAAKE,IAAUJ;QACf,IAAIE,EAAKD,iBAAY,GAA0B;YAC7CC,EAAKG,MAAM;gBACT,IAAI3B,KAAKyB,MAAUzB,KAAK0B,EAAQE,GAAO;oBACrCV;ADAM;gBCERlB,KAAKyB,IAAQzB,KAAKyB,EAAMI;gBACxB,OAAO7B;ADAH;YCGNwB,EAAKM,OAAO;gBACV,IAAI9B,KAAKyB,MAAUzB,KAAK0B,GAAS;oBAC/BR;ADDM;gBCGRlB,KAAKyB,IAAQzB,KAAKyB,EAAMM;gBACxB,OAAO/B;ADDH;AACJ,eCEG;YACLwB,EAAKG,MAAM;gBACT,IAAI3B,KAAKyB,MAAUzB,KAAK0B,EAAQM,GAAQ;oBACtCd;ADAM;gBCERlB,KAAKyB,IAAQzB,KAAKyB,EAAMM;gBACxB,OAAO/B;ADAH;YCGNwB,EAAKM,OAAO;gBACV,IAAI9B,KAAKyB,MAAUzB,KAAK0B,GAAS;oBAC/BR;ADDM;gBCGRlB,KAAKyB,IAAQzB,KAAKyB,EAAMI;gBACxB,OAAO7B;ADDH;AACJ;QACA,OAAOwB;AACX;ICWFpB,OAAA6B,eAAId,aAAAV,WAAA,SAAK;QDAHyB,KCAN;YACE,IAAIT,IAAQzB,KAAKyB;YACjB,IAAMU,IAAOnC,KAAK0B,EAAQU;YAC1B,IAAIX,MAAUzB,KAAK0B,GAAS;gBAC1B,IAAIS,GAAM;oBACR,OAAOA,EAAKE,KAAe;ADCnB;gBCCV,OAAO;ADCD;YCCR,IAAIC,IAAQ;YACZ,IAAIb,EAAMG,GAAO;gBACfU,KAAUb,EAAMG,EAAoCS;ADC9C;YCCR,OAAOZ,MAAUU,GAAM;gBACrB,IAAMC,IAAUX,EAAMW;gBACtB,IAAIX,MAAUW,EAAQJ,GAAQ;oBAC5BM,KAAS;oBACT,IAAIF,EAAQR,GAAO;wBACjBU,KAAUF,EAAQR,EAAoCS;ADC5C;AACJ;gBCCVZ,IAAQW;ADCF;YCCR,OAAOE;ADCH;QACAC,YAAY;QACZC,cAAc;;ICGtB,OAAArB;AAAA,CA7FA,CAA0CF;;eA+F3BE", "file": "TreeIterator.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { ContainerIterator } from \"../../ContainerBase\";\nimport { throwIteratorAccessError } from \"../../../utils/throwError\";\nvar TreeIterator = /** @class */ (function (_super) {\n    __extends(TreeIterator, _super);\n    /**\n     * @internal\n     */\n    function TreeIterator(node, header, iteratorType) {\n        var _this = _super.call(this, iteratorType) || this;\n        _this._node = node;\n        _this._header = header;\n        if (_this.iteratorType === 0 /* IteratorType.NORMAL */) {\n            _this.pre = function () {\n                if (this._node === this._header._left) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre();\n                return this;\n            };\n            _this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next();\n                return this;\n            };\n        }\n        else {\n            _this.pre = function () {\n                if (this._node === this._header._right) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._next();\n                return this;\n            };\n            _this.next = function () {\n                if (this._node === this._header) {\n                    throwIteratorAccessError();\n                }\n                this._node = this._node._pre();\n                return this;\n            };\n        }\n        return _this;\n    }\n    Object.defineProperty(TreeIterator.prototype, \"index\", {\n        /**\n         * @description Get the sequential index of the iterator in the tree container.<br/>\n         *              <strong>Note:</strong>\n         *              This function only takes effect when the specified tree container `enableIndex = true`.\n         * @returns The index subscript of the node in the tree.\n         * @example\n         * const st = new OrderedSet([1, 2, 3], true);\n         * console.log(st.begin().next().index);  // 1\n         */\n        get: function () {\n            var _node = this._node;\n            var root = this._header._parent;\n            if (_node === this._header) {\n                if (root) {\n                    return root._subTreeSize - 1;\n                }\n                return 0;\n            }\n            var index = 0;\n            if (_node._left) {\n                index += _node._left._subTreeSize;\n            }\n            while (_node !== root) {\n                var _parent = _node._parent;\n                if (_node === _parent._right) {\n                    index += 1;\n                    if (_parent._left) {\n                        index += _parent._left._subTreeSize;\n                    }\n                }\n                _node = _parent;\n            }\n            return index;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return TreeIterator;\n}(ContainerIterator));\nexport default TreeIterator;\n", "import { TreeNode } from './TreeNode';\nimport type { TreeNodeEnableIndex } from './TreeNode';\nimport { ContainerIterator, IteratorType } from '@/container/ContainerBase';\nimport TreeContainer from '@/container/TreeContainer/Base/index';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nabstract class TreeIterator<K, V> extends ContainerIterator<K | [K, V]> {\n  abstract readonly container: TreeContainer<K, V>;\n  /**\n   * @internal\n   */\n  _node: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _header: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected constructor(\n    node: TreeNode<K, V>,\n    header: TreeNode<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = node;\n    this._header = header;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node === this._header._left) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node === this._header._right) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n    }\n  }\n  /**\n   * @description Get the sequential index of the iterator in the tree container.<br/>\n   *              <strong>Note:</strong>\n   *              This function only takes effect when the specified tree container `enableIndex = true`.\n   * @returns The index subscript of the node in the tree.\n   * @example\n   * const st = new OrderedSet([1, 2, 3], true);\n   * console.log(st.begin().next().index);  // 1\n   */\n  get index() {\n    let _node = this._node as TreeNodeEnableIndex<K, V>;\n    const root = this._header._parent as TreeNodeEnableIndex<K, V>;\n    if (_node === this._header) {\n      if (root) {\n        return root._subTreeSize - 1;\n      }\n      return 0;\n    }\n    let index = 0;\n    if (_node._left) {\n      index += (_node._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n    while (_node !== root) {\n      const _parent = _node._parent as TreeNodeEnableIndex<K, V>;\n      if (_node === _parent._right) {\n        index += 1;\n        if (_parent._left) {\n          index += (_parent._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n        }\n      }\n      _node = _parent;\n    }\n    return index;\n  }\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n\nexport default TreeIterator;\n"]}
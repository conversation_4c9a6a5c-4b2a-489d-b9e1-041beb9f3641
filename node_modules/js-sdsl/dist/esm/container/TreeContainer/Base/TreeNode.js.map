{"version": 3, "sources": ["container/TreeContainer/Base/TreeNode.js", "../../src/container/TreeContainer/Base/TreeNode.ts"], "names": ["__extends", "this", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "TreeNode", "key", "value", "_color", "_key", "undefined", "_value", "_left", "_right", "_parent", "_pre", "preNode", "pre", "_next", "nextNode", "_rotateLeft", "PP", "V", "R", "_rotateRight", "F", "K", "TreeNodeEnableIndex", "_super", "_this", "apply", "arguments", "_subTreeSize", "parent", "_recount"], "mappings": "AAAA,IAAIA,YAAaC,QAAQA,KAAKD,KAAe;IACzC,IAAIE,gBAAgB,SAAUC,GAAGC;QAC7BF,gBAAgBG,OAAOC,kBAClB;YAAEC,WAAW;qBAAgBC,SAAS,SAAUL,GAAGC;YAAKD,EAAEI,YAAYH;AAAG,aAC1E,SAAUD,GAAGC;YAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;AAAI;QACpG,OAAOP,cAAcC,GAAGC;AAC5B;IACA,OAAO,SAAUD,GAAGC;QAChB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIS,UAAU,yBAAyBC,OAAOV,KAAK;QAC7DF,cAAcC,GAAGC;QACjB,SAASW;YAAOd,KAAKe,cAAcb;AAAG;QACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOY,OAAOb,MAAMW,GAAGL,YAAYN,EAAEM,WAAW,IAAIK;AACnF;AACJ,CAd6C;;ACK7C,IAAAG,WAAA;IAOE,SAAAA,SAAYC,GAASC;QANrBnB,KAAAoB,KAAM;QACNpB,KAAAqB,IAAsBC;QACtBtB,KAAAuB,IAAwBD;QACxBtB,KAAAwB,IAAoCF;QACpCtB,KAAAyB,IAAqCH;QACrCtB,KAAA0B,KAAsCJ;QAEpCtB,KAAKqB,IAAOH;QACZlB,KAAKuB,IAASJ;ADWd;ICLFF,SAAAR,UAAAkB,IAAA;QACE,IAAIC,IAA0B5B;QAC9B,IACE4B,EAAQR,OAAM,KACdQ,EAAQF,GAASA,OAAYE,GAC7B;YACAA,IAAUA,EAAQH;ADShB,eCRG,IAAIG,EAAQJ,GAAO;YACxBI,IAAUA,EAAQJ;YAClB,OAAOI,EAAQH,GAAQ;gBACrBG,IAAUA,EAAQH;ADUd;AACJ,eCTG;YACL,IAAII,IAAMD,EAAQF;YAClB,OAAOG,EAAIL,MAAUI,GAAS;gBAC5BA,IAAUC;gBACVA,IAAMD,EAAQF;ADWV;YCTNE,IAAUC;ADWR;QCTJ,OAAOD;ADWP;ICLFX,SAAAR,UAAAqB,IAAA;QACE,IAAIC,IAA2B/B;QAC/B,IAAI+B,EAASN,GAAQ;YACnBM,IAAWA,EAASN;YACpB,OAAOM,EAASP,GAAO;gBACrBO,IAAWA,EAASP;ADWhB;YCTN,OAAOO;ADWL,eCVG;YACL,IAAIF,IAAME,EAASL;YACnB,OAAOG,EAAIJ,MAAWM,GAAU;gBAC9BA,IAAWF;gBACXA,IAAME,EAASL;ADYX;YCVN,IAAIK,EAASN,MAAWI,GAAK;gBAC3B,OAAOA;ADYH,mBCXC,OAAOE;ADcZ;AACJ;ICRFd,SAAAR,UAAAuB,KAAA;QACE,IAAMC,IAAKjC,KAAK0B;QAChB,IAAMQ,IAAIlC,KAAKyB;QACf,IAAMU,IAAID,EAAEV;QAEZ,IAAIS,EAAGP,OAAY1B,MAAMiC,EAAGP,KAAUQ,QACjC,IAAID,EAAGT,MAAUxB,MAAMiC,EAAGT,IAAQU,QAClCD,EAAGR,IAASS;QAEjBA,EAAER,KAAUO;QACZC,EAAEV,IAAQxB;QAEVA,KAAK0B,KAAUQ;QACflC,KAAKyB,IAASU;QAEd,IAAIA,GAAGA,EAAET,KAAU1B;QAEnB,OAAOkC;ADaP;ICPFjB,SAAAR,UAAA2B,KAAA;QACE,IAAMH,IAAKjC,KAAK0B;QAChB,IAAMW,IAAIrC,KAAKwB;QACf,IAAMc,IAAID,EAAEZ;QAEZ,IAAIQ,EAAGP,OAAY1B,MAAMiC,EAAGP,KAAUW,QACjC,IAAIJ,EAAGT,MAAUxB,MAAMiC,EAAGT,IAAQa,QAClCJ,EAAGR,IAASY;QAEjBA,EAAEX,KAAUO;QACZI,EAAEZ,IAASzB;QAEXA,KAAK0B,KAAUW;QACfrC,KAAKwB,IAAQc;QAEb,IAAIA,GAAGA,EAAEZ,KAAU1B;QAEnB,OAAOqC;ADYP;ICVJ,OAAApB;AAAA,CA1GA;;SDuHSA;;ACXT,IAAAsB,sBAAA,SAAAC;IAA+CzC,UAAAwC,qBAAAC;IAA/C,SAAAD;QAAA,IAAAE,IAAAD,MAAA,QAAAA,EAAAE,MAAA1C,MAAA2C,cAAA3C;QACEyC,EAAAG,KAAe;QDgBT,OAAOH;ACcf;IAzBEF,oBAAA9B,UAAAuB,KAAA;QACE,IAAMa,IAASL,EAAA/B,UAAMuB,GAAWrB,KAAAX;QAChCA,KAAK8C;QACLD,EAAOC;QACP,OAAOD;ADkBP;ICZFN,oBAAA9B,UAAA2B,KAAA;QACE,IAAMS,IAASL,EAAA/B,UAAM2B,GAAYzB,KAAAX;QACjCA,KAAK8C;QACLD,EAAOC;QACP,OAAOD;ADkBP;IChBFN,oBAAA9B,UAAAqC,KAAA;QACE9C,KAAK4C,KAAe;QACpB,IAAI5C,KAAKwB,GAAO;YACdxB,KAAK4C,MAAiB5C,KAAKwB,EAAoCoB;ADkB7D;QChBJ,IAAI5C,KAAKyB,GAAQ;YACfzB,KAAK4C,MAAiB5C,KAAKyB,EAAqCmB;ADkB9D;AACJ;IChBJ,OAAAL;AAAA,CA/BA,CAA+CtB;;SDkDtCsB", "file": "TreeNode.js", "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar TreeNode = /** @class */ (function () {\n    function TreeNode(key, value) {\n        this._color = 1 /* TreeNodeColor.RED */;\n        this._key = undefined;\n        this._value = undefined;\n        this._left = undefined;\n        this._right = undefined;\n        this._parent = undefined;\n        this._key = key;\n        this._value = value;\n    }\n    /**\n     * @description Get the pre node.\n     * @returns TreeNode about the pre node.\n     */\n    TreeNode.prototype._pre = function () {\n        var preNode = this;\n        if (preNode._color === 1 /* TreeNodeColor.RED */ &&\n            preNode._parent._parent === preNode) {\n            preNode = preNode._right;\n        }\n        else if (preNode._left) {\n            preNode = preNode._left;\n            while (preNode._right) {\n                preNode = preNode._right;\n            }\n        }\n        else {\n            var pre = preNode._parent;\n            while (pre._left === preNode) {\n                preNode = pre;\n                pre = preNode._parent;\n            }\n            preNode = pre;\n        }\n        return preNode;\n    };\n    /**\n     * @description Get the next node.\n     * @returns TreeNode about the next node.\n     */\n    TreeNode.prototype._next = function () {\n        var nextNode = this;\n        if (nextNode._right) {\n            nextNode = nextNode._right;\n            while (nextNode._left) {\n                nextNode = nextNode._left;\n            }\n            return nextNode;\n        }\n        else {\n            var pre = nextNode._parent;\n            while (pre._right === nextNode) {\n                nextNode = pre;\n                pre = nextNode._parent;\n            }\n            if (nextNode._right !== pre) {\n                return pre;\n            }\n            else\n                return nextNode;\n        }\n    };\n    /**\n     * @description Rotate left.\n     * @returns TreeNode about moved to original position after rotation.\n     */\n    TreeNode.prototype._rotateLeft = function () {\n        var PP = this._parent;\n        var V = this._right;\n        var R = V._left;\n        if (PP._parent === this)\n            PP._parent = V;\n        else if (PP._left === this)\n            PP._left = V;\n        else\n            PP._right = V;\n        V._parent = PP;\n        V._left = this;\n        this._parent = V;\n        this._right = R;\n        if (R)\n            R._parent = this;\n        return V;\n    };\n    /**\n     * @description Rotate right.\n     * @returns TreeNode about moved to original position after rotation.\n     */\n    TreeNode.prototype._rotateRight = function () {\n        var PP = this._parent;\n        var F = this._left;\n        var K = F._right;\n        if (PP._parent === this)\n            PP._parent = F;\n        else if (PP._left === this)\n            PP._left = F;\n        else\n            PP._right = F;\n        F._parent = PP;\n        F._right = this;\n        this._parent = F;\n        this._left = K;\n        if (K)\n            K._parent = this;\n        return F;\n    };\n    return TreeNode;\n}());\nexport { TreeNode };\nvar TreeNodeEnableIndex = /** @class */ (function (_super) {\n    __extends(TreeNodeEnableIndex, _super);\n    function TreeNodeEnableIndex() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this._subTreeSize = 1;\n        return _this;\n    }\n    /**\n     * @description Rotate left and do recount.\n     * @returns TreeNode about moved to original position after rotation.\n     */\n    TreeNodeEnableIndex.prototype._rotateLeft = function () {\n        var parent = _super.prototype._rotateLeft.call(this);\n        this._recount();\n        parent._recount();\n        return parent;\n    };\n    /**\n     * @description Rotate right and do recount.\n     * @returns TreeNode about moved to original position after rotation.\n     */\n    TreeNodeEnableIndex.prototype._rotateRight = function () {\n        var parent = _super.prototype._rotateRight.call(this);\n        this._recount();\n        parent._recount();\n        return parent;\n    };\n    TreeNodeEnableIndex.prototype._recount = function () {\n        this._subTreeSize = 1;\n        if (this._left) {\n            this._subTreeSize += this._left._subTreeSize;\n        }\n        if (this._right) {\n            this._subTreeSize += this._right._subTreeSize;\n        }\n    };\n    return TreeNodeEnableIndex;\n}(TreeNode));\nexport { TreeNodeEnableIndex };\n", "export const enum TreeNodeColor {\n  RED = 1,\n  BLACK = 0\n}\n\nexport class TreeNode<K, V> {\n  _color = TreeNodeColor.RED;\n  _key: K | undefined = undefined;\n  _value: V | undefined = undefined;\n  _left: TreeNode<K, V> | undefined = undefined;\n  _right: TreeNode<K, V> | undefined = undefined;\n  _parent: TreeNode<K, V> | undefined = undefined;\n  constructor(key?: K, value?: V) {\n    this._key = key;\n    this._value = value;\n  }\n  /**\n   * @description Get the pre node.\n   * @returns TreeNode about the pre node.\n   */\n  _pre() {\n    let preNode: TreeNode<K, V> = this;\n    if (\n      preNode._color === TreeNodeColor.RED &&\n      preNode._parent!._parent === preNode\n    ) {\n      preNode = preNode._right!;\n    } else if (preNode._left) {\n      preNode = preNode._left;\n      while (preNode._right) {\n        preNode = preNode._right;\n      }\n    } else {\n      let pre = preNode._parent!;\n      while (pre._left === preNode) {\n        preNode = pre;\n        pre = preNode._parent!;\n      }\n      preNode = pre;\n    }\n    return preNode;\n  }\n  /**\n   * @description Get the next node.\n   * @returns TreeNode about the next node.\n   */\n  _next() {\n    let nextNode: TreeNode<K, V> = this;\n    if (nextNode._right) {\n      nextNode = nextNode._right;\n      while (nextNode._left) {\n        nextNode = nextNode._left;\n      }\n      return nextNode;\n    } else {\n      let pre = nextNode._parent!;\n      while (pre._right === nextNode) {\n        nextNode = pre;\n        pre = nextNode._parent!;\n      }\n      if (nextNode._right !== pre) {\n        return pre;\n      } else return nextNode;\n    }\n  }\n  /**\n   * @description Rotate left.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const PP = this._parent!;\n    const V = this._right!;\n    const R = V._left;\n\n    if (PP._parent === this) PP._parent = V;\n    else if (PP._left === this) PP._left = V;\n    else PP._right = V;\n\n    V._parent = PP;\n    V._left = this;\n\n    this._parent = V;\n    this._right = R;\n\n    if (R) R._parent = this;\n\n    return V;\n  }\n  /**\n   * @description Rotate right.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const PP = this._parent!;\n    const F = this._left!;\n    const K = F._right;\n\n    if (PP._parent === this) PP._parent = F;\n    else if (PP._left === this) PP._left = F;\n    else PP._right = F;\n\n    F._parent = PP;\n    F._right = this;\n\n    this._parent = F;\n    this._left = K;\n\n    if (K) K._parent = this;\n\n    return F;\n  }\n}\n\nexport class TreeNodeEnableIndex<K, V> extends TreeNode<K, V> {\n  _subTreeSize = 1;\n  /**\n   * @description Rotate left and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const parent = super._rotateLeft() as TreeNodeEnableIndex<K, V>;\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  /**\n   * @description Rotate right and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const parent = super._rotateRight() as TreeNodeEnableIndex<K, V>;\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  _recount() {\n    this._subTreeSize = 1;\n    if (this._left) {\n      this._subTreeSize += (this._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n    if (this._right) {\n      this._subTreeSize += (this._right as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n  }\n}\n"]}
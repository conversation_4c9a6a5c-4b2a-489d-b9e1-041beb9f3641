{"name": "@influxdata/influxdb-client", "version": "1.35.0", "description": "InfluxDB 2.x client", "scripts": {"apidoc:extract": "api-extractor run", "build": "yarn run clean && yarn tsup --config ./tsup.config.browser.ts && yarn tsup", "clean": "rimraf dist build coverage .nyc_output doc *.lcov reports", "coverage": "nyc mocha --require ts-node/register 'test/**/*.test.ts' --exit", "coverage:ci": "yarn run coverage && yarn run coverage:lcov", "coverage:lcov": "yarn run --silent nyc report --reporter=text-lcov > coverage/coverage.lcov", "test": "yarn run lint && yarn run typecheck && yarn run test:all", "test:all": "mocha --require esbuild-runner/register 'test/**/*.test.ts' --exit", "test:unit": "mocha --require esbuild-runner/register 'test/unit/**/*.test.ts' --exit", "test:integration": "mocha --require esbuild-runner/register 'test/integration/**/*.test.ts' --exit", "test:ci": "yarn run lint:ci && yarn run test:all --exit --reporter mocha-junit-reporter --reporter-options mochaFile=../../reports/core_mocha/test-results.xml", "test:watch": "mocha --require esbuild-runner/register 'test/unit/**/*.test.ts' --watch-extensions ts --watch", "typecheck": "tsc --noEmit --pretty", "lint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "lint:ci": "yarn run lint --format junit --output-file ../../reports/core_eslint/eslint.xml", "lint:fix": "eslint --fix 'src/**/*.ts' 'test/**/*.ts'"}, "main": "dist/index.js", "module": "dist/index.mjs", "module:browser": "dist/index.browser.mjs", "browser": "dist/index.browser.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "browser": {"import": "./dist/index.browser.mjs", "require": "./dist/index.browser.js", "script": "./dist/influxdb.js", "default": "./dist/index.browser.js"}, "deno": "./dist/index.browser.mjs", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "homepage": "https://github.com/influxdata/influxdb-client-js", "repository": {"type": "git", "url": "git+https://github.com/influxdata/influxdb-client-js", "directory": "packages/core"}, "keywords": ["influxdb", "influxdata"], "author": {"name": "<PERSON>"}, "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "^7.31.0", "@types/chai": "^4.2.5", "@types/mocha": "^10.0.0", "@types/sinon": "^17.0.2", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^8.0.0", "chai": "^4.2.0", "esbuild": "^0.23.0", "esbuild-runner": "^2.2.1", "eslint": "^8.18.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-tsdoc": "^0.3.0", "follow-redirects": "^1.14.7", "mocha": "^10.0.0", "mocha-junit-reporter": "^2.0.2", "nock": "^13.2.8", "nyc": "^17.0.0", "prettier": "^3.0.3", "rimraf": "^5.0.1", "rxjs": "^7.2.0", "sinon": "^18.0.0", "ts-node": "^10.9.1", "tsup": "^8.0.2", "typescript": "^5.1.3"}, "gitHead": "fe5bc8eb17156b7e93434dc1caaa479f3181ede4"}
{"version": 3, "sources": ["../src/index.ts", "../src/results/chunkCombiner.ts", "../src/results/chunksToLines.ts", "../src/results/chunksToLinesIterable.ts", "../src/results/LineSplitter.ts", "../src/results/FluxTableColumn.ts", "../src/errors.ts", "../src/results/FluxTableMetaData.ts", "../src/results/linesToTables.ts", "../src/results/linesToRowsIterable.ts", "../src/results/stringToLines.ts", "../src/observable/symbol.ts", "../src/results/ObservableQuery.ts", "../src/options.ts", "../src/util/escape.ts", "../src/util/currentTime.ts", "../src/util/logger.ts", "../src/query/flux.ts", "../src/Point.ts", "../src/impl/retryStrategy.ts", "../src/impl/RetryBuffer.ts", "../src/util/utf8Length.ts", "../src/impl/WriteApiImpl.ts", "../src/impl/completeCommunicationObserver.ts", "../src/impl/browser/FetchTransport.ts", "../src/impl/QueryApiImpl.ts", "../src/results/AnnotatedCSVResponseImpl.ts", "../src/InfluxDB.ts"], "sourcesContent": ["/**\n * The `@influxdata/influxdb-client` package provides optimized APIs that write or query InfluxDB v2.\n *\n * @remarks\n * The entry point of this package is the {@link @influxdata/influxdb-client#InfluxDB } class. It is\n * initialized with options that tells how to communicate with InfluxDB. The simple usage pattern is:\n *\n * ```\n * import {InfluxDB} = from('@influxdata/influxdb-client')\n * const influxDB = new InfluxDB({\n *   url: \"http://localhost:8086\",\n *   token: \"your-api-token\"\n * })\n * ```\n *\n * The influxDB object let you create two essential API instances, {@link @influxdata/influxdb-client#InfluxDB.getWriteApi }\n * and {@link @influxdata/influxdb-client#InfluxDB.getQueryApi }. The {@link @influxdata/influxdb-client#WriteApi}\n * asynchronously writes measurement points on background, in batches to optimize network traffic, and with retries\n * upon failures. The {@link @influxdata/influxdb-client#QueryApi} let you execute a flux query against InfluxDB\n * and offers several ways to stream query results.\n *\n * The influxDB object is also used to create more specialized InfluxDB management API instances in\n * {@link @influxdata/influxdb-client-apis# | @influxdata/influxdb-client-apis} .\n *\n * See also {@link https://github.com/influxdata/influxdb-client-js/tree/master/examples | examples} to know more.\n *\n * @packageDocumentation\n */\nexport * from './results'\nexport * from './options'\nexport * from './errors'\nexport * from './util/escape'\nexport * from './util/currentTime'\nexport * from './util/logger'\nexport * from './query'\nexport * from './transport'\nexport * from './observable'\nexport * from './Point'\nexport {default as InfluxDB} from './InfluxDB'\nexport {default as QueryApi, QueryOptions} from './QueryApi'\nexport {default as WriteApi} from './WriteApi'\n", "/**\n * Chunk<PERSON>ombiner is a simplified platform-neutral manipulation of Uint8arrays\n * that allows to process text data on the fly. The implementation can be optimized\n * for the target platform (node vs browser).\n */\nexport interface Chunk<PERSON>omb<PERSON> {\n  /**\n   * Concatenates first and second chunk.\n   * @param first - first chunk\n   * @param second - second chunk\n   * @returns first + second\n   */\n  concat(first: Uint8Array, second: Uint8Array): Uint8Array\n\n  /**\n   * Converts chunk into a string.\n   * @param chunk - chunk\n   * @param start - start index\n   * @param end - end index\n   * @returns string representation of chunk slice\n   */\n  toUtf8String(chunk: Uint8Array, start: number, end: number): string\n\n  /**\n   * Creates a new chunk from the supplied chunk.\n   * @param chunk - chunk to copy\n   * @param start - start index\n   * @param end - end index\n   * @returns a copy of a chunk slice\n   */\n  copy(chunk: Uint8Array, start: number, end: number): Uint8Array\n}\n\n// TextDecoder is available since node v8.3.0 and in all modern browsers\ndeclare class TextDecoder {\n  constructor(encoding: string)\n  decode(chunk: Uint8Array): string\n}\n\n/**\n * Creates a chunk combiner instance that uses UTF-8\n * TextDecoder to decode Uint8Arrays into strings.\n */\nexport function createTextDecoderCombiner(): ChunkCombiner {\n  const decoder = new TextDecoder('utf-8')\n  return {\n    concat(first: Uint8Array, second: Uint8Array): Uint8Array {\n      const retVal = new Uint8Array(first.length + second.length)\n      retVal.set(first)\n      retVal.set(second, first.length)\n      return retVal\n    },\n    copy(chunk: Uint8Array, start: number, end: number): Uint8Array {\n      const retVal = new Uint8Array(end - start)\n      retVal.set(chunk.subarray(start, end))\n      return retVal\n    },\n    toUtf8String(chunk: Uint8Array, start: number, end: number): string {\n      return decoder.decode(chunk.subarray(start, end))\n    },\n  }\n}\n", "import {<PERSON>k<PERSON><PERSON><PERSON>, createTextDecoderCombiner} from './chunkCombiner'\nimport {CommunicationObserver} from './CommunicationObserver'\nimport {Cancellable} from './Cancellable'\n\n/**\n * ChunksToLines is a transformation that accepts Uint8Array instances\n * and emmits strings representing CSV lines.\n * @param target - target to emmit CSV lines to\n * @param chunkCombiner - chunk combiner\n * @returns communication obrver to accept Uint8Arrays\n */\nexport function chunksToLines(\n  target: CommunicationObserver<string>,\n  chunkCombiner?: ChunkCombiner\n): CommunicationObserver<Uint8Array> {\n  const chunks = chunkCombiner ?? createTextDecoderCombiner()\n  let previous: Uint8Array | undefined\n  let finished = false\n  let quoted = false\n  let paused = false\n  let resumeChunks: (() => void) | undefined\n\n  function bufferReceived(chunk: Uint8Array): void {\n    let index: number\n    let start = 0\n    if (previous) {\n      // inspect the whole remaining data upon empty chunk\n      // empty chunk signalizes to restart of receiving\n      index = chunk.length === 0 ? 0 : (previous as Uint8Array).length\n      chunk = chunks.concat(previous, chunk)\n    } else {\n      index = 0\n    }\n    while (index < chunk.length) {\n      const c = chunk[index]\n      if (c === 10) {\n        if (!quoted) {\n          /* do not emit CR+LR or LF line ending */\n          const end = index > 0 && chunk[index - 1] === 13 ? index - 1 : index\n          // do not emmit more lines if the processing is already finished\n          if (finished) {\n            return\n          }\n          paused = target.next(chunks.toUtf8String(chunk, start, end)) === false\n          start = index + 1\n          if (paused) {\n            break\n          }\n        }\n      } else if (c === 34 /* \" */) {\n        quoted = !quoted\n      }\n      index++\n    }\n    if (start < chunk.length) {\n      previous = chunks.copy(chunk, start, chunk.length)\n    } else {\n      previous = undefined\n    }\n    if (paused) {\n      if (target.useResume) {\n        target.useResume(() => {\n          paused = false\n          bufferReceived(new Uint8Array(0))\n        })\n        return\n      }\n      retVal.error(new Error('Unable to pause, useResume is not configured!'))\n      paused = false // consume remaining data\n    }\n    if (resumeChunks) {\n      resumeChunks()\n      resumeChunks = undefined\n    }\n  }\n\n  const retVal: CommunicationObserver<Uint8Array> = {\n    next(chunk: Uint8Array): boolean {\n      if (!finished) {\n        try {\n          bufferReceived(chunk)\n          return !paused\n        } catch (e) {\n          this.error(e as Error)\n        }\n      }\n      return true\n    },\n    error(error: Error): void {\n      if (!finished) {\n        finished = true\n        target.error(error)\n      }\n    },\n    complete(): void {\n      if (!finished) {\n        if (previous) {\n          target.next(chunks.toUtf8String(previous, 0, previous.length))\n        }\n        finished = true\n        target.complete()\n      }\n    },\n  }\n  if (target.useCancellable) {\n    retVal.useCancellable = (cancellable: Cancellable) => {\n      target.useCancellable &&\n        target.useCancellable({\n          cancel(): void {\n            cancellable.cancel()\n            previous = undefined // do not emit more lines\n            retVal.complete()\n          },\n          isCancelled(): boolean {\n            return cancellable.isCancelled()\n          },\n        })\n    }\n  }\n  if (target.useResume) {\n    retVal.useResume = (x: () => void) => {\n      resumeChunks = x\n    }\n  }\n\n  return retVal\n}\n", "import {Chunk<PERSON><PERSON><PERSON>, createTextDecoder<PERSON>ombiner} from './chunkCombiner'\n\n/**\n * ChunksToLinesIterable is a transformation that accepts\n * an iterable of Uint8Array instances and returns iterable of lines.\n * @param source - iterable of transport buffers\n * @param chunkCombiner - chunk combiner\n * @returns iterable of lines\n */\nexport async function* chunksToLinesIterable(\n  source: AsyncIterable<Uint8Array>,\n  chunkCombiner?: ChunkCombiner\n): AsyncIterableIterator<string> {\n  const chunks = chunkCombiner ?? createTextDecoderCombiner()\n  let previous: Uint8Array | undefined\n  let quoted = false\n\n  for await (let chunk of source) {\n    let index: number\n    let start = 0\n    if (previous) {\n      index = previous.length\n      chunk = chunks.concat(previous, chunk)\n    } else {\n      index = 0\n    }\n    while (index < chunk.length) {\n      const c = chunk[index]\n      if (c === 10) {\n        if (!quoted) {\n          /* do not emit CR+LR or LF line ending */\n          const end = index > 0 && chunk[index - 1] === 13 ? index - 1 : index\n          yield chunks.toUtf8String(chunk, start, end)\n          start = index + 1\n        }\n      } else if (c === 34 /* \" */) {\n        quoted = !quoted\n      }\n      index++\n    }\n    if (start < chunk.length) {\n      previous = chunks.copy(chunk, start, chunk.length)\n    } else {\n      previous = undefined\n    }\n  }\n  if (previous) {\n    yield chunks.toUtf8String(previous, 0, previous.length)\n  }\n}\n", "/**\n * Optimized tokenizer of a single CSV line.\n */\nexport class LineSplitter {\n  /** returned value when reused  */\n  reusedValues: string[]\n  /** last length of elements in */\n  lastSplitLength: number\n  private _reuse = false\n\n  /**\n   * Reuse returned array between consecutive calls.\n   */\n  get reuse(): boolean {\n    return this._reuse\n  }\n  set reuse(val: boolean) {\n    if (val && !this.reusedValues) {\n      this.reusedValues = new Array(10)\n    }\n    this._reuse = val\n  }\n\n  /**\n   * Sets the reuse flag and returns this.\n   */\n  withReuse(): LineSplitter {\n    this.reuse = true\n    return this\n  }\n\n  /**\n   * Splits the supplied line to elements that are separated by\n   * comma with values possibly escaped within double quotes (\"value\")\n   * @param line - line\n   * @returns array of splitted parts\n   */\n  splitLine(line: string | undefined | null): string[] {\n    if (line === null || line === undefined) {\n      this.lastSplitLength = 0\n      return []\n    }\n    let quoteCount = 0\n    let startIndex = 0\n    const values = this._reuse ? this.reusedValues : []\n    let count = 0\n    for (let i = 0; i < line.length; i++) {\n      const c = line[i]\n      if (c === ',') {\n        if (quoteCount % 2 === 0) {\n          const val = this.getValue(line, startIndex, i, quoteCount)\n          if (this._reuse) {\n            values[count++] = val\n          } else {\n            values.push(val)\n          }\n          startIndex = i + 1\n          quoteCount = 0\n        }\n      } else if (c === '\"') {\n        quoteCount++\n      }\n    }\n    const val = this.getValue(line, startIndex, line.length, quoteCount)\n    if (this._reuse) {\n      values[count] = val\n      this.lastSplitLength = count + 1\n    } else {\n      values.push(val)\n      this.lastSplitLength = values.length\n    }\n\n    return values\n  }\n\n  private getValue(\n    line: string,\n    start: number,\n    end: number,\n    quoteCount: number\n  ): string {\n    if (start === line.length) {\n      return ''\n    } else if (quoteCount === 0) {\n      return line.substring(start, end)\n    } else if (quoteCount === 2) {\n      return line.substring(start + 1, end - 1)\n    } else {\n      // quoteCount >= 4\n      return line.substring(start + 1, end - 1).replace(/\"\"/gi, '\"')\n    }\n  }\n}\n", "/**\n * Type of query result column, see {@link https://docs.influxdata.com/influxdb/latest/reference/syntax/annotated-csv/#data-types }\n */\nexport type ColumnType =\n  | 'boolean'\n  | 'unsignedLong'\n  | 'long'\n  | 'double'\n  | 'string'\n  | 'base64Binary'\n  | 'dateTime:RFC3339'\n  | 'duration'\n  | string\n\n/**\n * FluxTableColumn describes {@link http://bit.ly/flux-spec#table | flux table} column.\n */\nexport interface FluxTableColumn {\n  /**\n   * Label (e.g., \"_start\", \"_stop\", \"_time\").\n   */\n  label: string\n\n  /**\n   * The data type of column (e.g., \"string\", \"long\", \"dateTime:RFC3339\").\n   */\n  dataType: ColumnType\n\n  /**\n   * Boolean flag indicating if the column is a part of the table's group key.\n   */\n  group: boolean\n\n  /**\n   * Default value to be used for rows whose string value is an empty string.\n   */\n  defaultValue: string\n\n  /**\n   * Index of this column in a row array.\n   */\n  index: number\n\n  /**\n   * Get returns a JavaScript object of this column in the supplied result row, using default deserializers.\n   * @param row - a data row\n   * @returns column value\n   */\n  get: (row: string[]) => any\n}\n\nconst identity = (x: string): any => x\n\n/**\n * A dictionary of serializers of particular types returned by a flux query.\n * See {@link https://docs.influxdata.com/influxdb/latest/reference/syntax/annotated-csv/#data-types }\n */\nexport const typeSerializers: Record<ColumnType, (val: string) => any> = {\n  boolean: (x: string): any => (x === '' ? null : x === 'true'),\n  unsignedLong: (x: string): any => (x === '' ? null : +x),\n  long: (x: string): any => (x === '' ? null : +x),\n  double(x: string): any {\n    switch (x) {\n      case '':\n        return null\n      case '+Inf':\n        return Number.POSITIVE_INFINITY\n      case '-Inf':\n        return Number.NEGATIVE_INFINITY\n      default:\n        return +x\n    }\n  },\n  string: identity,\n  base64Binary: identity,\n  duration: (x: string): any => (x === '' ? null : x),\n  'dateTime:RFC3339': (x: string): any => (x === '' ? null : x),\n}\n\n/**\n * FluxTableColumn implementation.\n */\nclass FluxTableColumnImpl implements FluxTableColumn {\n  label: string\n  dataType: ColumnType\n  group: boolean\n  defaultValue: string\n  index: number\n  public get(row: string[]): any {\n    let val = row[this.index]\n    if ((val === '' || val === undefined) && this.defaultValue) {\n      val = this.defaultValue\n    }\n    return (typeSerializers[this.dataType] ?? identity)(val)\n  }\n}\nexport const UNKNOWN_COLUMN: FluxTableColumn = Object.freeze({\n  label: '',\n  dataType: '',\n  group: false,\n  defaultValue: '',\n  index: Number.MAX_SAFE_INTEGER,\n  get: () => undefined,\n})\n\n/**\n * Creates a new flux table column.\n * @returns column instance\n */\nexport function newFluxTableColumn(): FluxTableColumn {\n  return new FluxTableColumnImpl()\n}\n\n/**\n * Creates a flux table column from a partial FluxTableColumn.\n * @param object - source object\n * @returns column instance\n */\nexport function createFluxTableColumn(\n  object: Partial<FluxTableColumn>\n): FluxTableColumn {\n  const retVal = new FluxTableColumnImpl()\n  retVal.label = String(object.label)\n  retVal.dataType = object.dataType as ColumnType\n  retVal.group = Boolean(object.group)\n  retVal.defaultValue = object.defaultValue ?? ''\n  retVal.index = object.index ?? 0\n  return retVal\n}\n", "import {HttpHeaders} from './results'\n\n/**\n * Strategy for calculating retry delays.\n */\nexport interface RetryDelayStrategy {\n  /**\n   * Returns delay for a next retry\n   * @param error - reason for retrying\n   * @param failedAttempts - a count of already failed attempts, 1 being the first\n   * @returns milliseconds to wait before retrying\n   */\n  nextDelay(error?: Error, failedAttempts?: number): number\n  /** Implementation should reset its state, this is mandatory to call upon success.  */\n  success(): void\n}\n\n/**\n * Interface for errors to inform that an associated operation can be retried.\n */\nexport interface RetriableDecision {\n  /**\n   * Informs whether this can be retried.\n   */\n  canRetry(): boolean\n  /**\n   * Get the delay in milliseconds to retry the action.\n   * @returns 0 to let the implementation decide, miliseconds delay otherwise\n   */\n  retryAfter(): number\n}\n\nconst retriableStatusCodes = [404, 408, 425, 429, 500, 502, 503, 504]\n/** isStatusCodeRetriable checks whether the supplied HTTP status code is retriable. */\nexport function isStatusCodeRetriable(statusCode: number): boolean {\n  return retriableStatusCodes.includes(statusCode)\n}\n\n/** IllegalArgumentError is thrown when illegal argument is supplied. */\nexport class IllegalArgumentError extends Error {\n  /* istanbul ignore next */\n  constructor(message: string) {\n    super(message)\n    this.name = 'IllegalArgumentError'\n    Object.setPrototypeOf(this, IllegalArgumentError.prototype)\n  }\n}\n\n/**\n * A general HTTP error.\n */\nexport class HttpError extends Error implements RetriableDecision {\n  private _retryAfter: number\n  /** application error code, when available */\n  public code: string | undefined\n  /** json error response */\n  public json: any\n\n  public headers?: HttpHeaders | undefined\n\n  /* istanbul ignore next because of super() not being covered*/\n  constructor(\n    readonly statusCode: number,\n    readonly statusMessage: string | undefined,\n    readonly body?: string,\n    retryAfter?: string | undefined | null,\n    readonly contentType?: string | undefined | null,\n    message?: string,\n    headers?: HttpHeaders | undefined\n  ) {\n    super()\n    Object.setPrototypeOf(this, HttpError.prototype)\n\n    this.headers = headers\n\n    if (message) {\n      this.message = message\n    } else if (body) {\n      if (contentType?.startsWith('application/json')) {\n        try {\n          this.json = JSON.parse(body)\n          this.message = this.json.message\n          this.code = this.json.code\n        } catch (e) {\n          // silently ignore, body string is still available\n        }\n      }\n      if (!this.message) {\n        this.message = `${statusCode} ${statusMessage} : ${body}`\n      }\n    } else {\n      this.message = `${statusCode} ${statusMessage}`\n    }\n    this.name = 'HttpError'\n    this.setRetryAfter(retryAfter)\n  }\n\n  private setRetryAfter(retryAfter?: string | undefined | null): void {\n    if (typeof retryAfter === 'string') {\n      // try to parse the supplied number as milliseconds\n      if (/^[0-9]+$/.test(retryAfter)) {\n        this._retryAfter = parseInt(retryAfter)\n      } else {\n        this._retryAfter = 0\n      }\n    } else {\n      this._retryAfter = 0\n    }\n  }\n\n  canRetry(): boolean {\n    return isStatusCodeRetriable(this.statusCode)\n  }\n  retryAfter(): number {\n    return this._retryAfter\n  }\n}\n\n//see https://nodejs.org/api/errors.html\nconst RETRY_CODES = [\n  'ECONNRESET',\n  'ENOTFOUND',\n  'ESOCKETTIMEDOUT',\n  'ETIMEDOUT',\n  'ECONNREFUSED',\n  'EHOSTUNREACH',\n  'EPIPE',\n]\n\n/**\n * Tests the error in order to know if an HTTP call can be retried.\n * @param error - error to test\n * @returns true for a retriable error\n */\nexport function canRetryHttpCall(error: any): boolean {\n  if (!error) {\n    return false\n  } else if (typeof (error as any).canRetry === 'function') {\n    return !!((error as any).canRetry as () => boolean)()\n  } else if ((error as any).code && RETRY_CODES.includes((error as any).code)) {\n    return true\n  }\n  return false\n}\n\n/**\n * Gets retry delay from the supplied error, possibly using random number up to retryJitter.\n */\nexport function getRetryDelay(error?: Error, retryJitter?: number): number {\n  if (!error) {\n    return 0\n  } else {\n    let retVal\n    if (typeof (error as any).retryAfter === 'function') {\n      return ((error as any).retryAfter as () => number)()\n    } else {\n      retVal = 0\n    }\n    if (retryJitter && retryJitter > 0) {\n      return retVal + Math.round(Math.random() * retryJitter)\n    } else {\n      return retVal\n    }\n  }\n}\n\n/** RequestTimedOutError indicates request timeout in the communication with the server */\nexport class RequestTimedOutError extends Error implements RetriableDecision {\n  /* istanbul ignore next because of super() not being covered */\n  constructor() {\n    super()\n    Object.setPrototypeOf(this, RequestTimedOutError.prototype)\n    this.name = 'RequestTimedOutError'\n    this.message = 'Request timed out'\n  }\n  canRetry(): boolean {\n    return true\n  }\n  retryAfter(): number {\n    return 0\n  }\n}\n\n/** AbortError indicates that the communication with the server was aborted */\nexport class AbortError extends Error implements RetriableDecision {\n  /* istanbul ignore next because of super() not being covered */\n  constructor() {\n    super()\n    this.name = 'AbortError'\n    Object.setPrototypeOf(this, AbortError.prototype)\n    this.message = 'Response aborted'\n  }\n  canRetry(): boolean {\n    return true\n  }\n  retryAfter(): number {\n    return 0\n  }\n}\n", "import {\n  FluxTableColumn,\n  UNKNOWN_COLUMN,\n  typeSerializers,\n} from './FluxTableColumn'\nimport {IllegalArgumentError} from '../errors'\n\n/**\n * serializeDateTimeAsDate changes type serializers to return JavaScript Date instances\n * for 'dateTime:RFC3339' query result data type. Empty value is converted to null.\n * @remarks\n * Please note that the result has millisecond precision whereas InfluxDB returns dateTime\n * in nanosecond precision.\n */\nexport function serializeDateTimeAsDate(): void {\n  typeSerializers['dateTime:RFC3339'] = (x: string): any =>\n    x === '' ? null : new Date(Date.parse(x))\n}\n/**\n * serializeDateTimeAsNumber changes type serializers to return milliseconds since epoch\n * for 'dateTime:RFC3339' query result data type. Empty value is converted to null.\n * @remarks\n * Please note that the result has millisecond precision whereas InfluxDB returns dateTime\n * in nanosecond precision.\n */\nexport function serializeDateTimeAsNumber(): void {\n  typeSerializers['dateTime:RFC3339'] = (x: string): any =>\n    x === '' ? null : Date.parse(x)\n}\n/**\n * serializeDateTimeAsString changes type serializers to return string values\n * for `dateTime:RFC3339` query result data type.  Empty value is converted to null.\n */\nexport function serializeDateTimeAsString(): void {\n  typeSerializers['dateTime:RFC3339'] = (x: string): any =>\n    x === '' ? null : x\n}\n\n/**\n * Represents metadata of a {@link http://bit.ly/flux-spec#table | flux table}.\n */\nexport interface FluxTableMetaData {\n  /**\n   * Table columns.\n   */\n  columns: Array<FluxTableColumn>\n\n  /**\n   * Gets columns by name\n   * @param label - column label\n   * @param errorOnMissingColumn - throw error on missing column (by default), return UNKNOWN_COLUMN when false\n   * @returns table column\n   * @throws IllegalArgumentError if column is not found\n   **/\n  column(label: string, errorOnMissingColumn?: boolean): FluxTableColumn\n\n  /**\n   * Creates an object out of the supplied row with the help of column descriptors.\n   * @param row - a row with data for each column\n   */\n  toObject(row: string[]): {[key: string]: any}\n\n  /**\n   * Gets column values out of the supplied row.\n   * @param row - a row with data for each column\n   * @param column - column name\n   * @returns column value, undefined for unknown column\n   */\n  get(row: string[], column: string): any\n}\n\n/**\n * FluxTableMetaData Implementation.\n */\nclass FluxTableMetaDataImpl implements FluxTableMetaData {\n  columns: Array<FluxTableColumn>\n  constructor(columns: FluxTableColumn[]) {\n    columns.forEach((col, i) => (col.index = i))\n    this.columns = columns\n  }\n  column(label: string, errorOnMissingColumn = true): FluxTableColumn {\n    for (let i = 0; i < this.columns.length; i++) {\n      const col = this.columns[i]\n      if (col.label === label) return col\n    }\n    if (errorOnMissingColumn) {\n      throw new IllegalArgumentError(`Column ${label} not found!`)\n    }\n    return UNKNOWN_COLUMN\n  }\n  toObject(row: string[]): {[key: string]: any} {\n    const acc: any = {}\n    for (let i = 0; i < this.columns.length && i < row.length; i++) {\n      const column = this.columns[i]\n      acc[column.label] = column.get(row)\n    }\n    return acc\n  }\n  get(row: string[], column: string): any {\n    return this.column(column, false).get(row)\n  }\n}\n\n/**\n * Created FluxTableMetaData from the columns supplied.\n * @param columns -  columns\n * @returns - instance\n */\nexport function createFluxTableMetaData(\n  columns: FluxTableColumn[]\n): FluxTableMetaData {\n  return new FluxTableMetaDataImpl(columns)\n}\n\n/** Wraps values and associated metadata of a query result row */\nexport interface Row {\n  values: string[]\n  tableMeta: FluxTableMetaData\n}\n", "import {CommunicationObserver} from './CommunicationObserver'\nimport {LineSplitter} from './LineSplitter'\nimport {FluxResultObserver} from './FluxResultObserver'\nimport {\n  FluxTableColumn,\n  ColumnType,\n  newFluxTableColumn,\n} from './FluxTableColumn'\nimport {FluxTableMetaData, createFluxTableMetaData} from './FluxTableMetaData'\n\n/**\n * LinesToTables creates a transformation that accepts (flux) annotated CSV lines\n * and emits rows together with table metadata.\n */\nexport function linesToTables(\n  consumer: FluxResultObserver<string[]>\n): CommunicationObserver<string> {\n  const splitter = new LineSplitter().withReuse()\n  let columns: FluxTableColumn[] | undefined\n  let expectMeta = true\n  let firstColumnIndex = 0\n  let lastMeta: FluxTableMetaData\n  const retVal: CommunicationObserver<string> = {\n    error(error: Error): void {\n      consumer.error(error)\n    },\n    next(line: string): void | boolean {\n      if (line === '') {\n        expectMeta = true\n        columns = undefined\n      } else {\n        const values = splitter.splitLine(line)\n        const size = splitter.lastSplitLength\n        if (expectMeta) {\n          // create columns\n          if (!columns) {\n            columns = new Array(size)\n            for (let i = 0; i < size; i++) {\n              columns[i] = newFluxTableColumn()\n            }\n          }\n          if (!values[0].startsWith('#')) {\n            // fill in column names\n            if (values[0] === '') {\n              firstColumnIndex = 1\n              columns = columns.slice(1)\n            } else {\n              firstColumnIndex = 0\n            }\n            for (let i = firstColumnIndex; i < size; i++) {\n              columns[i - firstColumnIndex].label = values[i]\n            }\n            lastMeta = createFluxTableMetaData(columns)\n            expectMeta = false\n          } else if (values[0] === '#datatype') {\n            for (let i = 1; i < size; i++) {\n              columns[i].dataType = values[i] as ColumnType\n            }\n          } else if (values[0] === '#default') {\n            for (let i = 1; i < size; i++) {\n              columns[i].defaultValue = values[i]\n            }\n          } else if (values[0] === '#group') {\n            for (let i = 1; i < size; i++) {\n              columns[i].group = values[i][0] === 't'\n            }\n          }\n        } else {\n          return consumer.next(values.slice(firstColumnIndex, size), lastMeta)\n        }\n      }\n      return true\n    },\n    complete(): void {\n      consumer.complete()\n    },\n  }\n  if (consumer.useCancellable) {\n    retVal.useCancellable = consumer.useCancellable.bind(consumer)\n  }\n  if (consumer.useResume) {\n    retVal.useResume = consumer.useResume.bind(consumer)\n  }\n  return retVal\n}\n", "import {LineSplitter} from './LineSplitter'\nimport {\n  FluxTableColumn,\n  ColumnType,\n  newFluxTableColumn,\n} from './FluxTableColumn'\nimport {\n  FluxTableMetaData,\n  createFluxTableMetaData,\n  Row,\n} from './FluxTableMetaData'\n\n/**\n * LinesToRowsIterable is a transformation that accepts\n * an iterable of flux annotated CSV lines and returns\n * an iterable of rows (row values and table metadata).\n */\nexport async function* linesToRowsIterable(\n  source: AsyncIterable<string>\n): AsyncIterableIterator<Row> {\n  const splitter = new LineSplitter().withReuse()\n  let columns: FluxTableColumn[] | undefined\n  let expectMeta = true\n  let firstColumnIndex = 0\n  let lastMeta: FluxTableMetaData | undefined = undefined\n  for await (const line of source) {\n    if (line === '') {\n      expectMeta = true\n      columns = undefined\n    } else {\n      const values = splitter.splitLine(line)\n      const size = splitter.lastSplitLength\n      if (expectMeta) {\n        // create columns\n        if (!columns) {\n          columns = new Array(size)\n          for (let i = 0; i < size; i++) {\n            columns[i] = newFluxTableColumn()\n          }\n        }\n        if (!values[0].startsWith('#')) {\n          // fill in column names\n          if (values[0] === '') {\n            firstColumnIndex = 1\n            columns = columns.slice(1)\n          } else {\n            firstColumnIndex = 0\n          }\n          for (let i = firstColumnIndex; i < size; i++) {\n            columns[i - firstColumnIndex].label = values[i]\n          }\n          lastMeta = createFluxTableMetaData(columns)\n          expectMeta = false\n        } else if (values[0] === '#datatype') {\n          for (let i = 1; i < size; i++) {\n            columns[i].dataType = values[i] as ColumnType\n          }\n        } else if (values[0] === '#default') {\n          for (let i = 1; i < size; i++) {\n            columns[i].defaultValue = values[i]\n          }\n        } else if (values[0] === '#group') {\n          for (let i = 1; i < size; i++) {\n            columns[i].group = values[i][0] === 't'\n          }\n        }\n      } else {\n        yield {\n          values: values.slice(firstColumnIndex, size),\n          tableMeta:\n            lastMeta as unknown as FluxTableMetaData /* never undefined */,\n        }\n      }\n    }\n  }\n}\n", "import {CommunicationObserver} from './CommunicationObserver'\n\n/**\n * StringToLines is a transformation that emmits strings for each CSV\n * line in the supplied source string.\n * @param source - source string\n * @param target - target to emmit CSV lines to\n * @returns communication obrver to accept Uint8Arrays\n */\nexport function stringToLines(\n  source: string,\n  target: CommunicationObserver<string>\n): void {\n  let quoted = false\n  let start = 0\n  let index = 0\n\n  while (index < source.length) {\n    const c = source.charCodeAt(index)\n    if (c === 10) {\n      if (!quoted) {\n        /* do not emit CR+LR or LF line ending */\n        const end =\n          index > 0 && source.charCodeAt(index - 1) === 13 ? index - 1 : index\n        // do not emmit more lines if the processing is already finished\n        target.next(source.substring(start, end))\n        start = index + 1\n      }\n    } else if (c === 34 /* \" */) {\n      quoted = !quoted\n    }\n    index++\n  }\n  if (start < index) {\n    target.next(source.substring(start, index))\n  }\n  target.complete()\n}\n", "/* Observable interop typing. Taken from https://github.com/ReactiveX/rxjs */\n\n/* Note: This will add Symbol.observable globally for all TypeScript users */\ndeclare global {\n  interface SymbolConstructor {\n    readonly observable: symbol\n  }\n}\n\n/** Symbol.observable or a string \"\\@\\@observable\". Used for interop */\nexport const symbolObservable = (():\n  | typeof Symbol.observable\n  | '@@observable' =>\n  (typeof Symbol === 'function' && Symbol.observable) || '@@observable')()\n", "import {\n  Observable,\n  Observer,\n  ObserverComplete,\n  ObserverError,\n  ObserverNext,\n  Subscription,\n  symbolObservable,\n} from '../observable'\nimport {CommunicationObserver} from '../results/CommunicationObserver'\nimport {Cancellable} from '../results/Cancellable'\n\n/** APIExecutor executes the API and passes its response to the supplied consumer */\nexport type APIExecutor = (consumer: CommunicationObserver<Uint8Array>) => void\n\ntype Decorator<T> = (observer: Observer<T>) => Observer<Uint8Array>\n\nclass QuerySubscription implements Subscription {\n  private cancellable?: Cancellable\n  private isClosed = false\n\n  public constructor(observer: Observer<Uint8Array>, executor: APIExecutor) {\n    try {\n      executor({\n        next: (value) => {\n          observer.next(value)\n        },\n        error: (e) => {\n          this.isClosed = true\n          observer.error(e)\n        },\n        complete: () => {\n          this.isClosed = true\n          observer.complete()\n        },\n        useCancellable: (c) => {\n          this.cancellable = c\n        },\n      })\n    } catch (e) {\n      this.isClosed = true\n      observer.error(e)\n    }\n  }\n\n  public get closed(): boolean {\n    return this.isClosed\n  }\n\n  public unsubscribe(): void {\n    this.cancellable?.cancel()\n    this.isClosed = true\n  }\n}\n\nfunction noop(): void {}\n\nfunction completeObserver<T>(observer: Partial<Observer<T>>): Observer<T> {\n  const {next, error, complete} = observer\n\n  return {\n    next: next ? next.bind(observer) : noop,\n    error: error ? error.bind(observer) : noop,\n    complete: complete ? complete.bind(observer) : noop,\n  }\n}\n\nexport default class ObservableQuery<T> implements Observable<T> {\n  public constructor(\n    private readonly executor: APIExecutor,\n    private readonly decorator: Decorator<T>\n  ) {}\n\n  public subscribe(\n    observerOrNext?: Partial<Observer<T>> | ObserverNext<T>,\n    error?: ObserverError,\n    complete?: ObserverComplete\n  ): Subscription {\n    const observer = completeObserver(\n      typeof observerOrNext !== 'object' || observerOrNext === null\n        ? {next: observerOrNext, error, complete}\n        : observerOrNext\n    )\n\n    return new QuerySubscription(this.decorator(observer), this.executor)\n  }\n\n  public [symbolObservable](): this {\n    return this\n  }\n\n  // this makes sure we satisfy the interface, while using a possibly polyfilled\n  // [symbolObservable] above for the actual implementation\n  public declare [Symbol.observable]: () => this\n}\n", "import {Transport} from './transport'\nimport <PERSON><PERSON><PERSON><PERSON> from './WriteApi'\n\n/**\n * Option for the communication with InfluxDB server.\n */\nexport interface ConnectionOptions {\n  /** base URL */\n  url: string\n  /** authentication token */\n  token?: string\n  /**\n   * socket timeout, 10000 milliseconds by default in node.js\n   * @defaultValue 10000\n   */\n  timeout?: number\n  /**\n   * TransportOptions supply extra options for the transport layer, they differ between node.js and browser/deno.\n   * Node.js transport accepts options specified in {@link https://nodejs.org/api/http.html#http_http_request_options_callback | http.request } or\n   * {@link https://nodejs.org/api/https.html#https_https_request_options_callback | https.request }. For example, an `agent` property can be set to\n   * {@link https://www.npmjs.com/package/proxy-http-agent | setup HTTP/HTTPS proxy }, {@link  https://nodejs.org/api/tls.html#tls_tls_connect_options_callback | rejectUnauthorized }\n   * property can disable TLS server certificate verification. Additionally,\n   * {@link https://github.com/follow-redirects/follow-redirects | follow-redirects } property can be also specified\n   * in order to follow redirects in node.js.\n   * {@link https://developer.mozilla.org/en-US/docs/Web/API/fetch | fetch } is used under the hood in browser/deno.\n   * For example,\n   * {@link https://developer.mozilla.org/en-US/docs/Web/API/fetch | redirect } property can be set to 'error' to abort request if a redirect occurs.\n   */\n  transportOptions?: {[key: string]: any}\n  /**\n   * Default HTTP headers to send with every request.\n   */\n  headers?: Record<string, string>\n  /**\n   * Full HTTP web proxy URL including schema, for example http://your-proxy:8080.\n   */\n  proxyUrl?: string\n}\n\n/** default connection options */\nexport const DEFAULT_ConnectionOptions: Partial<ConnectionOptions> = {\n  timeout: 10000,\n}\n\n/**\n * Options that configure strategy for retrying failed requests.\n */\nexport interface RetryDelayStrategyOptions {\n  /** add `random(retryJitter)` milliseconds delay when retrying HTTP calls */\n  retryJitter: number\n  /** minimum delay when retrying write (milliseconds) */\n  minRetryDelay: number\n  /** maximum delay when retrying write (milliseconds) */\n  maxRetryDelay: number\n  /** base for the exponential retry delay */\n  exponentialBase: number\n  /**\n   * randomRetry indicates whether the next retry delay is deterministic (false) or random (true).\n   * The deterministic delay starts with `minRetryDelay * exponentialBase` and it is multiplied\n   * by `exponentialBase` until it exceeds `maxRetryDelay`.\n   * When random is `true`, the next delay is computed as a random number between next retry attempt (upper)\n   * and the lower number in the deterministic sequence. `random(retryJitter)` is added to every returned value.\n   */\n  randomRetry: boolean\n}\n\n/**\n * Options that configure strategy for retrying failed InfluxDB write operations.\n */\nexport interface WriteRetryOptions extends RetryDelayStrategyOptions {\n  /**\n   * WriteFailed is called to inform about write errors.\n   * @param this - the instance of the API that failed\n   * @param error - write error\n   * @param lines - failed lines\n   * @param attempt - count of already failed attempts to write the lines (1 ... maxRetries+1)\n   * @param expires - expiration time for the lines to be retried in millis since epoch\n   * @returns a Promise to force the API to use it as a result of the flush operation,\n   * void/undefined to continue with default retry mechanism\n   */\n  writeFailed(\n    this: WriteApi,\n    error: Error,\n    lines: Array<string>,\n    attempt: number,\n    expires: number\n  ): Promise<void> | void\n\n  /**\n   * WriteSuccess is informed about successfully written lines.\n   * @param this - the instance of the API in use\n   * @param lines - written lines\n   */\n  writeSuccess(this: WriteApi, lines: Array<string>): void\n\n  /**\n   * WriteRetrySkipped is informed about lines that were removed from the retry buffer\n   * to keep the size of the retry buffer under the configured limit (maxBufferLines).\n   * @param entry - lines that were skipped\n   */\n  writeRetrySkipped(entry: {lines: Array<string>; expires: number}): void\n\n  /** max count of retries after the first write fails */\n  maxRetries: number\n  /** max time (millis) that can be spent with retries */\n  maxRetryTime: number\n  /** the maximum size of retry-buffer (in lines) */\n  maxBufferLines: number\n}\n\n/**\n * Options used by {@link WriteApi} .\n */\nexport interface WriteOptions extends WriteRetryOptions {\n  /** max number of records/lines to send in a batch   */\n  batchSize: number\n  /** delay between data flushes in milliseconds, at most `batch size` records are sent during flush  */\n  flushInterval: number\n  /** default tags, unescaped */\n  defaultTags?: Record<string, string>\n  /** HTTP headers that will be sent with every write request */\n  headers?: {[key: string]: string}\n  /** When specified, write bodies larger than the threshold are gzipped  */\n  gzipThreshold?: number\n  /** max size of a batch in bytes */\n  maxBatchBytes: number\n  /** InfluxDB Enterprise write consistency as explained in https://docs.influxdata.com/enterprise_influxdb/v1.9/concepts/clustering/#write-consistency */\n  consistency?: 'any' | 'one' | 'quorum' | 'all'\n}\n\n/** default RetryDelayStrategyOptions */\nexport const DEFAULT_RetryDelayStrategyOptions = {\n  retryJitter: 200,\n  minRetryDelay: 5000,\n  maxRetryDelay: 125000,\n  exponentialBase: 5,\n  randomRetry: true,\n}\n\n/** default writeOptions */\nexport const DEFAULT_WriteOptions: WriteOptions = {\n  batchSize: 1000,\n  maxBatchBytes: 50_000_000, // default max batch size in the cloud\n  flushInterval: 60000,\n  writeFailed: function () {},\n  writeSuccess: function () {},\n  writeRetrySkipped: function () {},\n  maxRetries: 5,\n  maxRetryTime: 180_000,\n  maxBufferLines: 32_000,\n  // a copy of DEFAULT_RetryDelayStrategyOptions, so that DEFAULT_WriteOptions could be tree-shaken\n  retryJitter: 200,\n  minRetryDelay: 5000,\n  maxRetryDelay: 125000,\n  exponentialBase: 2,\n  gzipThreshold: 1000,\n  randomRetry: true,\n}\n\n/**\n * Options used by {@link InfluxDB} .\n */\nexport interface ClientOptions extends ConnectionOptions {\n  /** supplies and overrides default writing options */\n  writeOptions?: Partial<WriteOptions>\n  /** specifies custom transport */\n  transport?: Transport\n}\n\n/**\n * Timestamp precision used in write operations.\n * See {@link https://docs.influxdata.com/influxdb/latest/api/#operation/PostWrite }\n */\nexport type WritePrecisionType = 'ns' | 'us' | 'ms' | 's'\n", "function createEscaper(\n  characters: string,\n  replacements: string[]\n): (value: string) => string {\n  return function (value: string): string {\n    let retVal = ''\n    let from = 0\n    let i = 0\n    while (i < value.length) {\n      const found = characters.indexOf(value[i])\n      if (found >= 0) {\n        retVal += value.substring(from, i)\n        retVal += replacements[found]\n        from = i + 1\n      }\n      i++\n    }\n    if (from == 0) {\n      return value\n    } else if (from < value.length) {\n      retVal += value.substring(from, value.length)\n    }\n    return retVal\n  }\n}\nfunction createQuotedEscaper(\n  characters: string,\n  replacements: string[]\n): (value: string) => string {\n  const escaper = createEscaper(characters, replacements)\n  return (value: string): string => '\"' + escaper(value) + '\"'\n}\n\n/**\n * Provides functions escape specific parts in InfluxDB line protocol.\n */\nexport const escape = {\n  /**\n   * Measurement escapes measurement names.\n   */\n  measurement: createEscaper(', \\n\\r\\t', ['\\\\,', '\\\\ ', '\\\\n', '\\\\r', '\\\\t']),\n  /**\n   * Quoted escapes quoted values, such as database names.\n   */\n  quoted: createQuotedEscaper('\"\\\\', ['\\\\\"', '\\\\\\\\']),\n\n  /**\n   * TagEscaper escapes tag keys, tag values, and field keys.\n   */\n  tag: createEscaper(', =\\n\\r\\t', ['\\\\,', '\\\\ ', '\\\\=', '\\\\n', '\\\\r', '\\\\t']),\n}\n", "declare let process: any\nconst zeroPadding = '000000000'\nlet useHrTime = false\n\nexport function useProcessHrtime(use: boolean): boolean {\n  /* istanbul ignore else */\n  if (!process.env.BUILD_BROWSER) {\n    return (useHrTime = use && process && typeof process.hrtime === 'function')\n  } else {\n    return false\n  }\n}\nuseProcessHrtime(true) // preffer node\n\nlet startHrMillis: number | undefined = undefined\nlet startHrTime: [number, number] | undefined = undefined\nlet lastMillis = Date.now()\nlet stepsInMillis = 0\nfunction nanos(): string {\n  if (!process.env.BUILD_BROWSER && useHrTime) {\n    const hrTime = process.hrtime() as [number, number]\n    let millis = Date.now()\n    if (!startHrTime) {\n      startHrTime = hrTime\n      startHrMillis = millis\n    } else {\n      hrTime[0] = hrTime[0] - startHrTime[0]\n      hrTime[1] = hrTime[1] - startHrTime[1]\n      // istanbul ignore next \"cannot mock system clock, manually reviewed\"\n      if (hrTime[1] < 0) {\n        hrTime[0] -= 1\n        hrTime[1] += 1000_000_000\n      }\n      millis =\n        (startHrMillis as number) +\n        hrTime[0] * 1000 +\n        Math.floor(hrTime[1] / 1000_000)\n    }\n    const nanos = String(hrTime[1] % 1000_000)\n    return String(millis) + zeroPadding.substr(0, 6 - nanos.length) + nanos\n  } else {\n    const millis = Date.now()\n    if (millis !== lastMillis) {\n      lastMillis = millis\n      stepsInMillis = 0\n    } else {\n      stepsInMillis++\n    }\n    const nanos = String(stepsInMillis)\n    return String(millis) + zeroPadding.substr(0, 6 - nanos.length) + nanos\n  }\n}\n\nfunction micros(): string {\n  if (!process.env.BUILD_BROWSER && useHrTime) {\n    const hrTime = process.hrtime() as [number, number]\n    const micros = String(Math.trunc(hrTime[1] / 1000) % 1000)\n    return (\n      String(Date.now()) + zeroPadding.substr(0, 3 - micros.length) + micros\n    )\n  } else {\n    return String(Date.now()) + zeroPadding.substr(0, 3)\n  }\n}\nfunction millis(): string {\n  return String(Date.now())\n}\nfunction seconds(): string {\n  return String(Math.floor(Date.now() / 1000))\n}\n\n/**\n * Exposes functions that creates strings that represent a timestamp that\n * can be used in the line protocol. Micro and nano timestamps are emulated\n * depending on the js platform in use.\n */\nexport const currentTime = {\n  s: seconds as () => string,\n  ms: millis as () => string,\n  us: micros as () => string,\n  ns: nanos as () => string,\n  seconds: seconds as () => string,\n  millis: millis as () => string,\n  micros: micros as () => string,\n  nanos: nanos as () => string,\n}\n\n/**\n * dateToProtocolTimestamp provides converters for JavaScript Date to InfluxDB Write Protocol Timestamp. Keys are supported precisions.\n */\nexport const dateToProtocolTimestamp = {\n  s: (d: Date): string => `${Math.floor(d.getTime() / 1000)}`,\n  ms: (d: Date): string => `${d.getTime()}`,\n  us: (d: Date): string => `${d.getTime()}000`,\n  ns: (d: Date): string => `${d.getTime()}000000`,\n}\n\n/**\n * convertTimeToNanos converts Point's timestamp to a string.\n * @param value - supported timestamp value\n * @returns line protocol value\n */\nexport function convertTimeToNanos(\n  value: string | number | Date | undefined\n): string | undefined {\n  if (value === undefined) {\n    return nanos()\n  } else if (typeof value === 'string') {\n    return value.length > 0 ? value : undefined\n  } else if (value instanceof Date) {\n    return `${value.getTime()}000000`\n  } else if (typeof value === 'number') {\n    return String(Math.floor(value))\n  } else {\n    return String(value)\n  }\n}\n", "/**\n * Logging interface.\n */\nexport interface Logger {\n  error(message: string, err?: any): void\n  warn(message: string, err?: any): void\n}\n\n/**\n * Logger that logs to console.out\n */\nexport const consoleLogger: Logger = {\n  error(message, error) {\n    // eslint-disable-next-line no-console\n    console.error('ERROR: ' + message, error ? error : '')\n  },\n  warn(message, error) {\n    // eslint-disable-next-line no-console\n    console.warn('WARN: ' + message, error ? error : '')\n  },\n}\nlet provider: Logger = consoleLogger\n\nexport const Log: Logger = {\n  error(message, error) {\n    provider.error(message, error)\n  },\n  warn(message, error) {\n    provider.warn(message, error)\n  },\n}\n\n/**\n * Sets custom logger.\n * @param logger - logger to use\n * @returns previous logger\n */\nexport function setLogger(logger: Logger): Logger {\n  const previous = provider\n  provider = logger\n  return previous\n}\n", "/** Property that offers a function that returns flux-sanitized value of an object.  */\nexport const FLUX_VALUE = Symbol('FLUX_VALUE')\n\n/**\n * A flux parameter can print its (sanitized) flux value.\n */\nexport interface FluxParameterLike {\n  [FLUX_VALUE](): string\n}\n\n/**\n * Represents a parameterized query.\n */\nexport interface ParameterizedQuery {\n  /**\n   * Returns flux query with sanitized parameters.\n   */\n  toString(): string\n}\n\nclass FluxParameter implements FluxParameterLike, ParameterizedQuery {\n  constructor(private fluxValue: string) {}\n  toString(): string {\n    return this.fluxValue\n  }\n  [FLUX_VALUE](): string {\n    return this.fluxValue\n  }\n}\n\n/**\n * Checks if the supplied object is FluxParameterLike\n * @param value - any value\n * @returns true if it is\n */\nfunction isFluxParameterLike(value: any): boolean {\n  return typeof value === 'object' && typeof value[FLUX_VALUE] === 'function'\n}\n\n/**\n * Escapes content of the supplied string so it can be wrapped into double qoutes\n * to become a [flux string literal](https://docs.influxdata.com/flux/latest/spec/lexical-elements/#string-literals).\n * @param value - string value\n * @returns sanitized string\n */\nfunction sanitizeString(value: any): string {\n  if (value === null || value === undefined) return ''\n  value = value.toString()\n  let retVal: any = undefined\n  let i = 0\n  function prepareRetVal(): void {\n    if (retVal === undefined) {\n      retVal = value.substring(0, i)\n    }\n  }\n  for (; i < value.length; i++) {\n    const c = value.charAt(i)\n    switch (c) {\n      case '\\r':\n        prepareRetVal()\n        retVal += '\\\\r'\n        break\n      case '\\n':\n        prepareRetVal()\n        retVal += '\\\\n'\n        break\n      case '\\t':\n        prepareRetVal()\n        retVal += '\\\\t'\n        break\n      case '\"':\n      case '\\\\':\n        prepareRetVal()\n        retVal = retVal + '\\\\' + c\n        break\n      case '$':\n        // escape ${\n        if (i + 1 < value.length && value.charAt(i + 1) === '{') {\n          prepareRetVal()\n          i++\n          retVal += '\\\\${'\n          break\n        }\n        // append $\n        if (retVal != undefined) {\n          retVal += c\n        }\n        break\n      default:\n        if (retVal != undefined) {\n          retVal += c\n        }\n    }\n  }\n  if (retVal !== undefined) {\n    return retVal\n  }\n  return value\n}\n\n/**\n * Creates a flux string literal.\n */\nexport function fluxString(value: any): FluxParameterLike {\n  return new FluxParameter(`\"${sanitizeString(value)}\"`)\n}\n\n/**\n * Sanitizes float value to avoid injections.\n * @param value - InfluxDB float literal\n * @returns sanitized float value\n * @throws Error if the the value cannot be sanitized\n */\nexport function sanitizeFloat(value: any): string {\n  const val = Number(value)\n  if (!isFinite(val)) {\n    if (typeof value === 'number') {\n      return `float(v: \"${val}\")`\n    }\n    throw new Error(`not a flux float: ${value}`)\n  }\n  // try to return a flux float literal if possible\n  // https://docs.influxdata.com/flux/latest/data-types/basic/float/#float-syntax\n  const strVal = val.toString()\n  let hasDot = false\n  for (const c of strVal) {\n    if ((c >= '0' && c <= '9') || c == '-') continue\n    if (c === '.') {\n      hasDot = true\n      continue\n    }\n    return `float(v: \"${strVal}\")`\n  }\n  return hasDot ? strVal : strVal + '.0'\n}\n/**\n * Creates a flux float literal.\n */\nexport function fluxFloat(value: any): FluxParameterLike {\n  return new FluxParameter(sanitizeFloat(value))\n}\n\n/**\n * Sanitizes integer value to avoid injections.\n * @param value - InfluxDB integer literal\n * @returns sanitized integer value\n * @throws Error if the the value cannot be sanitized\n */\nexport function sanitizeInteger(value: any): string {\n  // https://docs.influxdata.com/flux/latest/data-types/basic/int/\n  // Min value: -9223372036854775808\n  // Max value: 9223372036854775807\n  // \"9223372036854775807\".length === 19\n  const strVal = String(value)\n  const negative = strVal.startsWith('-')\n  const val = negative ? strVal.substring(1) : strVal\n  if (val.length === 0 || val.length > 19) {\n    throw new Error(`not a flux integer: ${strVal}`)\n  }\n  for (const c of val) {\n    if (c < '0' || c > '9') throw new Error(`not a flux integer: ${strVal}`)\n  }\n  if (val.length === 19) {\n    if (negative && val > '9223372036854775808') {\n      throw new Error(`flux integer out of bounds: ${strVal}`)\n    }\n    if (!negative && val > '9223372036854775807') {\n      throw new Error(`flux integer out of bounds: ${strVal}`)\n    }\n  }\n  return strVal\n}\n\n/**\n * Creates a flux integer literal.\n */\nexport function fluxInteger(value: any): FluxParameterLike {\n  return new FluxParameter(sanitizeInteger(value))\n}\n\nfunction sanitizeDateTime(value: any): string {\n  return `time(v: \"${sanitizeString(value)}\")`\n}\n\n/**\n * Creates flux date-time literal.\n */\nexport function fluxDateTime(value: any): FluxParameterLike {\n  return new FluxParameter(sanitizeDateTime(value))\n}\n\n/**\n * Creates flux date-time literal.\n */\nexport function fluxDuration(value: any): FluxParameterLike {\n  return new FluxParameter(`duration(v: \"${sanitizeString(value)}\")`)\n}\n\nfunction sanitizeRegExp(value: any): string {\n  if (value instanceof RegExp) {\n    return value.toString()\n  }\n  return new RegExp(value).toString()\n}\n\n/**\n * Creates flux regexp literal out of a regular expression. See\n * https://docs.influxdata.com/flux/latest/data-types/basic/regexp/#regular-expression-syntax\n * for details.\n */\nexport function fluxRegExp(value: any): FluxParameterLike {\n  // let the server decide if a regexp can be parsed\n  return new FluxParameter(sanitizeRegExp(value))\n}\n\n/**\n * Creates flux boolean literal.\n */\nexport function fluxBool(value: any): FluxParameterLike {\n  if (value === 'true' || value === 'false') {\n    return new FluxParameter(value)\n  }\n  return new FluxParameter((!!value).toString())\n}\n\n/**\n * Assumes that the supplied value is flux expression or literal that does not need sanitizing.\n *\n * @param value - any value\n * @returns the supplied value as-is\n */\nexport function fluxExpression(value: any): FluxParameterLike {\n  return new FluxParameter(String(value))\n}\n\n/**\n * Escapes content of the supplied parameter so that it can be safely embedded into flux query.\n * @param value - parameter value\n * @returns sanitized flux value or an empty string if it cannot be converted\n */\nexport function toFluxValue(value: any): string {\n  if (value === undefined) {\n    return ''\n  } else if (value === null) {\n    return 'null'\n  } else if (typeof value === 'boolean') {\n    return value.toString()\n  } else if (typeof value === 'string') {\n    return `\"${sanitizeString(value)}\"`\n  } else if (typeof value === 'number') {\n    if (Number.isSafeInteger(value)) {\n      return sanitizeInteger(value)\n    }\n    return sanitizeFloat(value)\n  } else if (typeof value === 'object') {\n    if (typeof value[FLUX_VALUE] === 'function') {\n      return value[FLUX_VALUE]()\n    } else if (value instanceof Date) {\n      return value.toISOString()\n    } else if (value instanceof RegExp) {\n      return sanitizeRegExp(value)\n    } else if (Array.isArray(value)) {\n      return `[${value.map(toFluxValue).join(',')}]`\n    }\n  } else if (typeof value === 'bigint') {\n    return `${value}.0`\n  }\n  // use toString value for unrecognized object, symbol\n  return toFluxValue(value.toString())\n}\n\n/**\n * Flux is a tagged template that sanitizes supplied parameters\n * to avoid injection attacks in flux.\n */\nexport function flux(\n  strings: TemplateStringsArray,\n  ...values: any\n): ParameterizedQuery {\n  if (strings.length == 1 && values.length === 0) {\n    return fluxExpression(strings[0]) // the simplest case\n  }\n  const parts = new Array<string>(strings.length + values.length)\n  let partIndex = 0\n  for (let i = 0; i < strings.length; i++) {\n    const text = strings[i]\n    parts[partIndex++] = text\n    if (i < values.length) {\n      const val = values[i]\n      let sanitized: string\n      if (\n        text.endsWith('\"') &&\n        i + 1 < strings.length &&\n        strings[i + 1].startsWith('\"')\n      ) {\n        // parameter is wrapped into flux double quotes\n        sanitized = sanitizeString(val)\n      } else {\n        sanitized = toFluxValue(val)\n        if (sanitized === '') {\n          // do not allow to insert empty strings, unless it is FluxParameterLike\n          if (!isFluxParameterLike(val)) {\n            throw new Error(\n              `Unsupported parameter literal '${val}' at index: ${i}, type: ${typeof val}`\n            )\n          }\n        }\n      }\n      parts[partIndex++] = sanitized\n    } else if (i < strings.length - 1) {\n      throw new Error('Too few parameters supplied!')\n    }\n  }\n  // return flux expression so that flux can be embedded into another flux as-is\n  return fluxExpression(parts.join(''))\n}\n", "import {convertTimeToNanos} from './util/currentTime'\nimport {escape} from './util/escape'\n\n/**\n * Settings that control the way of how a {@link Point} is serialized\n * to a protocol line.\n */\nexport interface PointSettings {\n  /** default tags to add to every point */\n  defaultTags?: {[key: string]: string}\n  /** convertTime serializes Point's timestamp to a line protocol value */\n  convertTime?: (\n    value: string | number | Date | undefined\n  ) => string | undefined\n}\n\n/**\n * Point defines values of a single measurement.\n */\nexport class Point {\n  private name: string\n  private tags: {[key: string]: string} = {}\n  /** escaped field values */\n  public fields: {[key: string]: string} = {}\n  private time: string | number | Date | undefined\n\n  /**\n   * Create a new Point with specified a measurement name.\n   *\n   * @param measurementName - the measurement name\n   */\n  constructor(measurementName?: string) {\n    if (measurementName) this.name = measurementName\n  }\n\n  /**\n   * Sets point's measurement.\n   *\n   * @param name - measurement name\n   * @returns this\n   */\n  public measurement(name: string): Point {\n    this.name = name\n    return this\n  }\n\n  /**\n   * Adds a tag. The caller has to ensure that both name and value are not empty\n   * and do not end with backslash.\n   *\n   * @param name - tag name\n   * @param value - tag value\n   * @returns this\n   */\n  public tag(name: string, value: string): Point {\n    this.tags[name] = value\n    return this\n  }\n\n  /**\n   * Adds a boolean field.\n   *\n   * @param field - field name\n   * @param value - field value\n   * @returns this\n   */\n  public booleanField(name: string, value: boolean | any): Point {\n    this.fields[name] = value ? 'T' : 'F'\n    return this\n  }\n\n  /**\n   * Adds an integer field.\n   *\n   * @param name - field name\n   * @param value - field value\n   * @returns this\n   * @throws NaN or out of int64 range value is supplied\n   */\n  public intField(name: string, value: number | any): Point {\n    let val: number\n    if (typeof value === 'number') {\n      val = value\n    } else {\n      val = parseInt(String(value))\n    }\n    if (isNaN(val) || val <= -9223372036854776e3 || val >= 9223372036854776e3) {\n      throw new Error(`invalid integer value for field '${name}': '${value}'!`)\n    }\n    this.fields[name] = `${Math.floor(val)}i`\n    return this\n  }\n\n  /**\n   * Adds an unsigned integer field.\n   *\n   * @param name - field name\n   * @param value - field value\n   * @returns this\n   * @throws NaN out of range value is supplied\n   */\n  public uintField(name: string, value: number | any): Point {\n    if (typeof value === 'number') {\n      if (isNaN(value) || value < 0 || value > Number.MAX_SAFE_INTEGER) {\n        throw new Error(`uint value for field '${name}' out of range: ${value}`)\n      }\n      this.fields[name] = `${Math.floor(value as number)}u`\n    } else {\n      const strVal = String(value)\n      for (let i = 0; i < strVal.length; i++) {\n        const code = strVal.charCodeAt(i)\n        if (code < 48 || code > 57) {\n          throw new Error(\n            `uint value has an unsupported character at pos ${i}: ${value}`\n          )\n        }\n      }\n      if (\n        strVal.length > 20 ||\n        (strVal.length === 20 &&\n          strVal.localeCompare('18446744073709551615') > 0)\n      ) {\n        throw new Error(\n          `uint value for field '${name}' out of range: ${strVal}`\n        )\n      }\n      this.fields[name] = `${strVal}u`\n    }\n    return this\n  }\n\n  /**\n   * Adds a number field.\n   *\n   * @param name - field name\n   * @param value - field value\n   * @returns this\n   * @throws NaN/Infinity/-Infinity is supplied\n   */\n  public floatField(name: string, value: number | any): Point {\n    let val: number\n    if (typeof value === 'number') {\n      val = value\n    } else {\n      val = parseFloat(value)\n    }\n    if (!isFinite(val)) {\n      throw new Error(`invalid float value for field '${name}': ${value}`)\n    }\n\n    this.fields[name] = String(val)\n    return this\n  }\n\n  /**\n   * Adds a string field.\n   *\n   * @param name - field name\n   * @param value - field value\n   * @returns this\n   */\n  public stringField(name: string, value: string | any): Point {\n    if (value !== null && value !== undefined) {\n      if (typeof value !== 'string') value = String(value)\n      this.fields[name] = escape.quoted(value)\n    }\n    return this\n  }\n\n  /**\n   * Sets point timestamp. Timestamp can be specified as a Date (preferred), number, string\n   * or an undefined value. An undefined value instructs to assign a local timestamp using\n   * the client's clock. An empty string can be used to let the server assign\n   * the timestamp. A number value represents time as a count of time units since epoch, the\n   * exact time unit then depends on the {@link InfluxDB.getWriteApi | precision} of the API\n   * that writes the point.\n   *\n   * Beware that the current time in nanoseconds can't precisely fit into a JS number,\n   * which can hold at most 2^53 integer number. Nanosecond precision numbers are thus supplied as\n   * a (base-10) string. An application can also use ES2020 BigInt to represent nanoseconds,\n   * BigInt's `toString()` returns the required high-precision string.\n   *\n   * Note that InfluxDB requires the timestamp to fit into int64 data type.\n   *\n   * @param value - point time\n   * @returns this\n   */\n  public timestamp(value: Date | number | string | undefined): Point {\n    this.time = value\n    return this\n  }\n\n  /**\n   * Creates an InfluxDB protocol line out of this instance.\n   * @param settings - settings control serialization of a point timestamp and can also add default tags,\n   * nanosecond timestamp precision is used when no `settings` or no `settings.convertTime` is supplied.\n   * @returns an InfluxDB protocol line out of this instance\n   */\n  public toLineProtocol(settings?: Partial<PointSettings>): string | undefined {\n    if (!this.name) return undefined\n    let fieldsLine = ''\n    Object.keys(this.fields)\n      .sort()\n      .forEach((x) => {\n        if (x) {\n          const val = this.fields[x]\n          if (fieldsLine.length > 0) fieldsLine += ','\n          fieldsLine += `${escape.tag(x)}=${val}`\n        }\n      })\n    if (fieldsLine.length === 0) return undefined // no fields present\n    let tagsLine = ''\n    const tags =\n      settings && settings.defaultTags\n        ? {...settings.defaultTags, ...this.tags}\n        : this.tags\n    Object.keys(tags)\n      .sort()\n      .forEach((x) => {\n        if (x) {\n          const val = tags[x]\n          if (val) {\n            tagsLine += ','\n            tagsLine += `${escape.tag(x)}=${escape.tag(val)}`\n          }\n        }\n      })\n    let time = this.time\n    if (settings && settings.convertTime) {\n      time = settings.convertTime(time)\n    } else {\n      time = convertTimeToNanos(time)\n    }\n\n    return `${escape.measurement(this.name)}${tagsLine} ${fieldsLine}${\n      time !== undefined ? ' ' + time : ''\n    }`\n  }\n\n  toString(): string {\n    const line = this.toLineProtocol(undefined)\n    return line ? line : `invalid point: ${JSON.stringify(this, undefined)}`\n  }\n}\n", "import {getRetryDelay, RetryDelayStrategy} from '../errors'\nimport {\n  RetryDelayStrategyOptions,\n  DEFAULT_RetryDelayStrategyOptions,\n} from '../options'\n\n/**\n * Applies a variant of exponential backoff with initial and max delay and a random\n * jitter delay. It also respects `retry delay` when specified together with an error.\n */\nexport class RetryStrategyImpl implements RetryDelayStrategy {\n  options: RetryDelayStrategyOptions\n  currentDelay: number | undefined\n\n  constructor(options?: Partial<RetryDelayStrategyOptions>) {\n    this.options = {...DEFAULT_RetryDelayStrategyOptions, ...options}\n    this.success()\n  }\n\n  nextDelay(error?: Error, failedAttempts?: number): number {\n    const delay = getRetryDelay(error)\n    if (delay && delay > 0) {\n      return delay + Math.round(Math.random() * this.options.retryJitter)\n    } else {\n      if (failedAttempts && failedAttempts > 0) {\n        // compute delay\n        if (this.options.randomRetry) {\n          // random delay between deterministic delays\n          let delay = Math.max(this.options.minRetryDelay, 1)\n          let nextDelay = delay * this.options.exponentialBase\n          for (let i = 1; i < failedAttempts; i++) {\n            delay = nextDelay\n            nextDelay = nextDelay * this.options.exponentialBase\n            if (nextDelay >= this.options.maxRetryDelay) {\n              nextDelay = this.options.maxRetryDelay\n              break\n            }\n          }\n          return (\n            delay +\n            Math.round(\n              Math.random() * (nextDelay - delay) +\n                Math.random() * this.options.retryJitter\n            )\n          )\n        }\n        // deterministric delay otherwise\n        let delay = Math.max(this.options.minRetryDelay, 1)\n        for (let i = 1; i < failedAttempts; i++) {\n          delay = delay * this.options.exponentialBase\n          if (delay >= this.options.maxRetryDelay) {\n            delay = this.options.maxRetryDelay\n            break\n          }\n        }\n        return delay + Math.round(Math.random() * this.options.retryJitter)\n      } else if (this.currentDelay) {\n        this.currentDelay = Math.min(\n          Math.max(this.currentDelay * this.options.exponentialBase, 1) +\n            Math.round(Math.random() * this.options.retryJitter),\n          this.options.maxRetryDelay\n        )\n      } else {\n        this.currentDelay =\n          this.options.minRetryDelay +\n          Math.round(Math.random() * this.options.retryJitter)\n      }\n      return this.currentDelay\n    }\n  }\n  success(): void {\n    this.currentDelay = undefined\n  }\n}\n\n/**\n * Creates a new instance of retry strategy.\n * @param options - retry options\n * @returns retry strategy implementation\n */\nexport function createRetryDelayStrategy(\n  options?: Partial<RetryDelayStrategyOptions>\n): RetryDelayStrategy {\n  return new RetryStrategyImpl(options)\n}\n", "import {Log} from '../util/logger'\n\ninterface RetryItem {\n  lines: string[]\n  retryCount: number\n  retryTime: number\n  expires: number\n  next?: RetryItem\n}\n\ntype FindShrinkCandidateResult = [found: RetryItem, parent?: RetryItem]\n\nfunction findShrinkCandidate(first: RetryItem): FindShrinkCandidateResult {\n  let parent = undefined\n  let found = first\n  let currentParent = first\n  while (currentParent.next) {\n    if (currentParent.next.expires < found.expires) {\n      parent = currentParent\n      found = currentParent.next\n    }\n    currentParent = currentParent.next\n  }\n  return [found, parent]\n}\n\n/**\n * Retries lines up to a limit of max buffer size.\n */\nexport default class RetryBuffer {\n  first?: RetryItem\n  size = 0\n  closed = false\n  private _timeoutHandle: any = undefined\n\n  constructor(\n    private maxLines: number,\n    private retryLines: (\n      lines: string[],\n      retryCountdown: number,\n      started: number\n    ) => Promise<void>,\n    private onShrink: (entry: {\n      lines: string[]\n      retryCount: number\n      expires: number\n    }) => void = () => undefined\n  ) {}\n\n  addLines(\n    lines: string[],\n    retryCount: number,\n    delay: number,\n    expires: number\n  ): void {\n    if (this.closed) return\n    if (!lines.length) return\n    let retryTime = Date.now() + delay\n    if (expires < retryTime) {\n      retryTime = expires\n    }\n    // ensure at most maxLines are in the Buffer\n    if (this.first && this.size + lines.length > this.maxLines) {\n      const origSize = this.size\n      const newSize = origSize * 0.7 // reduce to 70 %\n      do {\n        // remove \"oldest\" item\n        const [found, parent] = findShrinkCandidate(this.first)\n        this.size -= found.lines.length\n        if (parent) {\n          parent.next = found.next\n        } else {\n          this.first = found.next\n          if (this.first) {\n            this.scheduleRetry(this.first.retryTime - Date.now())\n          }\n        }\n        found.next = undefined\n        this.onShrink(found)\n      } while (this.first && this.size + lines.length > newSize)\n      Log.error(\n        `RetryBuffer: ${\n          origSize - this.size\n        } oldest lines removed to keep buffer size under the limit of ${\n          this.maxLines\n        } lines.`\n      )\n    }\n    const toAdd: RetryItem = {\n      lines,\n      retryCount,\n      retryTime,\n      expires,\n    }\n    // insert sorted according to retryTime\n    let current: RetryItem | undefined = this.first\n    let parent = undefined\n    for (;;) {\n      if (!current || current.retryTime > retryTime) {\n        toAdd.next = current\n        if (parent) {\n          parent.next = toAdd\n        } else {\n          this.first = toAdd\n          this.scheduleRetry(retryTime - Date.now())\n        }\n        break\n      }\n      parent = current\n      current = current.next\n    }\n    this.size += lines.length\n  }\n\n  removeLines(): RetryItem | undefined {\n    if (this.first) {\n      const toRetry = this.first\n      this.first = this.first.next\n      toRetry.next = undefined\n      this.size -= toRetry.lines.length\n      return toRetry\n    }\n    return undefined\n  }\n\n  scheduleRetry(delay: number): void {\n    if (this._timeoutHandle) {\n      clearTimeout(this._timeoutHandle)\n    }\n    this._timeoutHandle = setTimeout(\n      () => {\n        const toRetry = this.removeLines()\n        if (toRetry) {\n          this.retryLines(toRetry.lines, toRetry.retryCount, toRetry.expires)\n            .catch(() => {\n              /* error is already logged, it must be caught */\n            })\n            .finally(() => {\n              // schedule next retry execution\n              if (this.first) {\n                this.scheduleRetry(this.first.retryTime - Date.now())\n              }\n            })\n        } else {\n          this._timeoutHandle = undefined\n        }\n      },\n      Math.max(delay, 0)\n    )\n  }\n\n  async flush(): Promise<void> {\n    let toRetry\n    while ((toRetry = this.removeLines())) {\n      await this.retryLines(toRetry.lines, toRetry.retryCount, toRetry.expires)\n    }\n  }\n\n  close(): number {\n    if (this._timeoutHandle) {\n      clearTimeout(this._timeoutHandle)\n      this._timeoutHandle = undefined\n    }\n    this.closed = true\n    return this.size\n  }\n}\n", "/**\n * Utf8Length returns an expected length of a string when UTF-8 encoded.\n * @param s - input string\n * @returns expected count of bytes\n */\nexport default function utf8Length(s: string): number {\n  let retVal = s.length\n  // extends the size with code points (https://en.wikipedia.org/wiki/UTF-8#Encoding)\n  for (let i = 0; i < s.length; i++) {\n    const code = s.charCodeAt(i)\n    /* istanbul ignore else - JS does not count with 4-bytes UNICODE characters at the moment */\n    if (code < 0x80) {\n      continue\n    } else if (code >= 0x80 && code <= 0x7ff) {\n      retVal++\n    } else if (code >= 0x800 && code <= 0xffff) {\n      if (code >= 0xd800 && code <= 0xdfff) {\n        // node.js represents unicode characters above 0xffff by two UTF-16 surrogate halves\n        // see https://en.wikipedia.org/wiki/UTF-8#Codepage_layout\n        retVal++\n      } else {\n        retVal += 2\n      }\n    } else {\n      // never happens in node.js 14, the situation can vary in the futures or in deno/browsers\n      retVal += 3\n    }\n  }\n  return retVal\n}\n", "import WriteApi from '../WriteApi'\nimport {\n  DEFAULT_WriteOptions,\n  WriteOptions,\n  WritePrecisionType,\n} from '../options'\nimport {Transport, SendOptions} from '../transport'\nimport {Headers} from '../results'\nimport {Log} from '../util/logger'\nimport {HttpError, RetryDelayStrategy} from '../errors'\nimport {Point} from '../Point'\nimport {currentTime, dateToProtocolTimestamp} from '../util/currentTime'\nimport {createRetryDelayStrategy} from './retryStrategy'\nimport RetryBuffer from './RetryBuffer'\nimport utf8Length from '../util/utf8Length'\n\nclass WriteBuffer {\n  length = 0\n  bytes = -1\n  lines: string[]\n\n  constructor(\n    private maxChunkRecords: number,\n    private maxBatchBytes: number,\n    private flushFn: (lines: string[]) => Promise<void>,\n    private scheduleSend: () => void\n  ) {\n    this.lines = new Array<string>(maxChunkRecords)\n  }\n\n  add(record: string): void {\n    const size = utf8Length(record)\n    if (this.length === 0) {\n      this.scheduleSend()\n    } else if (this.bytes + size + 1 >= this.maxBatchBytes) {\n      // the new size already exceeds maxBatchBytes, send it\n      this.flush().catch((_e) => {\n        // an error is logged in case of failure, avoid UnhandledPromiseRejectionWarning\n      })\n    }\n    this.lines[this.length] = record\n    this.length++\n    this.bytes += size + 1\n    if (\n      this.length >= this.maxChunkRecords ||\n      this.bytes >= this.maxBatchBytes\n    ) {\n      this.flush().catch((_e) => {\n        // an error is logged in case of failure, avoid UnhandledPromiseRejectionWarning\n      })\n    }\n  }\n  flush(): Promise<void> {\n    const lines = this.reset()\n    if (lines.length > 0) {\n      return this.flushFn(lines)\n    } else {\n      return Promise.resolve()\n    }\n  }\n  reset(): string[] {\n    const retVal = this.lines.slice(0, this.length)\n    this.length = 0\n    this.bytes = -1 // lines are joined with \\n\n    return retVal\n  }\n}\n\nexport default class WriteApiImpl implements WriteApi {\n  public path: string\n\n  private writeBuffer: WriteBuffer\n  private closed = false\n  private writeOptions: WriteOptions\n  private sendOptions: SendOptions\n  private _timeoutHandle: any = undefined\n  private currentTime: () => string\n  private dateToProtocolTimestamp: (d: Date) => string\n\n  retryBuffer: RetryBuffer\n  retryStrategy: RetryDelayStrategy\n\n  constructor(\n    private transport: Transport,\n    org: string,\n    bucket: string,\n    precision: WritePrecisionType,\n    writeOptions?: Partial<WriteOptions>\n  ) {\n    this.path = `/api/v2/write?org=${encodeURIComponent(\n      org\n    )}&bucket=${encodeURIComponent(bucket)}&precision=${precision}`\n    if (writeOptions?.consistency) {\n      this.path += `&consistency=${encodeURIComponent(\n        writeOptions.consistency\n      )}`\n    }\n    this.writeOptions = {\n      ...DEFAULT_WriteOptions,\n      ...writeOptions,\n    }\n    this.currentTime = currentTime[precision]\n    this.dateToProtocolTimestamp = dateToProtocolTimestamp[precision]\n    if (this.writeOptions.defaultTags) {\n      this.useDefaultTags(this.writeOptions.defaultTags)\n    }\n    this.sendOptions = {\n      method: 'POST',\n      headers: {\n        'content-type': 'text/plain; charset=utf-8',\n        ...writeOptions?.headers,\n      },\n      gzipThreshold: this.writeOptions.gzipThreshold,\n    }\n\n    const scheduleNextSend = (): void => {\n      if (this.writeOptions.flushInterval > 0) {\n        this._clearFlushTimeout()\n        /* istanbul ignore else manually reviewed, hard to reproduce */\n        if (!this.closed) {\n          this._timeoutHandle = setTimeout(\n            () =>\n              this.sendBatch(\n                this.writeBuffer.reset(),\n                this.writeOptions.maxRetries\n              ).catch((_e) => {\n                // an error is logged in case of failure, avoid UnhandledPromiseRejectionWarning\n              }),\n            this.writeOptions.flushInterval\n          )\n        }\n      }\n    }\n    // write buffer\n    this.writeBuffer = new WriteBuffer(\n      this.writeOptions.batchSize,\n      this.writeOptions.maxBatchBytes,\n      (lines) => {\n        this._clearFlushTimeout()\n        return this.sendBatch(lines, this.writeOptions.maxRetries)\n      },\n      scheduleNextSend\n    )\n    this.sendBatch = this.sendBatch.bind(this)\n    // retry buffer\n    this.retryStrategy = createRetryDelayStrategy(this.writeOptions)\n    this.retryBuffer = new RetryBuffer(\n      this.writeOptions.maxBufferLines,\n      this.sendBatch,\n      this.writeOptions.writeRetrySkipped\n    )\n  }\n\n  sendBatch(\n    lines: string[],\n    retryAttempts: number,\n    expires: number = Date.now() + this.writeOptions.maxRetryTime\n  ): Promise<void> {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const self: WriteApiImpl = this\n    const failedAttempts = self.writeOptions.maxRetries + 1 - retryAttempts\n    if (!this.closed && lines.length > 0) {\n      if (expires <= Date.now()) {\n        const error = new Error('Max retry time exceeded.')\n        const onRetry = self.writeOptions.writeFailed.call(\n          self,\n          error,\n          lines,\n          failedAttempts,\n          expires\n        )\n        if (onRetry) {\n          return onRetry\n        }\n        Log.error(\n          `Write to InfluxDB failed (attempt: ${failedAttempts}).`,\n          error\n        )\n        return Promise.reject(error)\n      }\n      return new Promise<void>((resolve, reject) => {\n        let responseStatusCode: number | undefined\n        let headers: Headers | undefined\n        const callbacks = {\n          responseStarted(_headers: Headers, statusCode?: number): void {\n            responseStatusCode = statusCode\n            headers = _headers\n          },\n          error(error: Error): void {\n            // call the writeFailed listener and check if we can retry\n            const onRetry = self.writeOptions.writeFailed.call(\n              self,\n              error,\n              lines,\n              failedAttempts,\n              expires\n            )\n            if (onRetry) {\n              onRetry.then(resolve, reject)\n              return\n            }\n            // ignore informational message about the state of InfluxDB\n            // enterprise cluster, if present\n            if (\n              error instanceof HttpError &&\n              error.json &&\n              typeof error.json.error === 'string' &&\n              error.json.error.includes('hinted handoff queue not empty')\n            ) {\n              Log.warn('Write to InfluxDB returns: ' + error.json.error)\n              responseStatusCode = 204\n              callbacks.complete()\n              return\n            }\n            // retry if possible\n            if (\n              !self.closed &&\n              retryAttempts > 0 &&\n              (!(error instanceof HttpError) ||\n                (error as HttpError).statusCode >= 429)\n            ) {\n              Log.warn(\n                `Write to InfluxDB failed (attempt: ${failedAttempts}).`,\n                error\n              )\n              self.retryBuffer.addLines(\n                lines,\n                retryAttempts - 1,\n                self.retryStrategy.nextDelay(error, failedAttempts),\n                expires\n              )\n              reject(error)\n              return\n            }\n            Log.error(`Write to InfluxDB failed.`, error)\n            reject(error)\n          },\n          complete(): void {\n            // InfluxDB v3 returns 201 for partial success\n            // older implementations of transport do not report status code\n            if (\n              responseStatusCode == 204 ||\n              responseStatusCode == 201 ||\n              responseStatusCode == undefined\n            ) {\n              self.writeOptions.writeSuccess.call(self, lines)\n              self.retryStrategy.success()\n              resolve()\n            } else {\n              const message = `204 HTTP response status code expected, but ${responseStatusCode} returned`\n              const error = new HttpError(\n                responseStatusCode,\n                message,\n                undefined,\n                '0',\n                undefined,\n                undefined,\n                headers\n              )\n              error.message = message\n              callbacks.error(error)\n            }\n          },\n        }\n        this.transport.send(\n          this.path,\n          lines.join('\\n'),\n          this.sendOptions,\n          callbacks\n        )\n      })\n    } else {\n      return Promise.resolve()\n    }\n  }\n\n  private _clearFlushTimeout(): void {\n    if (this._timeoutHandle !== undefined) {\n      clearTimeout(this._timeoutHandle)\n      this._timeoutHandle = undefined\n    }\n  }\n\n  writeRecord(record: string): void {\n    if (this.closed) {\n      throw new Error('writeApi: already closed!')\n    }\n    this.writeBuffer.add(record)\n  }\n  writeRecords(records: ArrayLike<string>): void {\n    if (this.closed) {\n      throw new Error('writeApi: already closed!')\n    }\n    for (let i = 0; i < records.length; i++) {\n      this.writeBuffer.add(records[i])\n    }\n  }\n  writePoint(point: Point): void {\n    if (this.closed) {\n      throw new Error('writeApi: already closed!')\n    }\n    const line = point.toLineProtocol(this)\n    if (line) this.writeBuffer.add(line)\n  }\n  writePoints(points: ArrayLike<Point>): void {\n    if (this.closed) {\n      throw new Error('writeApi: already closed!')\n    }\n    for (let i = 0; i < points.length; i++) {\n      const line = points[i].toLineProtocol(this)\n      if (line) this.writeBuffer.add(line)\n    }\n  }\n  async flush(withRetryBuffer?: boolean): Promise<void> {\n    await this.writeBuffer.flush()\n    if (withRetryBuffer) {\n      return await this.retryBuffer.flush()\n    }\n  }\n  close(): Promise<void> {\n    const retVal = this.writeBuffer.flush().finally(() => {\n      const remaining = this.retryBuffer.close()\n      if (remaining) {\n        Log.error(\n          `Retry buffer closed with ${remaining} items that were not written to InfluxDB!`,\n          null\n        )\n      }\n      this.closed = true\n    })\n    return retVal\n  }\n  dispose(): number {\n    this._clearFlushTimeout()\n    this.closed = true\n    return this.retryBuffer.close() + this.writeBuffer.length\n  }\n\n  // PointSettings\n  defaultTags: {[key: string]: string} | undefined\n  useDefaultTags(tags: {[key: string]: string}): WriteApi {\n    this.defaultTags = tags\n    return this\n  }\n  convertTime(value: string | number | Date | undefined): string | undefined {\n    if (value === undefined) {\n      return this.currentTime()\n    } else if (typeof value === 'string') {\n      return value.length > 0 ? value : undefined\n    } else if (value instanceof Date) {\n      return this.dateToProtocolTimestamp(value)\n    } else if (typeof value === 'number') {\n      return String(Math.floor(value))\n    } else {\n      return String(value)\n    }\n  }\n}\n", "import {CommunicationObserver, Headers} from '../results'\n\ntype CompleteObserver = Omit<\n  Required<CommunicationObserver<any>>,\n  'useCancellable' | 'useResume'\n> &\n  Pick<CommunicationObserver<any>, 'useResume' | 'useCancellable'>\n\nexport default function completeCommunicationObserver(\n  callbacks: Partial<CommunicationObserver<any>> = {}\n): CompleteObserver {\n  let state = 0\n  const retVal: CompleteObserver = {\n    next: (data: any): void | boolean => {\n      if (\n        state === 0 &&\n        callbacks.next &&\n        data !== null &&\n        data !== undefined\n      ) {\n        return callbacks.next(data)\n      }\n    },\n    error: (error: Error): void => {\n      /* istanbul ignore else propagate error at most once */\n      if (state === 0) {\n        state = 1\n        /* istanbul ignore else safety check */\n        if (callbacks.error) callbacks.error(error)\n      }\n    },\n    complete: (): void => {\n      if (state === 0) {\n        state = 2\n        /* istanbul ignore else safety check */\n        if (callbacks.complete) callbacks.complete()\n      }\n    },\n    responseStarted: (headers: Headers, statusCode?: number): void => {\n      if (callbacks.responseStarted)\n        callbacks.responseStarted(headers, statusCode)\n    },\n  }\n  if (callbacks.useCancellable) {\n    retVal.useCancellable = callbacks.useCancellable.bind(callbacks)\n  }\n  if (callbacks.useResume) {\n    retVal.useResume = callbacks.useResume.bind(callbacks)\n  }\n  return retVal\n}\n", "import {Transport, SendOptions} from '../../transport'\nimport {ConnectionOptions} from '../../options'\nimport {AbortError, HttpError} from '../../errors'\nimport completeCommunicationObserver from '../completeCommunicationObserver'\nimport {Log} from '../../util/logger'\nimport {\n  Chunk<PERSON>ombiner,\n  CommunicationObserver,\n  createTextDecoder<PERSON>ombiner,\n  Headers,\n  ResponseStartedFn,\n} from '../../results'\n\nfunction getResponseHeaders(response: Response): Headers {\n  const headers: Headers = {}\n  response.headers.forEach((value: string, key: string) => {\n    const previous = headers[key]\n    if (previous === undefined) {\n      headers[key] = value\n    } else if (Array.isArray(previous)) {\n      previous.push(value)\n    } else {\n      headers[key] = [previous, value]\n    }\n  })\n  return headers\n}\n\n/**\n * Transport layer that use browser fetch.\n */\nexport default class FetchTransport implements Transport {\n  chunkCombiner: ChunkCombiner = createTextDecoder<PERSON>ombiner()\n  private defaultHeaders: {[key: string]: string}\n  private url: string\n  constructor(private connectionOptions: ConnectionOptions) {\n    this.defaultHeaders = {\n      'content-type': 'application/json; charset=utf-8',\n      // 'User-Agent': `influxdb-client-js/${CLIENT_LIB_VERSION}`, // user-agent can hardly be customized https://github.com/influxdata/influxdb-client-js/issues/262\n      ...connectionOptions.headers,\n    }\n    if (this.connectionOptions.token) {\n      this.defaultHeaders['Authorization'] =\n        'Token ' + this.connectionOptions.token\n    }\n    this.url = String(this.connectionOptions.url)\n    if (this.url.endsWith('/')) {\n      this.url = this.url.substring(0, this.url.length - 1)\n    }\n    // https://github.com/influxdata/influxdb-client-js/issues/263\n    // don't allow /api/v2 suffix to avoid future problems\n    if (this.url.endsWith('/api/v2')) {\n      this.url = this.url.substring(0, this.url.length - '/api/v2'.length)\n      Log.warn(\n        `Please remove '/api/v2' context path from InfluxDB base url, using ${this.url} !`\n      )\n    }\n  }\n  send(\n    path: string,\n    body: string,\n    options: SendOptions,\n    callbacks?: Partial<CommunicationObserver<Uint8Array>> | undefined\n  ): void {\n    const observer = completeCommunicationObserver(callbacks)\n    let cancelled = false\n    let signal = (options as any).signal\n    let pausePromise: Promise<void> | undefined\n    const resumeQuickly = () => {}\n    let resume = resumeQuickly\n    if (callbacks && callbacks.useCancellable) {\n      const controller = new AbortController()\n      if (!signal) {\n        signal = controller.signal\n        options = {...options, signal}\n      }\n      // resume data reading so that it can exit on abort signal\n      signal.addEventListener('abort', () => {\n        resume()\n      })\n      callbacks.useCancellable({\n        cancel() {\n          cancelled = true\n          controller.abort()\n        },\n        isCancelled() {\n          return cancelled || signal.aborted\n        },\n      })\n    }\n    this.fetch(path, body, options)\n      .then(async (response) => {\n        if (callbacks?.responseStarted) {\n          observer.responseStarted(\n            getResponseHeaders(response),\n            response.status\n          )\n        }\n        await this.throwOnErrorResponse(response)\n        if (response.body) {\n          const reader = response.body.getReader()\n          let chunk: ReadableStreamReadResult<Uint8Array>\n          do {\n            if (pausePromise) {\n              await pausePromise\n            }\n            if (cancelled) {\n              break\n            }\n            chunk = await reader.read()\n            if (observer.next(chunk.value) === false) {\n              const useResume = observer.useResume\n              if (!useResume) {\n                const msg = 'Unable to pause, useResume is not configured!'\n                await reader.cancel(msg)\n                return Promise.reject(new Error(msg))\n              }\n              pausePromise = new Promise((resolve) => {\n                resume = () => {\n                  resolve()\n                  pausePromise = undefined\n                  resume = resumeQuickly\n                }\n                useResume(resume)\n              })\n            }\n          } while (!chunk.done)\n        } else if (response.arrayBuffer) {\n          const buffer = await response.arrayBuffer()\n          observer.next(new Uint8Array(buffer))\n        } else {\n          const text = await response.text()\n          observer.next(new TextEncoder().encode(text))\n        }\n      })\n      .catch((e) => {\n        if (!cancelled) {\n          observer.error(e)\n        }\n      })\n      .finally(() => observer.complete())\n  }\n  private async throwOnErrorResponse(response: Response): Promise<void> {\n    if (response.status >= 300) {\n      let text = ''\n      try {\n        text = await response.text()\n        if (!text) {\n          const headerError = response.headers.get('x-influxdb-error')\n          if (headerError) {\n            text = headerError\n          }\n        }\n      } catch (e) {\n        Log.warn('Unable to receive error body', e)\n\n        throw new HttpError(\n          response.status,\n          response.statusText,\n          undefined,\n          response.headers.get('retry-after'),\n          response.headers.get('content-type'),\n          undefined,\n          getResponseHeaders(response)\n        )\n      }\n      throw new HttpError(\n        response.status,\n        response.statusText,\n        text,\n        response.headers.get('retry-after'),\n        response.headers.get('content-type'),\n        undefined,\n        getResponseHeaders(response)\n      )\n    }\n  }\n\n  async *iterate(\n    path: string,\n    body: string,\n    options: SendOptions\n  ): AsyncIterableIterator<Uint8Array> {\n    const response = await this.fetch(path, body, options)\n    await this.throwOnErrorResponse(response)\n    if (response.body) {\n      const reader = response.body.getReader()\n      for (;;) {\n        const {value, done} = await reader.read()\n        if (done) {\n          break\n        }\n        if (options.signal?.aborted) {\n          await response.body.cancel()\n          throw new AbortError()\n        }\n        yield value\n      }\n    } else if (response.arrayBuffer) {\n      const buffer = await response.arrayBuffer()\n      yield new Uint8Array(buffer)\n    } else {\n      const text = await response.text()\n      yield new TextEncoder().encode(text)\n    }\n  }\n\n  async request(\n    path: string,\n    body: any,\n    options: SendOptions,\n    responseStarted?: ResponseStartedFn\n  ): Promise<any> {\n    const response = await this.fetch(path, body, options)\n    const {headers} = response\n    const responseContentType = headers.get('content-type') || ''\n    if (responseStarted) {\n      responseStarted(getResponseHeaders(response), response.status)\n    }\n\n    await this.throwOnErrorResponse(response)\n    const responseType = options.headers?.accept ?? responseContentType\n    if (responseType.includes('json')) {\n      return await response.json()\n    } else if (\n      responseType.includes('text') ||\n      responseType.startsWith('application/csv')\n    ) {\n      return await response.text()\n    }\n  }\n\n  private fetch(\n    path: string,\n    body: any,\n    options: SendOptions\n  ): Promise<Response> {\n    const {method, headers, ...other} = options\n    const url = `${this.url}${path}`\n    const request: RequestInit = {\n      method: method,\n      body:\n        method === 'GET' || method === 'HEAD'\n          ? undefined\n          : typeof body === 'string'\n            ? body\n            : JSON.stringify(body),\n      headers: {\n        ...this.defaultHeaders,\n        ...headers,\n      },\n      credentials: 'omit' as const,\n      // override with custom transport options\n      ...this.connectionOptions.transportOptions,\n      // allow to specify custom options, such as signal, in SendOptions\n      ...other,\n    }\n    this.requestDecorator(request, options, url)\n    return fetch(url, request)\n  }\n\n  /**\n   * RequestDecorator allows to modify requests before sending.\n   *\n   * The following example shows a function that adds gzip\n   * compression of requests using pako.js.\n   *\n   * ```ts\n   * const client = new InfluxDB({url: 'http://a'})\n   * client.transport.requestDecorator = function(request, options) {\n   *   const body = request.body\n   *   if (\n   *     typeof body === 'string' &&\n   *     options.gzipThreshold !== undefined &&\n   *     body.length > options.gzipThreshold\n   *   ) {\n   *     request.headers['content-encoding'] = 'gzip'\n   *     request.body = pako.gzip(body)\n   *   }\n   * }\n   * ```\n   */\n  public requestDecorator: (\n    request: RequestInit,\n    options: SendOptions,\n    url: string\n  ) => void = function () {}\n}\n", "import {Observable} from '../observable'\nimport QueryApi, {QueryOptions} from '../QueryApi'\nimport {Transport} from '../transport'\nimport {\n  CommunicationObserver,\n  FluxResultObserver,\n  FluxTableMetaData,\n  Row,\n  AnnotatedCSVResponse,\n  IterableResultExecutor,\n} from '../results'\nimport {ParameterizedQuery} from '../query/flux'\nimport {APIExecutor} from '../results/ObservableQuery'\n\nconst DEFAULT_dialect: any = {\n  header: true,\n  delimiter: ',',\n  quoteChar: '\"',\n  commentPrefix: '#',\n  annotations: ['datatype', 'group', 'default'],\n}\n\nexport class QueryApiImpl implements QueryApi {\n  private options: QueryOptions\n  constructor(\n    private transport: Transport,\n    private createCSVResponse: (\n      executor: APIExecutor,\n      iterableResultExecutor: IterableResultExecutor\n    ) => AnnotatedCSVResponse,\n    org: string | QueryOptions\n  ) {\n    this.options = typeof org === 'string' ? {org} : org\n  }\n\n  with(options: Partial<QueryOptions>): Query<PERSON>pi {\n    return new QueryApiImpl(this.transport, this.createCSVResponse, {\n      ...this.options,\n      ...options,\n    })\n  }\n\n  response(query: string | ParameterizedQuery): AnnotatedCSVResponse {\n    const {org, type, gzip, headers} = this.options\n    const path = `/api/v2/query?org=${encodeURIComponent(org)}`\n    const body = JSON.stringify(\n      this.decorateRequest({\n        query: query.toString(),\n        dialect: DEFAULT_dialect,\n        type,\n      })\n    )\n    const options = {\n      method: 'POST',\n      headers: {\n        'content-type': 'application/json; encoding=utf-8',\n        'accept-encoding': gzip ? 'gzip' : 'identity',\n        ...headers,\n      },\n    }\n    return this.createCSVResponse(\n      (consumer) => this.transport.send(path, body, options, consumer),\n      () => this.transport.iterate(path, body, options)\n    )\n  }\n\n  iterateLines(query: string | ParameterizedQuery): AsyncIterable<string> {\n    return this.response(query).iterateLines()\n  }\n  iterateRows(query: string | ParameterizedQuery): AsyncIterable<Row> {\n    return this.response(query).iterateRows()\n  }\n  lines(query: string | ParameterizedQuery): Observable<string> {\n    return this.response(query).lines()\n  }\n\n  rows(query: string | ParameterizedQuery): Observable<Row> {\n    return this.response(query).rows()\n  }\n\n  queryLines(\n    query: string | ParameterizedQuery,\n    consumer: CommunicationObserver<string>\n  ): void {\n    return this.response(query).consumeLines(consumer)\n  }\n\n  queryRows(\n    query: string | ParameterizedQuery,\n    consumer: FluxResultObserver<string[]>\n  ): void {\n    return this.response(query).consumeRows(consumer)\n  }\n\n  collectRows<T>(\n    query: string | ParameterizedQuery,\n    rowMapper?: (\n      values: string[],\n      tableMeta: FluxTableMetaData\n    ) => T | undefined\n  ): Promise<Array<T>> {\n    return this.response(query).collectRows(rowMapper)\n  }\n\n  collectLines(query: string | ParameterizedQuery): Promise<Array<string>> {\n    return this.response(query).collectLines()\n  }\n\n  queryRaw(query: string | ParameterizedQuery): Promise<string> {\n    const {org, type, gzip, headers} = this.options\n    return this.transport.request(\n      `/api/v2/query?org=${encodeURIComponent(org)}`,\n      JSON.stringify(\n        this.decorateRequest({\n          query: query.toString(),\n          dialect: DEFAULT_dialect,\n          type,\n        })\n      ),\n      {\n        method: 'POST',\n        headers: {\n          accept: 'text/csv',\n          'accept-encoding': gzip ? 'gzip' : 'identity',\n          'content-type': 'application/json; encoding=utf-8',\n          ...headers,\n        },\n      }\n    )\n  }\n\n  private decorateRequest(request: any): any {\n    if (typeof this.options.now === 'function') {\n      request.now = this.options.now()\n    }\n    // https://docs.influxdata.com/influxdb/latest/api/#operation/PostQuery requires type\n    request.type = this.options.type ?? 'flux'\n    return request\n  }\n}\n\nexport default QueryApiImpl\n", "import {\n  CommunicationObserver,\n  FluxResultObserver,\n  FluxTableMetaData,\n  Row,\n  linesToTables,\n  ChunkCombiner,\n  chunksToLines,\n  chunksToLinesIterable,\n  linesToRowsIterable,\n} from '../results'\nimport {Observable} from '../observable'\nimport {\n  AnnotatedCSVResponse,\n  IterableResultExecutor,\n} from './AnnotatedCSVResponse'\nimport ObservableQuery, {APIExecutor} from './ObservableQuery'\n\nexport function defaultRowMapping(\n  values: string[],\n  tableMeta: FluxTableMetaData\n): Record<string, any> {\n  return tableMeta.toObject(values)\n}\n\n/**\n * AnnotatedCsvResponseImpl is an implementation AnnotatedCsvResponse\n * that uses the supplied executor to supply a response data stream.\n */\nexport class AnnotatedCSVResponseImpl implements AnnotatedCSVResponse {\n  constructor(\n    private executor: APIExecutor,\n    private iterableResultExecutor: IterableResultExecutor,\n    private chunkCombiner: Chunk<PERSON>ombiner\n  ) {}\n  iterateLines(): AsyncIterable<string> {\n    return chunksToLinesIterable(this.iterableResultExecutor())\n  }\n  iterateRows(): AsyncIterable<Row> {\n    return linesToRowsIterable(\n      chunksToLinesIterable(this.iterableResultExecutor())\n    )\n  }\n  lines(): Observable<string> {\n    return new ObservableQuery(this.executor, (observer) =>\n      chunksToLines(observer, this.chunkCombiner)\n    )\n  }\n\n  rows(): Observable<Row> {\n    return new ObservableQuery(this.executor, (observer) => {\n      return chunksToLines(\n        linesToTables({\n          next(values, tableMeta) {\n            observer.next({values, tableMeta})\n          },\n          error(e) {\n            observer.error(e)\n          },\n          complete() {\n            observer.complete()\n          },\n        }),\n        this.chunkCombiner\n      )\n    })\n  }\n\n  consumeLines(consumer: CommunicationObserver<string>): void {\n    this.executor(chunksToLines(consumer, this.chunkCombiner))\n  }\n\n  consumeRows(consumer: FluxResultObserver<string[]>): void {\n    this.executor(chunksToLines(linesToTables(consumer), this.chunkCombiner))\n  }\n\n  collectRows<T>(\n    rowMapper: (\n      values: string[],\n      tableMeta: FluxTableMetaData\n    ) => T | undefined = defaultRowMapping as (\n      values: string[],\n      tableMeta: FluxTableMetaData\n    ) => T | undefined\n  ): Promise<Array<T>> {\n    const retVal: Array<T> = []\n    return new Promise((resolve, reject) => {\n      this.consumeRows({\n        next(values: string[], tableMeta: FluxTableMetaData): void {\n          const toAdd = rowMapper.call(this, values, tableMeta)\n          if (toAdd !== undefined) {\n            retVal.push(toAdd)\n          }\n        },\n        error(error: Error): void {\n          reject(error)\n        },\n        complete(): void {\n          resolve(retVal)\n        },\n      })\n    })\n  }\n\n  collectLines(): Promise<Array<string>> {\n    const retVal: Array<string> = []\n    return new Promise((resolve, reject) => {\n      this.consumeLines({\n        next(line: string): void {\n          retVal.push(line)\n        },\n        error(error: Error): void {\n          reject(error)\n        },\n        complete(): void {\n          resolve(retVal)\n        },\n      })\n    })\n  }\n}\n", "import Write<PERSON><PERSON> from './WriteApi'\nimport {ClientOptions, WriteOptions, WritePrecisionType} from './options'\nimport WriteApiImpl from './impl/WriteApiImpl'\nimport {IllegalArgumentError} from './errors'\nimport {Transport} from './transport'\n// replaced by ./impl/browser/FetchTransport in browser builds\nimport TransportImpl from './impl/browser/FetchTransport'\nimport Query<PERSON><PERSON>, {QueryOptions} from './QueryApi'\nimport QueryApiImpl from './impl/QueryApiImpl'\nimport {\n  AnnotatedCSVResponse,\n  APIExecutor,\n  IterableResultExecutor,\n} from './results'\nimport {AnnotatedCSVResponseImpl} from './results/AnnotatedCSVResponseImpl'\n\n/**\n * InfluxDB entry point that configures communication with InfluxDB server\n * and provide APIs to write and query data.\n */\nexport default class InfluxDB {\n  private _options: ClientOptions\n  readonly transport: Transport\n  readonly processCSVResponse: (\n    executor: APIExecutor,\n    iterableResultExecutor: IterableResultExecutor\n  ) => AnnotatedCSVResponse\n\n  /**\n   * Creates influxdb client options from an options object or url.\n   * @param options - client options\n   */\n  constructor(options: ClientOptions | string) {\n    if (typeof options === 'string') {\n      this._options = {url: options}\n    } else if (options !== null && typeof options === 'object') {\n      this._options = Object.assign({}, options)\n    } else {\n      throw new IllegalArgumentError('No url or configuration specified!')\n    }\n    const url = this._options.url\n    if (typeof url !== 'string')\n      throw new IllegalArgumentError('No url specified!')\n    if (url.endsWith('/')) this._options.url = url.substring(0, url.length - 1)\n    this.transport = this._options.transport ?? new TransportImpl(this._options)\n    delete this._options.token\n    this.processCSVResponse = (\n      executor: APIExecutor,\n      iterableResultExecutor: IterableResultExecutor\n    ): AnnotatedCSVResponse =>\n      new AnnotatedCSVResponseImpl(\n        executor,\n        iterableResultExecutor,\n        this.transport.chunkCombiner\n      )\n  }\n\n  /**\n   * Creates WriteApi for the supplied organization and bucket. BEWARE that returned instances must be closed\n   * in order to flush the remaining data and close already scheduled retry executions.\n   *\n   * @remarks\n   * Use {@link WriteOptions} to customize retry strategy options, data chunking\n   * and flushing options. See {@link DEFAULT_WriteOptions} to see the defaults.\n   *\n   * See also {@link https://github.com/influxdata/influxdb-client-js/blob/master/examples/write.mjs | write example},\n   * {@link https://github.com/influxdata/influxdb-client-js/blob/master/examples/writeAdvanced.mjs | writeAdvanced example},\n   * and {@link https://github.com/influxdata/influxdb-client-js/blob/master/examples/index.html | browser example}.\n   *\n   * @param org - Specifies the destination organization for writes. Takes either the ID or Name interchangeably.\n   * @param bucket - The destination bucket for writes.\n   * @param precision - Timestamp precision for line items.\n   * @param writeOptions - Custom write options.\n   * @returns WriteApi instance\n   */\n  getWriteApi(\n    org: string,\n    bucket: string,\n    precision: WritePrecisionType = 'ns',\n    writeOptions?: Partial<WriteOptions>\n  ): WriteApi {\n    return new WriteApiImpl(\n      this.transport,\n      org,\n      bucket,\n      precision,\n      writeOptions ?? this._options.writeOptions\n    )\n  }\n\n  /**\n   * Creates QueryApi for the supplied organization .\n   *\n   * @remarks\n   * See also {@link https://github.com/influxdata/influxdb-client-js/blob/master/examples/query.ts | query.ts example},\n   * {@link https://github.com/influxdata/influxdb-client-js/blob/master/examples/queryWithParams.mjs | queryWithParams.mjs example},\n   * {@link https://github.com/influxdata/influxdb-client-js/blob/master/examples/rxjs-query.ts | rxjs-query.ts example},\n   * and {@link https://github.com/influxdata/influxdb-client-js/blob/master/examples/index.html | browser example},\n   *\n   * @param org - organization or query options\n   * @returns QueryApi instance\n   */\n  getQueryApi(org: string | QueryOptions): QueryApi {\n    return new QueryApiImpl(this.transport, this.processCSVResponse, org)\n  }\n}\n"], "mappings": ";;;;;scAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,gBAAAE,EAAA,8BAAAC,GAAA,sCAAAC,EAAA,yBAAAC,EAAA,eAAAC,EAAA,cAAAC,EAAA,yBAAAC,EAAA,aAAAC,EAAA,iBAAAC,EAAA,QAAAC,EAAA,UAAAC,GAAA,yBAAAC,EAAA,mBAAAC,EAAA,qBAAAC,GAAA,kBAAAC,EAAA,0BAAAC,EAAA,kBAAAC,GAAA,uBAAAC,GAAA,0BAAAC,GAAA,4BAAAC,EAAA,8BAAAC,EAAA,gBAAAC,GAAA,4BAAAC,GAAA,WAAAC,EAAA,SAAAC,GAAA,aAAAC,GAAA,iBAAAC,GAAA,iBAAAC,GAAA,mBAAAC,GAAA,cAAAC,GAAA,gBAAAC,GAAA,eAAAC,GAAA,eAAAC,GAAA,kBAAAC,EAAA,0BAAAC,GAAA,wBAAAC,EAAA,kBAAAC,EAAA,uBAAAC,EAAA,kBAAAC,GAAA,oBAAAC,GAAA,4BAAAC,GAAA,8BAAAC,GAAA,8BAAAC,GAAA,cAAAC,GAAA,kBAAAC,GAAA,qBAAAC,EAAA,gBAAAC,EAAA,oBAAAC,EAAA,qBAAAC,KC2CO,SAASC,GAA2C,CACzD,IAAMC,EAAU,IAAI,YAAY,OAAO,EACvC,MAAO,CACL,OAAOC,EAAmBC,EAAgC,CACxD,IAAMC,EAAS,IAAI,WAAWF,EAAM,OAASC,EAAO,MAAM,EAC1D,OAAAC,EAAO,IAAIF,CAAK,EAChBE,EAAO,IAAID,EAAQD,EAAM,MAAM,EACxBE,CACT,EACA,KAAKC,EAAmBC,EAAeC,EAAyB,CAC9D,IAAMH,EAAS,IAAI,WAAWG,EAAMD,CAAK,EACzC,OAAAF,EAAO,IAAIC,EAAM,SAASC,EAAOC,CAAG,CAAC,EAC9BH,CACT,EACA,aAAaC,EAAmBC,EAAeC,EAAqB,CAClE,OAAON,EAAQ,OAAOI,EAAM,SAASC,EAAOC,CAAG,CAAC,CAClD,CACF,CACF,CClDO,SAASC,EACdC,EACAC,EACmC,CACnC,IAAMC,EAASD,GAAA,KAAAA,EAAiBE,EAA0B,EACtDC,EACAC,EAAW,GACXC,EAAS,GACTC,EAAS,GACTC,EAEJ,SAASC,EAAeC,EAAyB,CAC/C,IAAIC,EACAC,EAAQ,EASZ,IARIR,GAGFO,EAAQD,EAAM,SAAW,EAAI,EAAKN,EAAwB,OAC1DM,EAAQR,EAAO,OAAOE,EAAUM,CAAK,GAErCC,EAAQ,EAEHA,EAAQD,EAAM,QAAQ,CAC3B,IAAMG,EAAIH,EAAMC,CAAK,EACrB,GAAIE,IAAM,IACR,GAAI,CAACP,EAAQ,CAEX,IAAMQ,EAAMH,EAAQ,GAAKD,EAAMC,EAAQ,CAAC,IAAM,GAAKA,EAAQ,EAAIA,EAE/D,GAAIN,EACF,OAIF,GAFAE,EAASP,EAAO,KAAKE,EAAO,aAAaQ,EAAOE,EAAOE,CAAG,CAAC,IAAM,GACjEF,EAAQD,EAAQ,EACZJ,EACF,KAEJ,OACSM,IAAM,KACfP,EAAS,CAACA,GAEZK,GACF,CAMA,GALIC,EAAQF,EAAM,OAChBN,EAAWF,EAAO,KAAKQ,EAAOE,EAAOF,EAAM,MAAM,EAEjDN,EAAW,OAETG,EAAQ,CACV,GAAIP,EAAO,UAAW,CACpBA,EAAO,UAAU,IAAM,CACrBO,EAAS,GACTE,EAAe,IAAI,WAAW,CAAC,CAAC,CAClC,CAAC,EACD,MACF,CACAM,EAAO,MAAM,IAAI,MAAM,+CAA+C,CAAC,EACvER,EAAS,EACX,CACIC,IACFA,EAAa,EACbA,EAAe,OAEnB,CAEA,IAAMO,EAA4C,CAChD,KAAKL,EAA4B,CAC/B,GAAI,CAACL,EACH,GAAI,CACF,OAAAI,EAAeC,CAAK,EACb,CAACH,CACV,OAASS,EAAG,CACV,KAAK,MAAMA,CAAU,CACvB,CAEF,MAAO,EACT,EACA,MAAMC,EAAoB,CACnBZ,IACHA,EAAW,GACXL,EAAO,MAAMiB,CAAK,EAEtB,EACA,UAAiB,CACVZ,IACCD,GACFJ,EAAO,KAAKE,EAAO,aAAaE,EAAU,EAAGA,EAAS,MAAM,CAAC,EAE/DC,EAAW,GACXL,EAAO,SAAS,EAEpB,CACF,EACA,OAAIA,EAAO,iBACTe,EAAO,eAAkBG,GAA6B,CACpDlB,EAAO,gBACLA,EAAO,eAAe,CACpB,QAAe,CACbkB,EAAY,OAAO,EACnBd,EAAW,OACXW,EAAO,SAAS,CAClB,EACA,aAAuB,CACrB,OAAOG,EAAY,YAAY,CACjC,CACF,CAAC,CACL,GAEElB,EAAO,YACTe,EAAO,UAAaI,GAAkB,CACpCX,EAAeW,CACjB,GAGKJ,CACT,CCrHA,eAAuBK,EACrBC,EACAC,EAC+B,CAC/B,IAAMC,EAASD,GAAA,KAAAA,EAAiBE,EAA0B,EACtDC,EACAC,EAAS,GAEb,cAAeC,KAASN,EAAQ,CAC9B,IAAIO,EACAC,EAAQ,EAOZ,IANIJ,GACFG,EAAQH,EAAS,OACjBE,EAAQJ,EAAO,OAAOE,EAAUE,CAAK,GAErCC,EAAQ,EAEHA,EAAQD,EAAM,QAAQ,CAC3B,IAAMG,EAAIH,EAAMC,CAAK,EACrB,GAAIE,IAAM,IACR,GAAI,CAACJ,EAAQ,CAEX,IAAMK,EAAMH,EAAQ,GAAKD,EAAMC,EAAQ,CAAC,IAAM,GAAKA,EAAQ,EAAIA,EAC/D,MAAML,EAAO,aAAaI,EAAOE,EAAOE,CAAG,EAC3CF,EAAQD,EAAQ,CAClB,OACSE,IAAM,KACfJ,EAAS,CAACA,GAEZE,GACF,CACIC,EAAQF,EAAM,OAChBF,EAAWF,EAAO,KAAKI,EAAOE,EAAOF,EAAM,MAAM,EAEjDF,EAAW,MAEf,CACIA,IACF,MAAMF,EAAO,aAAaE,EAAU,EAAGA,EAAS,MAAM,EAE1D,CC9CO,IAAMO,EAAN,KAAmB,CAAnB,cAKL,KAAQ,OAAS,GAKjB,IAAI,OAAiB,CACnB,OAAO,KAAK,MACd,CACA,IAAI,MAAMC,EAAc,CAClBA,GAAO,CAAC,KAAK,eACf,KAAK,aAAe,IAAI,MAAM,EAAE,GAElC,KAAK,OAASA,CAChB,CAKA,WAA0B,CACxB,YAAK,MAAQ,GACN,IACT,CAQA,UAAUC,EAA2C,CACnD,GAAIA,GAAS,KACX,YAAK,gBAAkB,EAChB,CAAC,EAEV,IAAIC,EAAa,EACbC,EAAa,EACXC,EAAS,KAAK,OAAS,KAAK,aAAe,CAAC,EAC9CC,EAAQ,EACZ,QAASC,EAAI,EAAGA,EAAIL,EAAK,OAAQK,IAAK,CACpC,IAAMC,EAAIN,EAAKK,CAAC,EAChB,GAAIC,IAAM,KACR,GAAIL,EAAa,IAAM,EAAG,CACxB,IAAMF,EAAM,KAAK,SAASC,EAAME,EAAYG,EAAGJ,CAAU,EACrD,KAAK,OACPE,EAAOC,GAAO,EAAIL,EAElBI,EAAO,KAAKJ,CAAG,EAEjBG,EAAaG,EAAI,EACjBJ,EAAa,CACf,OACSK,IAAM,KACfL,GAEJ,CACA,IAAMF,EAAM,KAAK,SAASC,EAAME,EAAYF,EAAK,OAAQC,CAAU,EACnE,OAAI,KAAK,QACPE,EAAOC,CAAK,EAAIL,EAChB,KAAK,gBAAkBK,EAAQ,IAE/BD,EAAO,KAAKJ,CAAG,EACf,KAAK,gBAAkBI,EAAO,QAGzBA,CACT,CAEQ,SACNH,EACAO,EACAC,EACAP,EACQ,CACR,OAAIM,IAAUP,EAAK,OACV,GACEC,IAAe,EACjBD,EAAK,UAAUO,EAAOC,CAAG,EACvBP,IAAe,EACjBD,EAAK,UAAUO,EAAQ,EAAGC,EAAM,CAAC,EAGjCR,EAAK,UAAUO,EAAQ,EAAGC,EAAM,CAAC,EAAE,QAAQ,OAAQ,GAAG,CAEjE,CACF,ECzCA,IAAMC,EAAYC,GAAmBA,EAMxBC,EAA4D,CACvE,QAAUD,GAAoBA,IAAM,GAAK,KAAOA,IAAM,OACtD,aAAeA,GAAoBA,IAAM,GAAK,KAAO,CAACA,EACtD,KAAOA,GAAoBA,IAAM,GAAK,KAAO,CAACA,EAC9C,OAAOA,EAAgB,CACrB,OAAQA,EAAG,CACT,IAAK,GACH,OAAO,KACT,IAAK,OACH,OAAO,OAAO,kBAChB,IAAK,OACH,OAAO,OAAO,kBAChB,QACE,MAAO,CAACA,CACZ,CACF,EACA,OAAQD,EACR,aAAcA,EACd,SAAWC,GAAoBA,IAAM,GAAK,KAAOA,EACjD,mBAAqBA,GAAoBA,IAAM,GAAK,KAAOA,CAC7D,EAKME,EAAN,KAAqD,CAM5C,IAAIC,EAAoB,CAxFjC,IAAAC,EAyFI,IAAIC,EAAMF,EAAI,KAAK,KAAK,EACxB,OAAKE,IAAQ,IAAMA,IAAQ,SAAc,KAAK,eAC5CA,EAAM,KAAK,gBAELD,EAAAH,EAAgB,KAAK,QAAQ,IAA7B,KAAAG,EAAkCL,GAAUM,CAAG,CACzD,CACF,EACaC,EAAkC,OAAO,OAAO,CAC3D,MAAO,GACP,SAAU,GACV,MAAO,GACP,aAAc,GACd,MAAO,OAAO,iBACd,IAAK,IAAG,EACV,CAAC,EAMM,SAASC,GAAsC,CACpD,OAAO,IAAIL,CACb,CAOO,SAASM,GACdC,EACiB,CAxHnB,IAAAL,EAAAM,EAyHE,IAAMC,EAAS,IAAIT,EACnB,OAAAS,EAAO,MAAQ,OAAOF,EAAO,KAAK,EAClCE,EAAO,SAAWF,EAAO,SACzBE,EAAO,MAAQ,EAAQF,EAAO,MAC9BE,EAAO,cAAeP,EAAAK,EAAO,eAAP,KAAAL,EAAuB,GAC7CO,EAAO,OAAQD,EAAAD,EAAO,QAAP,KAAAC,EAAgB,EACxBC,CACT,CChGA,IAAMC,GAAuB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAG,EAE7D,SAASC,GAAsBC,EAA6B,CACjE,OAAOF,GAAqB,SAASE,CAAU,CACjD,CAGO,IAAMC,EAAN,MAAMC,UAA6B,KAAM,CAE9C,YAAYC,EAAiB,CAC3B,MAAMA,CAAO,EACb,KAAK,KAAO,uBACZ,OAAO,eAAe,KAAMD,EAAqB,SAAS,CAC5D,CACF,EAKaE,EAAN,MAAMC,UAAkB,KAAmC,CAUhE,YACWL,EACAM,EACAC,EACTC,EACSC,EACTN,EACAO,EACA,CACA,MAAM,EARG,gBAAAV,EACA,mBAAAM,EACA,UAAAC,EAEA,iBAAAE,EAKT,UAAO,eAAe,KAAMJ,EAAU,SAAS,EAE/C,KAAK,QAAUK,EAEXP,EACF,KAAK,QAAUA,UACNI,EAAM,CACf,GAAIE,GAAA,MAAAA,EAAa,WAAW,oBAC1B,GAAI,CACF,KAAK,KAAO,KAAK,MAAMF,CAAI,EAC3B,KAAK,QAAU,KAAK,KAAK,QACzB,KAAK,KAAO,KAAK,KAAK,IACxB,OAASI,EAAG,CAEZ,CAEG,KAAK,UACR,KAAK,QAAU,GAAGX,CAAU,IAAIM,CAAa,MAAMC,CAAI,GAE3D,MACE,KAAK,QAAU,GAAGP,CAAU,IAAIM,CAAa,GAE/C,KAAK,KAAO,YACZ,KAAK,cAAcE,CAAU,CAC/B,CAEQ,cAAcA,EAA8C,CAC9D,OAAOA,GAAe,SAEpB,WAAW,KAAKA,CAAU,EAC5B,KAAK,YAAc,SAASA,CAAU,EAEtC,KAAK,YAAc,EAGrB,KAAK,YAAc,CAEvB,CAEA,UAAoB,CAClB,OAAOT,GAAsB,KAAK,UAAU,CAC9C,CACA,YAAqB,CACnB,OAAO,KAAK,WACd,CACF,EAGMa,GAAc,CAClB,aACA,YACA,kBACA,YACA,eACA,eACA,OACF,EAOO,SAASC,GAAiBC,EAAqB,CACpD,GAAKA,EAEE,IAAI,OAAQA,EAAc,UAAa,WAC5C,MAAO,CAAC,CAAGA,EAAc,SAA2B,EAC/C,GAAKA,EAAc,MAAQF,GAAY,SAAUE,EAAc,IAAI,EACxE,MAAO,OAJP,OAAO,GAMT,MAAO,EACT,CAKO,SAASC,EAAcD,EAAeE,EAA8B,CACzE,GAAKF,EAEE,CACL,IAAIG,EACJ,OAAI,OAAQH,EAAc,YAAe,WAC9BA,EAAc,WAA4B,GAEnDG,EAAS,EAEPD,GAAeA,EAAc,EACxBC,EAAS,KAAK,MAAM,KAAK,OAAO,EAAID,CAAW,EAE/CC,EAEX,KAbE,OAAO,EAcX,CAGO,IAAMC,EAAN,MAAMC,UAA6B,KAAmC,CAE3E,aAAc,CACZ,MAAM,EACN,OAAO,eAAe,KAAMA,EAAqB,SAAS,EAC1D,KAAK,KAAO,uBACZ,KAAK,QAAU,mBACjB,CACA,UAAoB,CAClB,MAAO,EACT,CACA,YAAqB,CACnB,MAAO,EACT,CACF,EAGaC,EAAN,MAAMC,UAAmB,KAAmC,CAEjE,aAAc,CACZ,MAAM,EACN,KAAK,KAAO,aACZ,OAAO,eAAe,KAAMA,EAAW,SAAS,EAChD,KAAK,QAAU,kBACjB,CACA,UAAoB,CAClB,MAAO,EACT,CACA,YAAqB,CACnB,MAAO,EACT,CACF,ECxLO,SAASC,IAAgC,CAC9CC,EAAgB,kBAAkB,EAAKC,GACrCA,IAAM,GAAK,KAAO,IAAI,KAAK,KAAK,MAAMA,CAAC,CAAC,CAC5C,CAQO,SAASC,IAAkC,CAChDF,EAAgB,kBAAkB,EAAKC,GACrCA,IAAM,GAAK,KAAO,KAAK,MAAMA,CAAC,CAClC,CAKO,SAASE,IAAkC,CAChDH,EAAgB,kBAAkB,EAAKC,GACrCA,IAAM,GAAK,KAAOA,CACtB,CAsCA,IAAMG,EAAN,KAAyD,CAEvD,YAAYC,EAA4B,CACtCA,EAAQ,QAAQ,CAACC,EAAKC,IAAOD,EAAI,MAAQC,CAAE,EAC3C,KAAK,QAAUF,CACjB,CACA,OAAOG,EAAeC,EAAuB,GAAuB,CAClE,QAASF,EAAI,EAAGA,EAAI,KAAK,QAAQ,OAAQA,IAAK,CAC5C,IAAMD,EAAM,KAAK,QAAQC,CAAC,EAC1B,GAAID,EAAI,QAAUE,EAAO,OAAOF,CAClC,CACA,GAAIG,EACF,MAAM,IAAIC,EAAqB,UAAUF,CAAK,aAAa,EAE7D,OAAOG,CACT,CACA,SAASC,EAAqC,CAC5C,IAAMC,EAAW,CAAC,EAClB,QAASN,EAAI,EAAGA,EAAI,KAAK,QAAQ,QAAUA,EAAIK,EAAI,OAAQL,IAAK,CAC9D,IAAMO,EAAS,KAAK,QAAQP,CAAC,EAC7BM,EAAIC,EAAO,KAAK,EAAIA,EAAO,IAAIF,CAAG,CACpC,CACA,OAAOC,CACT,CACA,IAAID,EAAeE,EAAqB,CACtC,OAAO,KAAK,OAAOA,EAAQ,EAAK,EAAE,IAAIF,CAAG,CAC3C,CACF,EAOO,SAASG,EACdV,EACmB,CACnB,OAAO,IAAID,EAAsBC,CAAO,CAC1C,CClGO,SAASW,EACdC,EAC+B,CAC/B,IAAMC,EAAW,IAAIC,EAAa,EAAE,UAAU,EAC1CC,EACAC,EAAa,GACbC,EAAmB,EACnBC,EACEC,EAAwC,CAC5C,MAAMC,EAAoB,CACxBR,EAAS,MAAMQ,CAAK,CACtB,EACA,KAAKC,EAA8B,CACjC,GAAIA,IAAS,GACXL,EAAa,GACbD,EAAU,WACL,CACL,IAAMO,EAAST,EAAS,UAAUQ,CAAI,EAChCE,EAAOV,EAAS,gBACtB,GAAIG,EAAY,CAEd,GAAI,CAACD,EAAS,CACZA,EAAU,IAAI,MAAMQ,CAAI,EACxB,QAASC,EAAI,EAAGA,EAAID,EAAMC,IACxBT,EAAQS,CAAC,EAAIC,EAAmB,CAEpC,CACA,GAAKH,EAAO,CAAC,EAAE,WAAW,GAAG,GAatB,GAAIA,EAAO,CAAC,IAAM,YACvB,QAASE,EAAI,EAAGA,EAAID,EAAMC,IACxBT,EAAQS,CAAC,EAAE,SAAWF,EAAOE,CAAC,UAEvBF,EAAO,CAAC,IAAM,WACvB,QAASE,EAAI,EAAGA,EAAID,EAAMC,IACxBT,EAAQS,CAAC,EAAE,aAAeF,EAAOE,CAAC,UAE3BF,EAAO,CAAC,IAAM,SACvB,QAASE,EAAI,EAAGA,EAAID,EAAMC,IACxBT,EAAQS,CAAC,EAAE,MAAQF,EAAOE,CAAC,EAAE,CAAC,IAAM,QAvBR,CAE1BF,EAAO,CAAC,IAAM,IAChBL,EAAmB,EACnBF,EAAUA,EAAQ,MAAM,CAAC,GAEzBE,EAAmB,EAErB,QAASO,EAAIP,EAAkBO,EAAID,EAAMC,IACvCT,EAAQS,EAAIP,CAAgB,EAAE,MAAQK,EAAOE,CAAC,EAEhDN,EAAWQ,EAAwBX,CAAO,EAC1CC,EAAa,EACf,CAaF,KACE,QAAOJ,EAAS,KAAKU,EAAO,MAAML,EAAkBM,CAAI,EAAGL,CAAQ,CAEvE,CACA,MAAO,EACT,EACA,UAAiB,CACfN,EAAS,SAAS,CACpB,CACF,EACA,OAAIA,EAAS,iBACXO,EAAO,eAAiBP,EAAS,eAAe,KAAKA,CAAQ,GAE3DA,EAAS,YACXO,EAAO,UAAYP,EAAS,UAAU,KAAKA,CAAQ,GAE9CO,CACT,CCnEA,eAAuBQ,EACrBC,EAC4B,CAC5B,IAAMC,EAAW,IAAIC,EAAa,EAAE,UAAU,EAC1CC,EACAC,EAAa,GACbC,EAAmB,EACnBC,EACJ,cAAiBC,KAAQP,EACvB,GAAIO,IAAS,GACXH,EAAa,GACbD,EAAU,WACL,CACL,IAAMK,EAASP,EAAS,UAAUM,CAAI,EAChCE,EAAOR,EAAS,gBACtB,GAAIG,EAAY,CAEd,GAAI,CAACD,EAAS,CACZA,EAAU,IAAI,MAAMM,CAAI,EACxB,QAASC,EAAI,EAAGA,EAAID,EAAMC,IACxBP,EAAQO,CAAC,EAAIC,EAAmB,CAEpC,CACA,GAAKH,EAAO,CAAC,EAAE,WAAW,GAAG,GAatB,GAAIA,EAAO,CAAC,IAAM,YACvB,QAASE,EAAI,EAAGA,EAAID,EAAMC,IACxBP,EAAQO,CAAC,EAAE,SAAWF,EAAOE,CAAC,UAEvBF,EAAO,CAAC,IAAM,WACvB,QAASE,EAAI,EAAGA,EAAID,EAAMC,IACxBP,EAAQO,CAAC,EAAE,aAAeF,EAAOE,CAAC,UAE3BF,EAAO,CAAC,IAAM,SACvB,QAASE,EAAI,EAAGA,EAAID,EAAMC,IACxBP,EAAQO,CAAC,EAAE,MAAQF,EAAOE,CAAC,EAAE,CAAC,IAAM,QAvBR,CAE1BF,EAAO,CAAC,IAAM,IAChBH,EAAmB,EACnBF,EAAUA,EAAQ,MAAM,CAAC,GAEzBE,EAAmB,EAErB,QAASK,EAAIL,EAAkBK,EAAID,EAAMC,IACvCP,EAAQO,EAAIL,CAAgB,EAAE,MAAQG,EAAOE,CAAC,EAEhDJ,EAAWM,EAAwBT,CAAO,EAC1CC,EAAa,EACf,CAaF,MACE,KAAM,CACJ,OAAQI,EAAO,MAAMH,EAAkBI,CAAI,EAC3C,UACEH,CACJ,CAEJ,CAEJ,CClEO,SAASO,GACdC,EACAC,EACM,CACN,IAAIC,EAAS,GACTC,EAAQ,EACRC,EAAQ,EAEZ,KAAOA,EAAQJ,EAAO,QAAQ,CAC5B,IAAMK,EAAIL,EAAO,WAAWI,CAAK,EACjC,GAAIC,IAAM,IACR,GAAI,CAACH,EAAQ,CAEX,IAAMI,EACJF,EAAQ,GAAKJ,EAAO,WAAWI,EAAQ,CAAC,IAAM,GAAKA,EAAQ,EAAIA,EAEjEH,EAAO,KAAKD,EAAO,UAAUG,EAAOG,CAAG,CAAC,EACxCH,EAAQC,EAAQ,CAClB,OACSC,IAAM,KACfH,EAAS,CAACA,GAEZE,GACF,CACID,EAAQC,GACVH,EAAO,KAAKD,EAAO,UAAUG,EAAOC,CAAK,CAAC,EAE5CH,EAAO,SAAS,CAClB,CC3BO,IAAMM,EAGV,OAAO,QAAW,YAAc,OAAO,YAAe,eCIzD,IAAMC,EAAN,KAAgD,CAIvC,YAAYC,EAAgCC,EAAuB,CAF1E,KAAQ,SAAW,GAGjB,GAAI,CACFA,EAAS,CACP,KAAOC,GAAU,CACfF,EAAS,KAAKE,CAAK,CACrB,EACA,MAAQC,GAAM,CACZ,KAAK,SAAW,GAChBH,EAAS,MAAMG,CAAC,CAClB,EACA,SAAU,IAAM,CACd,KAAK,SAAW,GAChBH,EAAS,SAAS,CACpB,EACA,eAAiBI,GAAM,CACrB,KAAK,YAAcA,CACrB,CACF,CAAC,CACH,OAASD,EAAG,CACV,KAAK,SAAW,GAChBH,EAAS,MAAMG,CAAC,CAClB,CACF,CAEA,IAAW,QAAkB,CAC3B,OAAO,KAAK,QACd,CAEO,aAAoB,CAjD7B,IAAAE,GAkDIA,EAAA,KAAK,cAAL,MAAAA,EAAkB,SAClB,KAAK,SAAW,EAClB,CACF,EAEA,SAASC,GAAa,CAAC,CAEvB,SAASC,GAAoBP,EAA6C,CACxE,GAAM,CAAC,KAAAQ,EAAM,MAAAC,EAAO,SAAAC,CAAQ,EAAIV,EAEhC,MAAO,CACL,KAAMQ,EAAOA,EAAK,KAAKR,CAAQ,EAAIM,EACnC,MAAOG,EAAQA,EAAM,KAAKT,CAAQ,EAAIM,EACtC,SAAUI,EAAWA,EAAS,KAAKV,CAAQ,EAAIM,CACjD,CACF,CAEA,IAAqBK,EAArB,KAAiE,CACxD,YACYV,EACAW,EACjB,CAFiB,cAAAX,EACA,eAAAW,CAChB,CAEI,UACLC,EACAJ,EACAC,EACc,CACd,IAAMV,EAAWO,GACf,OAAOM,GAAmB,UAAYA,IAAmB,KACrD,CAAC,KAAMA,EAAgB,MAAAJ,EAAO,SAAAC,CAAQ,EACtCG,CACN,EAEA,OAAO,IAAId,EAAkB,KAAK,UAAUC,CAAQ,EAAG,KAAK,QAAQ,CACtE,CAEA,CAAQc,CAAgB,GAAU,CAChC,OAAO,IACT,CAKF,ECtDO,IAAMC,GAAwD,CACnE,QAAS,GACX,EAyFaC,EAAoC,CAC/C,YAAa,IACb,cAAe,IACf,cAAe,MACf,gBAAiB,EACjB,YAAa,EACf,EAGaC,EAAqC,CAChD,UAAW,IACX,cAAe,IACf,cAAe,IACf,YAAa,UAAY,CAAC,EAC1B,aAAc,UAAY,CAAC,EAC3B,kBAAmB,UAAY,CAAC,EAChC,WAAY,EACZ,aAAc,KACd,eAAgB,KAEhB,YAAa,IACb,cAAe,IACf,cAAe,MACf,gBAAiB,EACjB,cAAe,IACf,YAAa,EACf,EC7JA,SAASC,GACPC,EACAC,EAC2B,CAC3B,OAAO,SAAUC,EAAuB,CACtC,IAAIC,EAAS,GACTC,EAAO,EACPC,EAAI,EACR,KAAOA,EAAIH,EAAM,QAAQ,CACvB,IAAMI,EAAQN,EAAW,QAAQE,EAAMG,CAAC,CAAC,EACrCC,GAAS,IACXH,GAAUD,EAAM,UAAUE,EAAMC,CAAC,EACjCF,GAAUF,EAAaK,CAAK,EAC5BF,EAAOC,EAAI,GAEbA,GACF,CACA,OAAID,GAAQ,EACHF,GACEE,EAAOF,EAAM,SACtBC,GAAUD,EAAM,UAAUE,EAAMF,EAAM,MAAM,GAEvCC,EACT,CACF,CACA,SAASI,GACPP,EACAC,EAC2B,CAC3B,IAAMO,EAAUT,GAAcC,EAAYC,CAAY,EACtD,OAAQC,GAA0B,IAAMM,EAAQN,CAAK,EAAI,GAC3D,CAKO,IAAMO,EAAS,CAIpB,YAAaV,GAAc;AAAA,KAAY,CAAC,MAAO,MAAO,MAAO,MAAO,KAAK,CAAC,EAI1E,OAAQQ,GAAoB,MAAO,CAAC,MAAO,MAAM,CAAC,EAKlD,IAAKR,GAAc;AAAA,KAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,CAAC,CAC5E,ECjDA,IAAMW,GAAc,YAGb,SAASC,GAAiBC,EAAuB,CAKpD,MAAO,EAEX,CACAD,GAAiB,EAAI,EAIrB,IAAIE,GAAa,KAAK,IAAI,EACtBC,GAAgB,EACpB,SAASC,IAAgB,CAsBhB,CACL,IAAMC,EAAS,KAAK,IAAI,EACpBA,IAAWH,IACbA,GAAaG,EACbF,GAAgB,GAEhBA,KAEF,IAAMC,EAAQ,OAAOD,EAAa,EAClC,OAAO,OAAOE,CAAM,EAAIC,GAAY,OAAO,EAAG,EAAIF,EAAM,MAAM,EAAIA,CACpE,CACF,CAEA,SAASG,IAAiB,CAQtB,OAAO,OAAO,KAAK,IAAI,CAAC,EAAID,GAAY,OAAO,EAAG,CAAC,CAEvD,CACA,SAASD,IAAiB,CACxB,OAAO,OAAO,KAAK,IAAI,CAAC,CAC1B,CACA,SAASG,IAAkB,CACzB,OAAO,OAAO,KAAK,MAAM,KAAK,IAAI,EAAI,GAAI,CAAC,CAC7C,CAOO,IAAMC,GAAc,CACzB,EAAGD,GACH,GAAIH,GACJ,GAAIE,GACJ,GAAIH,GACJ,QAASI,GACT,OAAQH,GACR,OAAQE,GACR,MAAOH,EACT,EAKaM,GAA0B,CACrC,EAAIC,GAAoB,GAAG,KAAK,MAAMA,EAAE,QAAQ,EAAI,GAAI,CAAC,GACzD,GAAKA,GAAoB,GAAGA,EAAE,QAAQ,CAAC,GACvC,GAAKA,GAAoB,GAAGA,EAAE,QAAQ,CAAC,MACvC,GAAKA,GAAoB,GAAGA,EAAE,QAAQ,CAAC,QACzC,EAOO,SAASC,GACdC,EACoB,CACpB,OAAIA,IAAU,OACLT,GAAM,EACJ,OAAOS,GAAU,SACnBA,EAAM,OAAS,EAAIA,EAAQ,OACzBA,aAAiB,KACnB,GAAGA,EAAM,QAAQ,CAAC,SAElB,OADE,OAAOA,GAAU,SACZ,KAAK,MAAMA,CAAK,EAEhBA,CAFiB,CAInC,CCzGO,IAAMC,GAAwB,CACnC,MAAMC,EAASC,EAAO,CAEpB,QAAQ,MAAM,UAAYD,EAASC,GAAgB,EAAE,CACvD,EACA,KAAKD,EAASC,EAAO,CAEnB,QAAQ,KAAK,SAAWD,EAASC,GAAgB,EAAE,CACrD,CACF,EACIC,EAAmBH,GAEVI,EAAc,CACzB,MAAMH,EAASC,EAAO,CACpBC,EAAS,MAAMF,EAASC,CAAK,CAC/B,EACA,KAAKD,EAASC,EAAO,CACnBC,EAAS,KAAKF,EAASC,CAAK,CAC9B,CACF,EAOO,SAASG,GAAUC,EAAwB,CAChD,IAAMC,EAAWJ,EACjB,OAAAA,EAAWG,EACJC,CACT,CCxCO,IAAMC,EAAa,OAAO,YAAY,EAmBvCC,EAAN,KAAqE,CACnE,YAAoBC,EAAmB,CAAnB,eAAAA,CAAoB,CACxC,UAAmB,CACjB,OAAO,KAAK,SACd,CACA,CAACF,CAAU,GAAY,CACrB,OAAO,KAAK,SACd,CACF,EAOA,SAASG,GAAoBC,EAAqB,CAChD,OAAO,OAAOA,GAAU,UAAY,OAAOA,EAAMJ,CAAU,GAAM,UACnE,CAQA,SAASK,EAAeD,EAAoB,CAC1C,GAAIA,GAAU,KAA6B,MAAO,GAClDA,EAAQA,EAAM,SAAS,EACvB,IAAIE,EACAC,EAAI,EACR,SAASC,GAAsB,CACzBF,IAAW,SACbA,EAASF,EAAM,UAAU,EAAGG,CAAC,EAEjC,CACA,KAAOA,EAAIH,EAAM,OAAQG,IAAK,CAC5B,IAAME,EAAIL,EAAM,OAAOG,CAAC,EACxB,OAAQE,EAAG,CACT,IAAK,KACHD,EAAc,EACdF,GAAU,MACV,MACF,IAAK;AAAA,EACHE,EAAc,EACdF,GAAU,MACV,MACF,IAAK,IACHE,EAAc,EACdF,GAAU,MACV,MACF,IAAK,IACL,IAAK,KACHE,EAAc,EACdF,EAASA,EAAS,KAAOG,EACzB,MACF,IAAK,IAEH,GAAIF,EAAI,EAAIH,EAAM,QAAUA,EAAM,OAAOG,EAAI,CAAC,IAAM,IAAK,CACvDC,EAAc,EACdD,IACAD,GAAU,OACV,KACF,CAEIA,GAAU,OACZA,GAAUG,GAEZ,MACF,QACMH,GAAU,OACZA,GAAUG,EAEhB,CACF,CACA,OAAIH,IAAW,OACNA,EAEFF,CACT,CAKO,SAASM,GAAWN,EAA+B,CACxD,OAAO,IAAIH,EAAc,IAAII,EAAeD,CAAK,CAAC,GAAG,CACvD,CAQO,SAASO,GAAcP,EAAoB,CAChD,IAAMQ,EAAM,OAAOR,CAAK,EACxB,GAAI,CAAC,SAASQ,CAAG,EAAG,CAClB,GAAI,OAAOR,GAAU,SACnB,MAAO,aAAaQ,CAAG,KAEzB,MAAM,IAAI,MAAM,qBAAqBR,CAAK,EAAE,CAC9C,CAGA,IAAMS,EAASD,EAAI,SAAS,EACxBE,EAAS,GACb,QAAWL,KAAKI,EACd,GAAK,EAAAJ,GAAK,KAAOA,GAAK,KAAQA,GAAK,KACnC,IAAIA,IAAM,IAAK,CACbK,EAAS,GACT,QACF,CACA,MAAO,aAAaD,CAAM,KAE5B,OAAOC,EAASD,EAASA,EAAS,IACpC,CAIO,SAASE,GAAUX,EAA+B,CACvD,OAAO,IAAIH,EAAcU,GAAcP,CAAK,CAAC,CAC/C,CAQO,SAASY,GAAgBZ,EAAoB,CAKlD,IAAMS,EAAS,OAAOT,CAAK,EACrBa,EAAWJ,EAAO,WAAW,GAAG,EAChCD,EAAMK,EAAWJ,EAAO,UAAU,CAAC,EAAIA,EAC7C,GAAID,EAAI,SAAW,GAAKA,EAAI,OAAS,GACnC,MAAM,IAAI,MAAM,uBAAuBC,CAAM,EAAE,EAEjD,QAAWJ,KAAKG,EACd,GAAIH,EAAI,KAAOA,EAAI,IAAK,MAAM,IAAI,MAAM,uBAAuBI,CAAM,EAAE,EAEzE,GAAID,EAAI,SAAW,GAAI,CACrB,GAAIK,GAAYL,EAAM,sBACpB,MAAM,IAAI,MAAM,+BAA+BC,CAAM,EAAE,EAEzD,GAAI,CAACI,GAAYL,EAAM,sBACrB,MAAM,IAAI,MAAM,+BAA+BC,CAAM,EAAE,CAE3D,CACA,OAAOA,CACT,CAKO,SAASK,GAAYd,EAA+B,CACzD,OAAO,IAAIH,EAAce,GAAgBZ,CAAK,CAAC,CACjD,CAEA,SAASe,GAAiBf,EAAoB,CAC5C,MAAO,YAAYC,EAAeD,CAAK,CAAC,IAC1C,CAKO,SAASgB,GAAahB,EAA+B,CAC1D,OAAO,IAAIH,EAAckB,GAAiBf,CAAK,CAAC,CAClD,CAKO,SAASiB,GAAajB,EAA+B,CAC1D,OAAO,IAAIH,EAAc,gBAAgBI,EAAeD,CAAK,CAAC,IAAI,CACpE,CAEA,SAASkB,GAAelB,EAAoB,CAC1C,OAAIA,aAAiB,OACZA,EAAM,SAAS,EAEjB,IAAI,OAAOA,CAAK,EAAE,SAAS,CACpC,CAOO,SAASmB,GAAWnB,EAA+B,CAExD,OAAO,IAAIH,EAAcqB,GAAelB,CAAK,CAAC,CAChD,CAKO,SAASoB,GAASpB,EAA+B,CACtD,OAAIA,IAAU,QAAUA,IAAU,QACzB,IAAIH,EAAcG,CAAK,EAEzB,IAAIH,GAAe,CAAC,CAACG,GAAO,SAAS,CAAC,CAC/C,CAQO,SAASqB,GAAerB,EAA+B,CAC5D,OAAO,IAAIH,EAAc,OAAOG,CAAK,CAAC,CACxC,CAOO,SAASsB,EAAYtB,EAAoB,CAC9C,GAAIA,IAAU,OACZ,MAAO,GACF,GAAIA,IAAU,KACnB,MAAO,OACF,GAAI,OAAOA,GAAU,UAC1B,OAAOA,EAAM,SAAS,EACjB,GAAI,OAAOA,GAAU,SAC1B,MAAO,IAAIC,EAAeD,CAAK,CAAC,IAC3B,GAAI,OAAOA,GAAU,SAC1B,OAAI,OAAO,cAAcA,CAAK,EACrBY,GAAgBZ,CAAK,EAEvBO,GAAcP,CAAK,EACrB,GAAI,OAAOA,GAAU,SAAU,CACpC,GAAI,OAAOA,EAAMJ,CAAU,GAAM,WAC/B,OAAOI,EAAMJ,CAAU,EAAE,EACpB,GAAII,aAAiB,KAC1B,OAAOA,EAAM,YAAY,EACpB,GAAIA,aAAiB,OAC1B,OAAOkB,GAAelB,CAAK,EACtB,GAAI,MAAM,QAAQA,CAAK,EAC5B,MAAO,IAAIA,EAAM,IAAIsB,CAAW,EAAE,KAAK,GAAG,CAAC,GAE/C,SAAW,OAAOtB,GAAU,SAC1B,MAAO,GAAGA,CAAK,KAGjB,OAAOsB,EAAYtB,EAAM,SAAS,CAAC,CACrC,CAMO,SAASuB,GACdC,KACGC,EACiB,CACpB,GAAID,EAAQ,QAAU,GAAKC,EAAO,SAAW,EAC3C,OAAOJ,GAAeG,EAAQ,CAAC,CAAC,EAElC,IAAME,EAAQ,IAAI,MAAcF,EAAQ,OAASC,EAAO,MAAM,EAC1DE,EAAY,EAChB,QAAS,EAAI,EAAG,EAAIH,EAAQ,OAAQ,IAAK,CACvC,IAAMI,EAAOJ,EAAQ,CAAC,EAEtB,GADAE,EAAMC,GAAW,EAAIC,EACjB,EAAIH,EAAO,OAAQ,CACrB,IAAMjB,EAAMiB,EAAO,CAAC,EAChBI,EACJ,GACED,EAAK,SAAS,GAAG,GACjB,EAAI,EAAIJ,EAAQ,QAChBA,EAAQ,EAAI,CAAC,EAAE,WAAW,GAAG,EAG7BK,EAAY5B,EAAeO,CAAG,UAE9BqB,EAAYP,EAAYd,CAAG,EACvBqB,IAAc,IAEZ,CAAC9B,GAAoBS,CAAG,EAC1B,MAAM,IAAI,MACR,kCAAkCA,CAAG,eAAe,CAAC,WAAW,OAAOA,CAAG,EAC5E,EAINkB,EAAMC,GAAW,EAAIE,CACvB,SAAW,EAAIL,EAAQ,OAAS,EAC9B,MAAM,IAAI,MAAM,8BAA8B,CAElD,CAEA,OAAOH,GAAeK,EAAM,KAAK,EAAE,CAAC,CACtC,CCxSO,IAAMI,GAAN,KAAY,CAYjB,YAAYC,EAA0B,CAVtC,KAAQ,KAAgC,CAAC,EAEzC,KAAO,OAAkC,CAAC,EASpCA,IAAiB,KAAK,KAAOA,EACnC,CAQO,YAAYC,EAAqB,CACtC,YAAK,KAAOA,EACL,IACT,CAUO,IAAIA,EAAcC,EAAsB,CAC7C,YAAK,KAAKD,CAAI,EAAIC,EACX,IACT,CASO,aAAaD,EAAcC,EAA6B,CAC7D,YAAK,OAAOD,CAAI,EAAIC,EAAQ,IAAM,IAC3B,IACT,CAUO,SAASD,EAAcC,EAA4B,CACxD,IAAIC,EAMJ,GALI,OAAOD,GAAU,SACnBC,EAAMD,EAENC,EAAM,SAAS,OAAOD,CAAK,CAAC,EAE1B,MAAMC,CAAG,GAAKA,GAAO,qBAAuBA,GAAO,mBACrD,MAAM,IAAI,MAAM,oCAAoCF,CAAI,OAAOC,CAAK,IAAI,EAE1E,YAAK,OAAOD,CAAI,EAAI,GAAG,KAAK,MAAME,CAAG,CAAC,IAC/B,IACT,CAUO,UAAUF,EAAcC,EAA4B,CACzD,GAAI,OAAOA,GAAU,SAAU,CAC7B,GAAI,MAAMA,CAAK,GAAKA,EAAQ,GAAKA,EAAQ,OAAO,iBAC9C,MAAM,IAAI,MAAM,yBAAyBD,CAAI,mBAAmBC,CAAK,EAAE,EAEzE,KAAK,OAAOD,CAAI,EAAI,GAAG,KAAK,MAAMC,CAAe,CAAC,GACpD,KAAO,CACL,IAAME,EAAS,OAAOF,CAAK,EAC3B,QAAS,EAAI,EAAG,EAAIE,EAAO,OAAQ,IAAK,CACtC,IAAMC,EAAOD,EAAO,WAAW,CAAC,EAChC,GAAIC,EAAO,IAAMA,EAAO,GACtB,MAAM,IAAI,MACR,kDAAkD,CAAC,KAAKH,CAAK,EAC/D,CAEJ,CACA,GACEE,EAAO,OAAS,IACfA,EAAO,SAAW,IACjBA,EAAO,cAAc,sBAAsB,EAAI,EAEjD,MAAM,IAAI,MACR,yBAAyBH,CAAI,mBAAmBG,CAAM,EACxD,EAEF,KAAK,OAAOH,CAAI,EAAI,GAAGG,CAAM,GAC/B,CACA,OAAO,IACT,CAUO,WAAWH,EAAcC,EAA4B,CAC1D,IAAIC,EAMJ,GALI,OAAOD,GAAU,SACnBC,EAAMD,EAENC,EAAM,WAAWD,CAAK,EAEpB,CAAC,SAASC,CAAG,EACf,MAAM,IAAI,MAAM,kCAAkCF,CAAI,MAAMC,CAAK,EAAE,EAGrE,YAAK,OAAOD,CAAI,EAAI,OAAOE,CAAG,EACvB,IACT,CASO,YAAYF,EAAcC,EAA4B,CAC3D,OAAIA,GAAU,OACR,OAAOA,GAAU,WAAUA,EAAQ,OAAOA,CAAK,GACnD,KAAK,OAAOD,CAAI,EAAIK,EAAO,OAAOJ,CAAK,GAElC,IACT,CAoBO,UAAUA,EAAkD,CACjE,YAAK,KAAOA,EACL,IACT,CAQO,eAAeK,EAAuD,CAC3E,GAAI,CAAC,KAAK,KAAM,OAChB,IAAIC,EAAa,GAUjB,GATA,OAAO,KAAK,KAAK,MAAM,EACpB,KAAK,EACL,QAASC,GAAM,CACd,GAAIA,EAAG,CACL,IAAMN,EAAM,KAAK,OAAOM,CAAC,EACrBD,EAAW,OAAS,IAAGA,GAAc,KACzCA,GAAc,GAAGF,EAAO,IAAIG,CAAC,CAAC,IAAIN,CAAG,EACvC,CACF,CAAC,EACCK,EAAW,SAAW,EAAG,OAC7B,IAAIE,EAAW,GACTC,EACJJ,GAAYA,EAAS,YACjB,CAAC,GAAGA,EAAS,YAAa,GAAG,KAAK,IAAI,EACtC,KAAK,KACX,OAAO,KAAKI,CAAI,EACb,KAAK,EACL,QAASF,GAAM,CACd,GAAIA,EAAG,CACL,IAAMN,EAAMQ,EAAKF,CAAC,EACdN,IACFO,GAAY,IACZA,GAAY,GAAGJ,EAAO,IAAIG,CAAC,CAAC,IAAIH,EAAO,IAAIH,CAAG,CAAC,GAEnD,CACF,CAAC,EACH,IAAIS,EAAO,KAAK,KAChB,OAAIL,GAAYA,EAAS,YACvBK,EAAOL,EAAS,YAAYK,CAAI,EAEhCA,EAAOC,GAAmBD,CAAI,EAGzB,GAAGN,EAAO,YAAY,KAAK,IAAI,CAAC,GAAGI,CAAQ,IAAIF,CAAU,GAC9DI,IAAS,OAAY,IAAMA,EAAO,EACpC,EACF,CAEA,UAAmB,CACjB,IAAME,EAAO,KAAK,eAAe,MAAS,EAC1C,OAAOA,GAAc,kBAAkB,KAAK,UAAU,KAAM,MAAS,CAAC,EACxE,CACF,ECzOO,IAAMC,GAAN,KAAsD,CAI3D,YAAYC,EAA8C,CACxD,KAAK,QAAU,CAAC,GAAGC,EAAmC,GAAGD,CAAO,EAChE,KAAK,QAAQ,CACf,CAEA,UAAUE,EAAeC,EAAiC,CACxD,IAAMC,EAAQC,EAAcH,CAAK,EACjC,GAAIE,GAASA,EAAQ,EACnB,OAAOA,EAAQ,KAAK,MAAM,KAAK,OAAO,EAAI,KAAK,QAAQ,WAAW,EAElE,GAAID,GAAkBA,EAAiB,EAAG,CAExC,GAAI,KAAK,QAAQ,YAAa,CAE5B,IAAIC,EAAQ,KAAK,IAAI,KAAK,QAAQ,cAAe,CAAC,EAC9CE,EAAYF,EAAQ,KAAK,QAAQ,gBACrC,QAASG,EAAI,EAAGA,EAAIJ,EAAgBI,IAGlC,GAFAH,EAAQE,EACRA,EAAYA,EAAY,KAAK,QAAQ,gBACjCA,GAAa,KAAK,QAAQ,cAAe,CAC3CA,EAAY,KAAK,QAAQ,cACzB,KACF,CAEF,OACEF,EACA,KAAK,MACH,KAAK,OAAO,GAAKE,EAAYF,GAC3B,KAAK,OAAO,EAAI,KAAK,QAAQ,WACjC,CAEJ,CAEA,IAAIA,EAAQ,KAAK,IAAI,KAAK,QAAQ,cAAe,CAAC,EAClD,QAASG,EAAI,EAAGA,EAAIJ,EAAgBI,IAElC,GADAH,EAAQA,EAAQ,KAAK,QAAQ,gBACzBA,GAAS,KAAK,QAAQ,cAAe,CACvCA,EAAQ,KAAK,QAAQ,cACrB,KACF,CAEF,OAAOA,EAAQ,KAAK,MAAM,KAAK,OAAO,EAAI,KAAK,QAAQ,WAAW,CACpE,MAAW,KAAK,aACd,KAAK,aAAe,KAAK,IACvB,KAAK,IAAI,KAAK,aAAe,KAAK,QAAQ,gBAAiB,CAAC,EAC1D,KAAK,MAAM,KAAK,OAAO,EAAI,KAAK,QAAQ,WAAW,EACrD,KAAK,QAAQ,aACf,EAEA,KAAK,aACH,KAAK,QAAQ,cACb,KAAK,MAAM,KAAK,OAAO,EAAI,KAAK,QAAQ,WAAW,EAEvD,OAAO,KAAK,YAEhB,CACA,SAAgB,CACd,KAAK,aAAe,MACtB,CACF,EAOO,SAASI,GACdR,EACoB,CACpB,OAAO,IAAID,GAAkBC,CAAO,CACtC,CCxEA,SAASS,GAAoBC,EAA6C,CACxE,IAAIC,EACAC,EAAQF,EACRG,EAAgBH,EACpB,KAAOG,EAAc,MACfA,EAAc,KAAK,QAAUD,EAAM,UACrCD,EAASE,EACTD,EAAQC,EAAc,MAExBA,EAAgBA,EAAc,KAEhC,MAAO,CAACD,EAAOD,CAAM,CACvB,CAKA,IAAqBG,EAArB,KAAiC,CAM/B,YACUC,EACAC,EAKAC,EAIK,IAAG,GAChB,CAXQ,cAAAF,EACA,gBAAAC,EAKA,cAAAC,EAXV,UAAO,EACP,YAAS,GACT,KAAQ,eAAsB,MAc3B,CAEH,SACEC,EACAC,EACAC,EACAC,EACM,CAEN,GADI,KAAK,QACL,CAACH,EAAM,OAAQ,OACnB,IAAII,EAAY,KAAK,IAAI,EAAIF,EAK7B,GAJIC,EAAUC,IACZA,EAAYD,GAGV,KAAK,OAAS,KAAK,KAAOH,EAAM,OAAS,KAAK,SAAU,CAC1D,IAAMK,EAAW,KAAK,KAChBC,EAAUD,EAAW,GAC3B,EAAG,CAED,GAAM,CAACX,EAAOD,CAAM,EAAIF,GAAoB,KAAK,KAAK,EACtD,KAAK,MAAQG,EAAM,MAAM,OACrBD,EACFA,EAAO,KAAOC,EAAM,MAEpB,KAAK,MAAQA,EAAM,KACf,KAAK,OACP,KAAK,cAAc,KAAK,MAAM,UAAY,KAAK,IAAI,CAAC,GAGxDA,EAAM,KAAO,OACb,KAAK,SAASA,CAAK,CACrB,OAAS,KAAK,OAAS,KAAK,KAAOM,EAAM,OAASM,GAClDC,EAAI,MACF,gBACEF,EAAW,KAAK,IAClB,gEACE,KAAK,QACP,SACF,CACF,CACA,IAAMG,EAAmB,CACvB,MAAAR,EACA,WAAAC,EACA,UAAAG,EACA,QAAAD,CACF,EAEIM,EAAiC,KAAK,MACtChB,EACJ,OAAS,CACP,GAAI,CAACgB,GAAWA,EAAQ,UAAYL,EAAW,CAC7CI,EAAM,KAAOC,EACThB,EACFA,EAAO,KAAOe,GAEd,KAAK,MAAQA,EACb,KAAK,cAAcJ,EAAY,KAAK,IAAI,CAAC,GAE3C,KACF,CACAX,EAASgB,EACTA,EAAUA,EAAQ,IACpB,CACA,KAAK,MAAQT,EAAM,MACrB,CAEA,aAAqC,CACnC,GAAI,KAAK,MAAO,CACd,IAAMU,EAAU,KAAK,MACrB,YAAK,MAAQ,KAAK,MAAM,KACxBA,EAAQ,KAAO,OACf,KAAK,MAAQA,EAAQ,MAAM,OACpBA,CACT,CAEF,CAEA,cAAcR,EAAqB,CAC7B,KAAK,gBACP,aAAa,KAAK,cAAc,EAElC,KAAK,eAAiB,WACpB,IAAM,CACJ,IAAMQ,EAAU,KAAK,YAAY,EAC7BA,EACF,KAAK,WAAWA,EAAQ,MAAOA,EAAQ,WAAYA,EAAQ,OAAO,EAC/D,MAAM,IAAM,CAEb,CAAC,EACA,QAAQ,IAAM,CAET,KAAK,OACP,KAAK,cAAc,KAAK,MAAM,UAAY,KAAK,IAAI,CAAC,CAExD,CAAC,EAEH,KAAK,eAAiB,MAE1B,EACA,KAAK,IAAIR,EAAO,CAAC,CACnB,CACF,CAEA,MAAM,OAAuB,CAC3B,IAAIQ,EACJ,KAAQA,EAAU,KAAK,YAAY,GACjC,MAAM,KAAK,WAAWA,EAAQ,MAAOA,EAAQ,WAAYA,EAAQ,OAAO,CAE5E,CAEA,OAAgB,CACd,OAAI,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,QAExB,KAAK,OAAS,GACP,KAAK,IACd,CACF,ECjKe,SAARC,GAA4BC,EAAmB,CACpD,IAAIC,EAASD,EAAE,OAEf,QAASE,EAAI,EAAGA,EAAIF,EAAE,OAAQE,IAAK,CACjC,IAAMC,EAAOH,EAAE,WAAWE,CAAC,EAEvBC,EAAO,MAEAA,GAAQ,KAAQA,GAAQ,KACjCF,IACSE,GAAQ,MAASA,GAAQ,MAC9BA,GAAQ,OAAUA,GAAQ,MAG5BF,IAEAA,GAAU,EAIZA,GAAU,EAEd,CACA,OAAOA,CACT,CCbA,IAAMG,GAAN,KAAkB,CAKhB,YACUC,EACAC,EACAC,EACAC,EACR,CAJQ,qBAAAH,EACA,mBAAAC,EACA,aAAAC,EACA,kBAAAC,EARV,YAAS,EACT,WAAQ,GASN,KAAK,MAAQ,IAAI,MAAcH,CAAe,CAChD,CAEA,IAAII,EAAsB,CACxB,IAAMC,EAAOC,GAAWF,CAAM,EAC1B,KAAK,SAAW,EAClB,KAAK,aAAa,EACT,KAAK,MAAQC,EAAO,GAAK,KAAK,eAEvC,KAAK,MAAM,EAAE,MAAOE,GAAO,CAE3B,CAAC,EAEH,KAAK,MAAM,KAAK,MAAM,EAAIH,EAC1B,KAAK,SACL,KAAK,OAASC,EAAO,GAEnB,KAAK,QAAU,KAAK,iBACpB,KAAK,OAAS,KAAK,gBAEnB,KAAK,MAAM,EAAE,MAAOE,GAAO,CAE3B,CAAC,CAEL,CACA,OAAuB,CACrB,IAAMC,EAAQ,KAAK,MAAM,EACzB,OAAIA,EAAM,OAAS,EACV,KAAK,QAAQA,CAAK,EAElB,QAAQ,QAAQ,CAE3B,CACA,OAAkB,CAChB,IAAMC,EAAS,KAAK,MAAM,MAAM,EAAG,KAAK,MAAM,EAC9C,YAAK,OAAS,EACd,KAAK,MAAQ,GACNA,CACT,CACF,EAEqBC,EAArB,KAAsD,CAcpD,YACUC,EACRC,EACAC,EACAC,EACAC,EACA,CALQ,eAAAJ,EAXV,KAAQ,OAAS,GAGjB,KAAQ,eAAsB,OAc5B,KAAK,KAAO,qBAAqB,mBAC/BC,CACF,CAAC,WAAW,mBAAmBC,CAAM,CAAC,cAAcC,CAAS,GACzDC,GAAA,MAAAA,EAAc,cAChB,KAAK,MAAQ,gBAAgB,mBAC3BA,EAAa,WACf,CAAC,IAEH,KAAK,aAAe,CAClB,GAAGC,EACH,GAAGD,CACL,EACA,KAAK,YAAcE,GAAYH,CAAS,EACxC,KAAK,wBAA0BI,GAAwBJ,CAAS,EAC5D,KAAK,aAAa,aACpB,KAAK,eAAe,KAAK,aAAa,WAAW,EAEnD,KAAK,YAAc,CACjB,OAAQ,OACR,QAAS,CACP,eAAgB,4BAChB,GAAGC,GAAA,YAAAA,EAAc,OACnB,EACA,cAAe,KAAK,aAAa,aACnC,EAEA,IAAMI,EAAmB,IAAY,CAC/B,KAAK,aAAa,cAAgB,IACpC,KAAK,mBAAmB,EAEnB,KAAK,SACR,KAAK,eAAiB,WACpB,IACE,KAAK,UACH,KAAK,YAAY,MAAM,EACvB,KAAK,aAAa,UACpB,EAAE,MAAOZ,GAAO,CAEhB,CAAC,EACH,KAAK,aAAa,aACpB,GAGN,EAEA,KAAK,YAAc,IAAIR,GACrB,KAAK,aAAa,UAClB,KAAK,aAAa,cACjBS,IACC,KAAK,mBAAmB,EACjB,KAAK,UAAUA,EAAO,KAAK,aAAa,UAAU,GAE3DW,CACF,EACA,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EAEzC,KAAK,cAAgBC,GAAyB,KAAK,YAAY,EAC/D,KAAK,YAAc,IAAIC,EACrB,KAAK,aAAa,eAClB,KAAK,UACL,KAAK,aAAa,iBACpB,CACF,CAEA,UACEb,EACAc,EACAC,EAAkB,KAAK,IAAI,EAAI,KAAK,aAAa,aAClC,CAEf,IAAMC,EAAqB,KACrBC,EAAiBD,EAAK,aAAa,WAAa,EAAIF,EAC1D,GAAI,CAAC,KAAK,QAAUd,EAAM,OAAS,EAAG,CACpC,GAAIe,GAAW,KAAK,IAAI,EAAG,CACzB,IAAMG,EAAQ,IAAI,MAAM,0BAA0B,EAC5CC,EAAUH,EAAK,aAAa,YAAY,KAC5CA,EACAE,EACAlB,EACAiB,EACAF,CACF,EACA,OAAII,IAGJC,EAAI,MACF,sCAAsCH,CAAc,KACpDC,CACF,EACO,QAAQ,OAAOA,CAAK,EAC7B,CACA,OAAO,IAAI,QAAc,CAACG,EAASC,IAAW,CAC5C,IAAIC,EACAC,EACEC,EAAY,CAChB,gBAAgBC,EAAmBC,EAA2B,CAC5DJ,EAAqBI,EACrBH,EAAUE,CACZ,EACA,MAAMR,EAAoB,CAExB,IAAMC,EAAUH,EAAK,aAAa,YAAY,KAC5CA,EACAE,EACAlB,EACAiB,EACAF,CACF,EACA,GAAII,EAAS,CACXA,EAAQ,KAAKE,EAASC,CAAM,EAC5B,MACF,CAGA,GACEJ,aAAiBU,GACjBV,EAAM,MACN,OAAOA,EAAM,KAAK,OAAU,UAC5BA,EAAM,KAAK,MAAM,SAAS,gCAAgC,EAC1D,CACAE,EAAI,KAAK,8BAAgCF,EAAM,KAAK,KAAK,EACzDK,EAAqB,IACrBE,EAAU,SAAS,EACnB,MACF,CAEA,GACE,CAACT,EAAK,QACNF,EAAgB,IACf,EAAEI,aAAiBU,IACjBV,EAAoB,YAAc,KACrC,CACAE,EAAI,KACF,sCAAsCH,CAAc,KACpDC,CACF,EACAF,EAAK,YAAY,SACfhB,EACAc,EAAgB,EAChBE,EAAK,cAAc,UAAUE,EAAOD,CAAc,EAClDF,CACF,EACAO,EAAOJ,CAAK,EACZ,MACF,CACAE,EAAI,MAAM,4BAA6BF,CAAK,EAC5CI,EAAOJ,CAAK,CACd,EACA,UAAiB,CAGf,GACEK,GAAsB,KACtBA,GAAsB,KACtBA,GAAsB,KAEtBP,EAAK,aAAa,aAAa,KAAKA,EAAMhB,CAAK,EAC/CgB,EAAK,cAAc,QAAQ,EAC3BK,EAAQ,MACH,CACL,IAAMQ,EAAU,+CAA+CN,CAAkB,YAC3EL,EAAQ,IAAIU,EAChBL,EACAM,EACA,OACA,IACA,OACA,OACAL,CACF,EACAN,EAAM,QAAUW,EAChBJ,EAAU,MAAMP,CAAK,CACvB,CACF,CACF,EACA,KAAK,UAAU,KACb,KAAK,KACLlB,EAAM,KAAK;AAAA,CAAI,EACf,KAAK,YACLyB,CACF,CACF,CAAC,CACH,KACE,QAAO,QAAQ,QAAQ,CAE3B,CAEQ,oBAA2B,CAC7B,KAAK,iBAAmB,SAC1B,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,OAE1B,CAEA,YAAY7B,EAAsB,CAChC,GAAI,KAAK,OACP,MAAM,IAAI,MAAM,2BAA2B,EAE7C,KAAK,YAAY,IAAIA,CAAM,CAC7B,CACA,aAAakC,EAAkC,CAC7C,GAAI,KAAK,OACP,MAAM,IAAI,MAAM,2BAA2B,EAE7C,QAASC,EAAI,EAAGA,EAAID,EAAQ,OAAQC,IAClC,KAAK,YAAY,IAAID,EAAQC,CAAC,CAAC,CAEnC,CACA,WAAWC,EAAoB,CAC7B,GAAI,KAAK,OACP,MAAM,IAAI,MAAM,2BAA2B,EAE7C,IAAMC,EAAOD,EAAM,eAAe,IAAI,EAClCC,GAAM,KAAK,YAAY,IAAIA,CAAI,CACrC,CACA,YAAYC,EAAgC,CAC1C,GAAI,KAAK,OACP,MAAM,IAAI,MAAM,2BAA2B,EAE7C,QAASH,EAAI,EAAGA,EAAIG,EAAO,OAAQH,IAAK,CACtC,IAAME,EAAOC,EAAOH,CAAC,EAAE,eAAe,IAAI,EACtCE,GAAM,KAAK,YAAY,IAAIA,CAAI,CACrC,CACF,CACA,MAAM,MAAME,EAA0C,CAEpD,GADA,MAAM,KAAK,YAAY,MAAM,EACzBA,EACF,OAAO,MAAM,KAAK,YAAY,MAAM,CAExC,CACA,OAAuB,CAWrB,OAVe,KAAK,YAAY,MAAM,EAAE,QAAQ,IAAM,CACpD,IAAMC,EAAY,KAAK,YAAY,MAAM,EACrCA,GACFhB,EAAI,MACF,4BAA4BgB,CAAS,4CACrC,IACF,EAEF,KAAK,OAAS,EAChB,CAAC,CAEH,CACA,SAAkB,CAChB,YAAK,mBAAmB,EACxB,KAAK,OAAS,GACP,KAAK,YAAY,MAAM,EAAI,KAAK,YAAY,MACrD,CAIA,eAAeC,EAAyC,CACtD,YAAK,YAAcA,EACZ,IACT,CACA,YAAYC,EAA+D,CACzE,OAAIA,IAAU,OACL,KAAK,YAAY,EACf,OAAOA,GAAU,SACnBA,EAAM,OAAS,EAAIA,EAAQ,OACzBA,aAAiB,KACnB,KAAK,wBAAwBA,CAAK,EAElC,OADE,OAAOA,GAAU,SACZ,KAAK,MAAMA,CAAK,EAEhBA,CAFiB,CAInC,CACF,EC7Ve,SAARC,GACLC,EAAiD,CAAC,EAChC,CAClB,IAAIC,EAAQ,EACNC,EAA2B,CAC/B,KAAOC,GAA8B,CACnC,GACEF,IAAU,GACVD,EAAU,MACVG,IAAS,MACTA,IAAS,OAET,OAAOH,EAAU,KAAKG,CAAI,CAE9B,EACA,MAAQC,GAAuB,CAEzBH,IAAU,IACZA,EAAQ,EAEJD,EAAU,OAAOA,EAAU,MAAMI,CAAK,EAE9C,EACA,SAAU,IAAY,CAChBH,IAAU,IACZA,EAAQ,EAEJD,EAAU,UAAUA,EAAU,SAAS,EAE/C,EACA,gBAAiB,CAACK,EAAkBC,IAA8B,CAC5DN,EAAU,iBACZA,EAAU,gBAAgBK,EAASC,CAAU,CACjD,CACF,EACA,OAAIN,EAAU,iBACZE,EAAO,eAAiBF,EAAU,eAAe,KAAKA,CAAS,GAE7DA,EAAU,YACZE,EAAO,UAAYF,EAAU,UAAU,KAAKA,CAAS,GAEhDE,CACT,CCrCA,SAASK,EAAmBC,EAA6B,CACvD,IAAMC,EAAmB,CAAC,EAC1B,OAAAD,EAAS,QAAQ,QAAQ,CAACE,EAAeC,IAAgB,CACvD,IAAMC,EAAWH,EAAQE,CAAG,EACxBC,IAAa,OACfH,EAAQE,CAAG,EAAID,EACN,MAAM,QAAQE,CAAQ,EAC/BA,EAAS,KAAKF,CAAK,EAEnBD,EAAQE,CAAG,EAAI,CAACC,EAAUF,CAAK,CAEnC,CAAC,EACMD,CACT,CAKA,IAAqBI,EAArB,KAAyD,CAIvD,YAAoBC,EAAsC,CAAtC,uBAAAA,EAHpB,mBAA+BC,EAA0B,EA0PzD,KAAO,iBAIK,UAAY,CAAC,EA1PvB,KAAK,eAAiB,CACpB,eAAgB,kCAEhB,GAAGD,EAAkB,OACvB,EACI,KAAK,kBAAkB,QACzB,KAAK,eAAe,cAClB,SAAW,KAAK,kBAAkB,OAEtC,KAAK,IAAM,OAAO,KAAK,kBAAkB,GAAG,EACxC,KAAK,IAAI,SAAS,GAAG,IACvB,KAAK,IAAM,KAAK,IAAI,UAAU,EAAG,KAAK,IAAI,OAAS,CAAC,GAIlD,KAAK,IAAI,SAAS,SAAS,IAC7B,KAAK,IAAM,KAAK,IAAI,UAAU,EAAG,KAAK,IAAI,OAAS,CAAgB,EACnEE,EAAI,KACF,sEAAsE,KAAK,GAAG,IAChF,EAEJ,CACA,KACEC,EACAC,EACAC,EACAC,EACM,CACN,IAAMC,EAAWC,GAA8BF,CAAS,EACpDG,EAAY,GACZC,EAAUL,EAAgB,OAC1BM,EACEC,EAAgB,IAAM,CAAC,EACzBC,EAASD,EACb,GAAIN,GAAaA,EAAU,eAAgB,CACzC,IAAMQ,EAAa,IAAI,gBAClBJ,IACHA,EAASI,EAAW,OACpBT,EAAU,CAAC,GAAGA,EAAS,OAAAK,CAAM,GAG/BA,EAAO,iBAAiB,QAAS,IAAM,CACrCG,EAAO,CACT,CAAC,EACDP,EAAU,eAAe,CACvB,QAAS,CACPG,EAAY,GACZK,EAAW,MAAM,CACnB,EACA,aAAc,CACZ,OAAOL,GAAaC,EAAO,OAC7B,CACF,CAAC,CACH,CACA,KAAK,MAAMP,EAAMC,EAAMC,CAAO,EAC3B,KAAK,MAAOX,GAAa,CAQxB,GAPIY,GAAA,MAAAA,EAAW,iBACbC,EAAS,gBACPd,EAAmBC,CAAQ,EAC3BA,EAAS,MACX,EAEF,MAAM,KAAK,qBAAqBA,CAAQ,EACpCA,EAAS,KAAM,CACjB,IAAMqB,EAASrB,EAAS,KAAK,UAAU,EACnCsB,EACJ,EAAG,CAID,GAHIL,GACF,MAAMA,EAEJF,EACF,MAGF,GADAO,EAAQ,MAAMD,EAAO,KAAK,EACtBR,EAAS,KAAKS,EAAM,KAAK,IAAM,GAAO,CACxC,IAAMC,EAAYV,EAAS,UAC3B,GAAI,CAACU,EAAW,CACd,IAAMC,EAAM,gDACZ,aAAMH,EAAO,OAAOG,CAAG,EAChB,QAAQ,OAAO,IAAI,MAAMA,CAAG,CAAC,CACtC,CACAP,EAAe,IAAI,QAASQ,GAAY,CACtCN,EAAS,IAAM,CACbM,EAAQ,EACRR,EAAe,OACfE,EAASD,CACX,EACAK,EAAUJ,CAAM,CAClB,CAAC,CACH,CACF,OAAS,CAACG,EAAM,KAClB,SAAWtB,EAAS,YAAa,CAC/B,IAAM0B,EAAS,MAAM1B,EAAS,YAAY,EAC1Ca,EAAS,KAAK,IAAI,WAAWa,CAAM,CAAC,CACtC,KAAO,CACL,IAAMC,EAAO,MAAM3B,EAAS,KAAK,EACjCa,EAAS,KAAK,IAAI,YAAY,EAAE,OAAOc,CAAI,CAAC,CAC9C,CACF,CAAC,EACA,MAAOC,GAAM,CACPb,GACHF,EAAS,MAAMe,CAAC,CAEpB,CAAC,EACA,QAAQ,IAAMf,EAAS,SAAS,CAAC,CACtC,CACA,MAAc,qBAAqBb,EAAmC,CACpE,GAAIA,EAAS,QAAU,IAAK,CAC1B,IAAI2B,EAAO,GACX,GAAI,CAEF,GADAA,EAAO,MAAM3B,EAAS,KAAK,EACvB,CAAC2B,EAAM,CACT,IAAME,EAAc7B,EAAS,QAAQ,IAAI,kBAAkB,EACvD6B,IACFF,EAAOE,EAEX,CACF,OAASD,EAAG,CACV,MAAApB,EAAI,KAAK,+BAAgCoB,CAAC,EAEpC,IAAIE,EACR9B,EAAS,OACTA,EAAS,WACT,OACAA,EAAS,QAAQ,IAAI,aAAa,EAClCA,EAAS,QAAQ,IAAI,cAAc,EACnC,OACAD,EAAmBC,CAAQ,CAC7B,CACF,CACA,MAAM,IAAI8B,EACR9B,EAAS,OACTA,EAAS,WACT2B,EACA3B,EAAS,QAAQ,IAAI,aAAa,EAClCA,EAAS,QAAQ,IAAI,cAAc,EACnC,OACAD,EAAmBC,CAAQ,CAC7B,CACF,CACF,CAEA,MAAO,QACLS,EACAC,EACAC,EACmC,CAtLvC,IAAAoB,EAuLI,IAAM/B,EAAW,MAAM,KAAK,MAAMS,EAAMC,EAAMC,CAAO,EAErD,GADA,MAAM,KAAK,qBAAqBX,CAAQ,EACpCA,EAAS,KAAM,CACjB,IAAMqB,EAASrB,EAAS,KAAK,UAAU,EACvC,OAAS,CACP,GAAM,CAAC,MAAAE,EAAO,KAAA8B,CAAI,EAAI,MAAMX,EAAO,KAAK,EACxC,GAAIW,EACF,MAEF,IAAID,EAAApB,EAAQ,SAAR,MAAAoB,EAAgB,QAClB,YAAM/B,EAAS,KAAK,OAAO,EACrB,IAAIiC,EAEZ,MAAM/B,CACR,CACF,SAAWF,EAAS,YAAa,CAC/B,IAAM0B,EAAS,MAAM1B,EAAS,YAAY,EAC1C,MAAM,IAAI,WAAW0B,CAAM,CAC7B,KAAO,CACL,IAAMC,EAAO,MAAM3B,EAAS,KAAK,EACjC,MAAM,IAAI,YAAY,EAAE,OAAO2B,CAAI,CACrC,CACF,CAEA,MAAM,QACJlB,EACAC,EACAC,EACAuB,EACc,CApNlB,IAAAH,EAAAI,EAqNI,IAAMnC,EAAW,MAAM,KAAK,MAAMS,EAAMC,EAAMC,CAAO,EAC/C,CAAC,QAAAV,CAAO,EAAID,EACZoC,EAAsBnC,EAAQ,IAAI,cAAc,GAAK,GACvDiC,GACFA,EAAgBnC,EAAmBC,CAAQ,EAAGA,EAAS,MAAM,EAG/D,MAAM,KAAK,qBAAqBA,CAAQ,EACxC,IAAMqC,GAAeF,GAAAJ,EAAApB,EAAQ,UAAR,YAAAoB,EAAiB,SAAjB,KAAAI,EAA2BC,EAChD,GAAIC,EAAa,SAAS,MAAM,EAC9B,OAAO,MAAMrC,EAAS,KAAK,EACtB,GACLqC,EAAa,SAAS,MAAM,GAC5BA,EAAa,WAAW,iBAAiB,EAEzC,OAAO,MAAMrC,EAAS,KAAK,CAE/B,CAEQ,MACNS,EACAC,EACAC,EACmB,CACnB,GAAM,CAAC,OAAA2B,EAAQ,QAAArC,EAAS,GAAGsC,CAAK,EAAI5B,EAC9B6B,EAAM,GAAG,KAAK,GAAG,GAAG/B,CAAI,GACxBgC,EAAuB,CAC3B,OAAQH,EACR,KACEA,IAAW,OAASA,IAAW,OAC3B,OACA,OAAO5B,GAAS,SACdA,EACA,KAAK,UAAUA,CAAI,EAC3B,QAAS,CACP,GAAG,KAAK,eACR,GAAGT,CACL,EACA,YAAa,OAEb,GAAG,KAAK,kBAAkB,iBAE1B,GAAGsC,CACL,EACA,YAAK,iBAAiBE,EAAS9B,EAAS6B,CAAG,EACpC,MAAMA,EAAKC,CAAO,CAC3B,CA4BF,ECjRA,IAAMC,GAAuB,CAC3B,OAAQ,GACR,UAAW,IACX,UAAW,IACX,cAAe,IACf,YAAa,CAAC,WAAY,QAAS,SAAS,CAC9C,EAEaC,GAAN,MAAMC,CAAiC,CAE5C,YACUC,EACAC,EAIRC,EACA,CANQ,eAAAF,EACA,uBAAAC,EAMR,KAAK,QAAU,OAAOC,GAAQ,SAAW,CAAC,IAAAA,CAAG,EAAIA,CACnD,CAEA,KAAKC,EAA0C,CAC7C,OAAO,IAAIJ,EAAa,KAAK,UAAW,KAAK,kBAAmB,CAC9D,GAAG,KAAK,QACR,GAAGI,CACL,CAAC,CACH,CAEA,SAASC,EAA0D,CACjE,GAAM,CAAC,IAAAF,EAAK,KAAAG,EAAM,KAAAC,EAAM,QAAAC,CAAO,EAAI,KAAK,QAClCC,EAAO,qBAAqB,mBAAmBN,CAAG,CAAC,GACnDO,EAAO,KAAK,UAChB,KAAK,gBAAgB,CACnB,MAAOL,EAAM,SAAS,EACtB,QAASP,GACT,KAAAQ,CACF,CAAC,CACH,EACMF,EAAU,CACd,OAAQ,OACR,QAAS,CACP,eAAgB,mCAChB,kBAAmBG,EAAO,OAAS,WACnC,GAAGC,CACL,CACF,EACA,OAAO,KAAK,kBACTG,GAAa,KAAK,UAAU,KAAKF,EAAMC,EAAMN,EAASO,CAAQ,EAC/D,IAAM,KAAK,UAAU,QAAQF,EAAMC,EAAMN,CAAO,CAClD,CACF,CAEA,aAAaC,EAA2D,CACtE,OAAO,KAAK,SAASA,CAAK,EAAE,aAAa,CAC3C,CACA,YAAYA,EAAwD,CAClE,OAAO,KAAK,SAASA,CAAK,EAAE,YAAY,CAC1C,CACA,MAAMA,EAAwD,CAC5D,OAAO,KAAK,SAASA,CAAK,EAAE,MAAM,CACpC,CAEA,KAAKA,EAAqD,CACxD,OAAO,KAAK,SAASA,CAAK,EAAE,KAAK,CACnC,CAEA,WACEA,EACAM,EACM,CACN,OAAO,KAAK,SAASN,CAAK,EAAE,aAAaM,CAAQ,CACnD,CAEA,UACEN,EACAM,EACM,CACN,OAAO,KAAK,SAASN,CAAK,EAAE,YAAYM,CAAQ,CAClD,CAEA,YACEN,EACAO,EAImB,CACnB,OAAO,KAAK,SAASP,CAAK,EAAE,YAAYO,CAAS,CACnD,CAEA,aAAaP,EAA4D,CACvE,OAAO,KAAK,SAASA,CAAK,EAAE,aAAa,CAC3C,CAEA,SAASA,EAAqD,CAC5D,GAAM,CAAC,IAAAF,EAAK,KAAAG,EAAM,KAAAC,EAAM,QAAAC,CAAO,EAAI,KAAK,QACxC,OAAO,KAAK,UAAU,QACpB,qBAAqB,mBAAmBL,CAAG,CAAC,GAC5C,KAAK,UACH,KAAK,gBAAgB,CACnB,MAAOE,EAAM,SAAS,EACtB,QAASP,GACT,KAAAQ,CACF,CAAC,CACH,EACA,CACE,OAAQ,OACR,QAAS,CACP,OAAQ,WACR,kBAAmBC,EAAO,OAAS,WACnC,eAAgB,mCAChB,GAAGC,CACL,CACF,CACF,CACF,CAEQ,gBAAgBK,EAAmB,CAnI7C,IAAAC,EAoII,OAAI,OAAO,KAAK,QAAQ,KAAQ,aAC9BD,EAAQ,IAAM,KAAK,QAAQ,IAAI,GAGjCA,EAAQ,MAAOC,EAAA,KAAK,QAAQ,OAAb,KAAAA,EAAqB,OAC7BD,CACT,CACF,EAEOE,GAAQhB,GC3HR,SAASiB,GACdC,EACAC,EACqB,CACrB,OAAOA,EAAU,SAASD,CAAM,CAClC,CAMO,IAAME,EAAN,KAA+D,CACpE,YACUC,EACAC,EACAC,EACR,CAHQ,cAAAF,EACA,4BAAAC,EACA,mBAAAC,CACP,CACH,cAAsC,CACpC,OAAOC,EAAsB,KAAK,uBAAuB,CAAC,CAC5D,CACA,aAAkC,CAChC,OAAOC,EACLD,EAAsB,KAAK,uBAAuB,CAAC,CACrD,CACF,CACA,OAA4B,CAC1B,OAAO,IAAIE,EAAgB,KAAK,SAAWC,GACzCC,EAAcD,EAAU,KAAK,aAAa,CAC5C,CACF,CAEA,MAAwB,CACtB,OAAO,IAAID,EAAgB,KAAK,SAAWC,GAClCC,EACLC,EAAc,CACZ,KAAKX,EAAQC,EAAW,CACtBQ,EAAS,KAAK,CAAC,OAAAT,EAAQ,UAAAC,CAAS,CAAC,CACnC,EACA,MAAMW,EAAG,CACPH,EAAS,MAAMG,CAAC,CAClB,EACA,UAAW,CACTH,EAAS,SAAS,CACpB,CACF,CAAC,EACD,KAAK,aACP,CACD,CACH,CAEA,aAAaI,EAA+C,CAC1D,KAAK,SAASH,EAAcG,EAAU,KAAK,aAAa,CAAC,CAC3D,CAEA,YAAYA,EAA8C,CACxD,KAAK,SAASH,EAAcC,EAAcE,CAAQ,EAAG,KAAK,aAAa,CAAC,CAC1E,CAEA,YACEC,EAGqBf,GAIF,CACnB,IAAMgB,EAAmB,CAAC,EAC1B,OAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CACtC,KAAK,YAAY,CACf,KAAKjB,EAAkBC,EAAoC,CACzD,IAAMiB,EAAQJ,EAAU,KAAK,KAAMd,EAAQC,CAAS,EAChDiB,IAAU,QACZH,EAAO,KAAKG,CAAK,CAErB,EACA,MAAMC,EAAoB,CACxBF,EAAOE,CAAK,CACd,EACA,UAAiB,CACfH,EAAQD,CAAM,CAChB,CACF,CAAC,CACH,CAAC,CACH,CAEA,cAAuC,CACrC,IAAMA,EAAwB,CAAC,EAC/B,OAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CACtC,KAAK,aAAa,CAChB,KAAKG,EAAoB,CACvBL,EAAO,KAAKK,CAAI,CAClB,EACA,MAAMD,EAAoB,CACxBF,EAAOE,CAAK,CACd,EACA,UAAiB,CACfH,EAAQD,CAAM,CAChB,CACF,CAAC,CACH,CAAC,CACH,CACF,ECpGA,IAAqBM,EAArB,KAA8B,CAY5B,YAAYC,EAAiC,CAhC/C,IAAAC,EAiCI,GAAI,OAAOD,GAAY,SACrB,KAAK,SAAW,CAAC,IAAKA,CAAO,UACpBA,IAAY,MAAQ,OAAOA,GAAY,SAChD,KAAK,SAAW,OAAO,OAAO,CAAC,EAAGA,CAAO,MAEzC,OAAM,IAAIE,EAAqB,oCAAoC,EAErE,IAAMC,EAAM,KAAK,SAAS,IAC1B,GAAI,OAAOA,GAAQ,SACjB,MAAM,IAAID,EAAqB,mBAAmB,EAChDC,EAAI,SAAS,GAAG,IAAG,KAAK,SAAS,IAAMA,EAAI,UAAU,EAAGA,EAAI,OAAS,CAAC,GAC1E,KAAK,WAAYF,EAAA,KAAK,SAAS,YAAd,KAAAA,EAA2B,IAAIG,EAAc,KAAK,QAAQ,EAC3E,OAAO,KAAK,SAAS,MACrB,KAAK,mBAAqB,CACxBC,EACAC,IAEA,IAAIC,EACFF,EACAC,EACA,KAAK,UAAU,aACjB,CACJ,CAoBA,YACEE,EACAC,EACAC,EAAgC,KAChCC,EACU,CACV,OAAO,IAAIC,EACT,KAAK,UACLJ,EACAC,EACAC,EACAC,GAAA,KAAAA,EAAgB,KAAK,SAAS,YAChC,CACF,CAcA,YAAYH,EAAsC,CAChD,OAAO,IAAIK,GAAa,KAAK,UAAW,KAAK,mBAAoBL,CAAG,CACtE,CACF", "names": ["src_exports", "__export", "AbortError", "DEFAULT_ConnectionOptions", "DEFAULT_RetryDelayStrategyOptions", "DEFAULT_WriteOptions", "FLUX_VALUE", "HttpError", "IllegalArgumentError", "InfluxDB", "LineSplitter", "Log", "Point", "RequestTimedOutError", "UNKNOWN_COLUMN", "canRetryHttpCall", "chunksToLines", "chunksToLinesIterable", "consoleLogger", "convertTimeToNanos", "createFluxTableColumn", "createFluxTableMetaData", "createTextDecoderCombiner", "currentTime", "dateToProtocolTimestamp", "escape", "flux", "fluxBool", "fluxDateTime", "fluxDuration", "fluxExpression", "fluxFloat", "fluxInteger", "fluxRegExp", "fluxString", "getRetryDelay", "isStatusCodeRetriable", "linesToRowsIterable", "linesToTables", "newFluxTableColumn", "sanitizeFloat", "sanitizeInteger", "serializeDateTimeAsDate", "serializeDateTimeAsNumber", "serializeDateTimeAsString", "<PERSON><PERSON><PERSON><PERSON>", "stringToLines", "symbolObservable", "toFluxValue", "typeSerializers", "useProcessHrtime", "createTextDecoderCombiner", "decoder", "first", "second", "retVal", "chunk", "start", "end", "chunksToLines", "target", "chunkCombiner", "chunks", "createTextDecoderCombiner", "previous", "finished", "quoted", "paused", "resumeChunks", "bufferReceived", "chunk", "index", "start", "c", "end", "retVal", "e", "error", "cancellable", "x", "chunksToLinesIterable", "source", "chunkCombiner", "chunks", "createTextDecoderCombiner", "previous", "quoted", "chunk", "index", "start", "c", "end", "LineSplitter", "val", "line", "quoteCount", "startIndex", "values", "count", "i", "c", "start", "end", "identity", "x", "typeSerializers", "FluxTableColumnImpl", "row", "_a", "val", "UNKNOWN_COLUMN", "newFluxTableColumn", "createFluxTableColumn", "object", "_b", "retVal", "retriableStatusCodes", "isStatusCodeRetriable", "statusCode", "IllegalArgumentError", "_IllegalArgumentError", "message", "HttpError", "_HttpError", "statusMessage", "body", "retryAfter", "contentType", "headers", "e", "RETRY_CODES", "canRetryHttpCall", "error", "getRetryDelay", "retryJitter", "retVal", "RequestTimedOutError", "_RequestTimedOutError", "AbortError", "_AbortError", "serializeDateTimeAsDate", "typeSerializers", "x", "serializeDateTimeAsNumber", "serializeDateTimeAsString", "FluxTableMetaDataImpl", "columns", "col", "i", "label", "errorOnMissingColumn", "IllegalArgumentError", "UNKNOWN_COLUMN", "row", "acc", "column", "createFluxTableMetaData", "linesToTables", "consumer", "splitter", "LineSplitter", "columns", "expectMeta", "firstColumnIndex", "lastMeta", "retVal", "error", "line", "values", "size", "i", "newFluxTableColumn", "createFluxTableMetaData", "linesToRowsIterable", "source", "splitter", "LineSplitter", "columns", "expectMeta", "firstColumnIndex", "lastMeta", "line", "values", "size", "i", "newFluxTableColumn", "createFluxTableMetaData", "stringToLines", "source", "target", "quoted", "start", "index", "c", "end", "symbolObservable", "QuerySubscription", "observer", "executor", "value", "e", "c", "_a", "noop", "completeObserver", "next", "error", "complete", "ObservableQuery", "decorator", "observerOrNext", "symbolObservable", "DEFAULT_ConnectionOptions", "DEFAULT_RetryDelayStrategyOptions", "DEFAULT_WriteOptions", "createEscaper", "characters", "replacements", "value", "retVal", "from", "i", "found", "createQuotedEscaper", "escaper", "escape", "zeroPadding", "useProcessHrtime", "use", "<PERSON><PERSON><PERSON><PERSON>", "stepsInMillis", "nanos", "millis", "zeroPadding", "micros", "seconds", "currentTime", "dateToProtocolTimestamp", "d", "convertTimeToNanos", "value", "consoleLogger", "message", "error", "provider", "Log", "<PERSON><PERSON><PERSON><PERSON>", "logger", "previous", "FLUX_VALUE", "FluxParameter", "fluxValue", "isFluxParameterLike", "value", "sanitizeString", "retVal", "i", "prepareRetVal", "c", "fluxString", "sanitizeFloat", "val", "strVal", "hasDot", "fluxFloat", "sanitizeInteger", "negative", "fluxInteger", "sanitizeDateTime", "fluxDateTime", "fluxDuration", "sanitizeRegExp", "fluxRegExp", "fluxBool", "fluxExpression", "toFluxValue", "flux", "strings", "values", "parts", "partIndex", "text", "sanitized", "Point", "measurementName", "name", "value", "val", "strVal", "code", "escape", "settings", "fieldsLine", "x", "tagsLine", "tags", "time", "convertTimeToNanos", "line", "RetryStrategyImpl", "options", "DEFAULT_RetryDelayStrategyOptions", "error", "failedAttempts", "delay", "getRetryDelay", "nextDelay", "i", "createRetryDelayStrategy", "findShrinkCandidate", "first", "parent", "found", "currentParent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLines", "retryLines", "onShrink", "lines", "retryCount", "delay", "expires", "retryTime", "origSize", "newSize", "Log", "toAdd", "current", "toRetry", "utf8Length", "s", "retVal", "i", "code", "WriteBuffer", "maxChunkRecords", "maxBatchBytes", "flushFn", "scheduleSend", "record", "size", "utf8Length", "_e", "lines", "retVal", "WriteApiImpl", "transport", "org", "bucket", "precision", "writeOptions", "DEFAULT_WriteOptions", "currentTime", "dateToProtocolTimestamp", "scheduleNextSend", "createRetryDelayStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retryAttempts", "expires", "self", "failedAttempts", "error", "onRetry", "Log", "resolve", "reject", "responseStatusCode", "headers", "callbacks", "_headers", "statusCode", "HttpError", "message", "records", "i", "point", "line", "points", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remaining", "tags", "value", "completeCommunicationObserver", "callbacks", "state", "retVal", "data", "error", "headers", "statusCode", "getResponseHeaders", "response", "headers", "value", "key", "previous", "FetchTransport", "connectionOptions", "createTextDecoderCombiner", "Log", "path", "body", "options", "callbacks", "observer", "completeCommunicationObserver", "cancelled", "signal", "pausePromise", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resume", "controller", "reader", "chunk", "useResume", "msg", "resolve", "buffer", "text", "e", "headerError", "HttpError", "_a", "done", "AbortError", "responseStarted", "_b", "responseContentType", "responseType", "method", "other", "url", "request", "DEFAULT_dialect", "QueryApiImpl", "_QueryApiImpl", "transport", "createCSVResponse", "org", "options", "query", "type", "gzip", "headers", "path", "body", "consumer", "rowMapper", "request", "_a", "QueryApiImpl_default", "defaultRowMapping", "values", "tableMeta", "AnnotatedCSVResponseImpl", "executor", "iterableResultExecutor", "chunkCombiner", "chunksToLinesIterable", "linesToRowsIterable", "ObservableQuery", "observer", "chunksToLines", "linesToTables", "e", "consumer", "rowMapper", "retVal", "resolve", "reject", "toAdd", "error", "line", "InfluxDB", "options", "_a", "IllegalArgumentError", "url", "FetchTransport", "executor", "iterableResultExecutor", "AnnotatedCSVResponseImpl", "org", "bucket", "precision", "writeOptions", "WriteApiImpl", "QueryApiImpl_default"]}
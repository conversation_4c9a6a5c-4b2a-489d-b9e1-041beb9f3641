"use strict";var Xe=Object.create;var B=Object.defineProperty;var Ye=Object.getOwnPropertyDescriptor;var Ge=Object.getOwnPropertyNames;var Ke=Object.getPrototypeOf,Ze=Object.prototype.hasOwnProperty;var Oe=t=>{throw TypeError(t)};var et=(t,e)=>{for(var r in e)B(t,r,{get:e[r],enumerable:!0})},Ae=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Ge(e))!Ze.call(t,i)&&i!==r&&B(t,i,{get:()=>e[i],enumerable:!(n=Ye(e,i))||n.enumerable});return t};var Q=(t,e,r)=>(r=t!=null?Xe(Ke(t)):{},Ae(e||!t||!t.__esModule?B(r,"default",{value:t,enumerable:!0}):r,t)),tt=t=>Ae(B({},"__esModule",{value:!0}),t);var Ee=(t,e,r)=>e.has(t)||Oe("Cannot "+r);var j=(t,e,r)=>(Ee(t,e,"read from private field"),r?r.call(t):e.get(t)),De=(t,e,r)=>e.has(t)?Oe("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r),Pe=(t,e,r,n)=>(Ee(t,e,"write to private field"),n?n.call(t,r):e.set(t,r),r);var Et={};et(Et,{AbortError:()=>x,DEFAULT_ConnectionOptions:()=>re,DEFAULT_RetryDelayStrategyOptions:()=>ne,DEFAULT_WriteOptions:()=>ie,FLUX_VALUE:()=>D,HttpError:()=>g,IllegalArgumentError:()=>y,InfluxDB:()=>U,LineSplitter:()=>b,Log:()=>m,Point:()=>he,RequestTimedOutError:()=>C,UNKNOWN_COLUMN:()=>X,canRetryHttpCall:()=>st,chunksToLines:()=>w,chunksToLinesIterable:()=>$,consoleLogger:()=>Me,convertTimeToNanos:()=>pe,createFluxTableColumn:()=>rt,createFluxTableMetaData:()=>E,createTextDecoderCombiner:()=>O,currentTime:()=>ue,dateToProtocolTimestamp:()=>fe,escape:()=>R,flux:()=>Tt,fluxBool:()=>wt,fluxDateTime:()=>bt,fluxDuration:()=>xt,fluxExpression:()=>ce,fluxFloat:()=>ht,fluxInteger:()=>gt,fluxRegExp:()=>Rt,fluxString:()=>mt,getRetryDelay:()=>Y,isStatusCodeRetriable:()=>Fe,linesToRowsIterable:()=>K,linesToTables:()=>z,newFluxTableColumn:()=>A,sanitizeFloat:()=>de,sanitizeInteger:()=>me,serializeDateTimeAsDate:()=>ot,serializeDateTimeAsNumber:()=>at,serializeDateTimeAsString:()=>lt,setLogger:()=>ct,stringToLines:()=>ut,symbolObservable:()=>Z,toFluxValue:()=>N,typeSerializers:()=>T,useProcessHrtime:()=>$e});module.exports=tt(Et);function O(){let t=new TextDecoder("utf-8");return{concat(e,r){let n=new Uint8Array(e.length+r.length);return n.set(e),n.set(r,e.length),n},copy(e,r,n){let i=new Uint8Array(n-r);return i.set(e.subarray(r,n)),i},toUtf8String(e,r,n){return t.decode(e.subarray(r,n))}}}function w(t,e){let r=e!=null?e:O(),n,i=!1,s=!1,a=!1,o;function l(u){let p,c=0;for(n?(p=u.length===0?0:n.length,u=r.concat(n,u)):p=0;p<u.length;){let d=u[p];if(d===10){if(!s){let _=p>0&&u[p-1]===13?p-1:p;if(i)return;if(a=t.next(r.toUtf8String(u,c,_))===!1,c=p+1,a)break}}else d===34&&(s=!s);p++}if(c<u.length?n=r.copy(u,c,u.length):n=void 0,a){if(t.useResume){t.useResume(()=>{a=!1,l(new Uint8Array(0))});return}f.error(new Error("Unable to pause, useResume is not configured!")),a=!1}o&&(o(),o=void 0)}let f={next(u){if(!i)try{return l(u),!a}catch(p){this.error(p)}return!0},error(u){i||(i=!0,t.error(u))},complete(){i||(n&&t.next(r.toUtf8String(n,0,n.length)),i=!0,t.complete())}};return t.useCancellable&&(f.useCancellable=u=>{t.useCancellable&&t.useCancellable({cancel(){u.cancel(),n=void 0,f.complete()},isCancelled(){return u.isCancelled()}})}),t.useResume&&(f.useResume=u=>{o=u}),f}async function*$(t,e){let r=e!=null?e:O(),n,i=!1;for await(let s of t){let a,o=0;for(n?(a=n.length,s=r.concat(n,s)):a=0;a<s.length;){let l=s[a];if(l===10){if(!i){let f=a>0&&s[a-1]===13?a-1:a;yield r.toUtf8String(s,o,f),o=a+1}}else l===34&&(i=!i);a++}o<s.length?n=r.copy(s,o,s.length):n=void 0}n&&(yield r.toUtf8String(n,0,n.length))}var b=class{constructor(){this._reuse=!1}get reuse(){return this._reuse}set reuse(e){e&&!this.reusedValues&&(this.reusedValues=new Array(10)),this._reuse=e}withReuse(){return this.reuse=!0,this}splitLine(e){if(e==null)return this.lastSplitLength=0,[];let r=0,n=0,i=this._reuse?this.reusedValues:[],s=0;for(let o=0;o<e.length;o++){let l=e[o];if(l===","){if(r%2===0){let f=this.getValue(e,n,o,r);this._reuse?i[s++]=f:i.push(f),n=o+1,r=0}}else l==='"'&&r++}let a=this.getValue(e,n,e.length,r);return this._reuse?(i[s]=a,this.lastSplitLength=s+1):(i.push(a),this.lastSplitLength=i.length),i}getValue(e,r,n,i){return r===e.length?"":i===0?e.substring(r,n):i===2?e.substring(r+1,n-1):e.substring(r+1,n-1).replace(/""/gi,'"')}};var J=t=>t,T={boolean:t=>t===""?null:t==="true",unsignedLong:t=>t===""?null:+t,long:t=>t===""?null:+t,double(t){switch(t){case"":return null;case"+Inf":return Number.POSITIVE_INFINITY;case"-Inf":return Number.NEGATIVE_INFINITY;default:return+t}},string:J,base64Binary:J,duration:t=>t===""?null:t,"dateTime:RFC3339":t=>t===""?null:t},M=class{get(e){var n;let r=e[this.index];return(r===""||r===void 0)&&this.defaultValue&&(r=this.defaultValue),((n=T[this.dataType])!=null?n:J)(r)}},X=Object.freeze({label:"",dataType:"",group:!1,defaultValue:"",index:Number.MAX_SAFE_INTEGER,get:()=>{}});function A(){return new M}function rt(t){var r,n;let e=new M;return e.label=String(t.label),e.dataType=t.dataType,e.group=!!t.group,e.defaultValue=(r=t.defaultValue)!=null?r:"",e.index=(n=t.index)!=null?n:0,e}var nt=[404,408,425,429,500,502,503,504];function Fe(t){return nt.includes(t)}var y=class t extends Error{constructor(e){super(e),this.name="IllegalArgumentError",Object.setPrototypeOf(this,t.prototype)}},g=class t extends Error{constructor(r,n,i,s,a,o,l){super();this.statusCode=r;this.statusMessage=n;this.body=i;this.contentType=a;if(Object.setPrototypeOf(this,t.prototype),this.headers=l,o)this.message=o;else if(i){if(a!=null&&a.startsWith("application/json"))try{this.json=JSON.parse(i),this.message=this.json.message,this.code=this.json.code}catch(f){}this.message||(this.message=`${r} ${n} : ${i}`)}else this.message=`${r} ${n}`;this.name="HttpError",this.setRetryAfter(s)}setRetryAfter(r){typeof r=="string"?/^[0-9]+$/.test(r)?this._retryAfter=parseInt(r):this._retryAfter=0:this._retryAfter=0}canRetry(){return Fe(this.statusCode)}retryAfter(){return this._retryAfter}},it=["ECONNRESET","ENOTFOUND","ESOCKETTIMEDOUT","ETIMEDOUT","ECONNREFUSED","EHOSTUNREACH","EPIPE"];function st(t){if(t){if(typeof t.canRetry=="function")return!!t.canRetry();if(t.code&&it.includes(t.code))return!0}else return!1;return!1}function Y(t,e){if(t){let r;return typeof t.retryAfter=="function"?t.retryAfter():(r=0,e&&e>0?r+Math.round(Math.random()*e):r)}else return 0}var C=class t extends Error{constructor(){super(),Object.setPrototypeOf(this,t.prototype),this.name="RequestTimedOutError",this.message="Request timed out"}canRetry(){return!0}retryAfter(){return 0}},x=class t extends Error{constructor(){super(),this.name="AbortError",Object.setPrototypeOf(this,t.prototype),this.message="Response aborted"}canRetry(){return!0}retryAfter(){return 0}};function ot(){T["dateTime:RFC3339"]=t=>t===""?null:new Date(Date.parse(t))}function at(){T["dateTime:RFC3339"]=t=>t===""?null:Date.parse(t)}function lt(){T["dateTime:RFC3339"]=t=>t===""?null:t}var G=class{constructor(e){e.forEach((r,n)=>r.index=n),this.columns=e}column(e,r=!0){for(let n=0;n<this.columns.length;n++){let i=this.columns[n];if(i.label===e)return i}if(r)throw new y(`Column ${e} not found!`);return X}toObject(e){let r={};for(let n=0;n<this.columns.length&&n<e.length;n++){let i=this.columns[n];r[i.label]=i.get(e)}return r}get(e,r){return this.column(r,!1).get(e)}};function E(t){return new G(t)}function z(t){let e=new b().withReuse(),r,n=!0,i=0,s,a={error(o){t.error(o)},next(o){if(o==="")n=!0,r=void 0;else{let l=e.splitLine(o),f=e.lastSplitLength;if(n){if(!r){r=new Array(f);for(let u=0;u<f;u++)r[u]=A()}if(l[0].startsWith("#")){if(l[0]==="#datatype")for(let u=1;u<f;u++)r[u].dataType=l[u];else if(l[0]==="#default")for(let u=1;u<f;u++)r[u].defaultValue=l[u];else if(l[0]==="#group")for(let u=1;u<f;u++)r[u].group=l[u][0]==="t"}else{l[0]===""?(i=1,r=r.slice(1)):i=0;for(let u=i;u<f;u++)r[u-i].label=l[u];s=E(r),n=!1}}else return t.next(l.slice(i,f),s)}return!0},complete(){t.complete()}};return t.useCancellable&&(a.useCancellable=t.useCancellable.bind(t)),t.useResume&&(a.useResume=t.useResume.bind(t)),a}async function*K(t){let e=new b().withReuse(),r,n=!0,i=0,s;for await(let a of t)if(a==="")n=!0,r=void 0;else{let o=e.splitLine(a),l=e.lastSplitLength;if(n){if(!r){r=new Array(l);for(let f=0;f<l;f++)r[f]=A()}if(o[0].startsWith("#")){if(o[0]==="#datatype")for(let f=1;f<l;f++)r[f].dataType=o[f];else if(o[0]==="#default")for(let f=1;f<l;f++)r[f].defaultValue=o[f];else if(o[0]==="#group")for(let f=1;f<l;f++)r[f].group=o[f][0]==="t"}else{o[0]===""?(i=1,r=r.slice(1)):i=0;for(let f=i;f<l;f++)r[f-i].label=o[f];s=E(r),n=!1}}else yield{values:o.slice(i,l),tableMeta:s}}}function ut(t,e){let r=!1,n=0,i=0;for(;i<t.length;){let s=t.charCodeAt(i);if(s===10){if(!r){let a=i>0&&t.charCodeAt(i-1)===13?i-1:i;e.next(t.substring(n,a)),n=i+1}}else s===34&&(r=!r);i++}n<i&&e.next(t.substring(n,i)),e.complete()}var Z=typeof Symbol=="function"&&Symbol.observable||"@@observable";var te=class{constructor(e,r){this.isClosed=!1;try{r({next:n=>{e.next(n)},error:n=>{this.isClosed=!0,e.error(n)},complete:()=>{this.isClosed=!0,e.complete()},useCancellable:n=>{this.cancellable=n}})}catch(n){this.isClosed=!0,e.error(n)}}get closed(){return this.isClosed}unsubscribe(){var e;(e=this.cancellable)==null||e.cancel(),this.isClosed=!0}};function ee(){}function ft(t){let{next:e,error:r,complete:n}=t;return{next:e?e.bind(t):ee,error:r?r.bind(t):ee,complete:n?n.bind(t):ee}}var S=class{constructor(e,r){this.executor=e;this.decorator=r}subscribe(e,r,n){let i=ft(typeof e!="object"||e===null?{next:e,error:r,complete:n}:e);return new te(this.decorator(i),this.executor)}[Z](){return this}};var re={timeout:1e4},ne={retryJitter:200,minRetryDelay:5e3,maxRetryDelay:125e3,exponentialBase:5,randomRetry:!0},ie={batchSize:1e3,maxBatchBytes:5e7,flushInterval:6e4,writeFailed:function(){},writeSuccess:function(){},writeRetrySkipped:function(){},maxRetries:5,maxRetryTime:18e4,maxBufferLines:32e3,retryJitter:200,minRetryDelay:5e3,maxRetryDelay:125e3,exponentialBase:2,gzipThreshold:1e3,randomRetry:!0};function se(t,e){return function(r){let n="",i=0,s=0;for(;s<r.length;){let a=t.indexOf(r[s]);a>=0&&(n+=r.substring(i,s),n+=e[a],i=s+1),s++}return i==0?r:(i<r.length&&(n+=r.substring(i,r.length)),n)}}function pt(t,e){let r=se(t,e);return n=>'"'+r(n)+'"'}var R={measurement:se(`, 
\r	`,["\\,","\\ ","\\n","\\r","\\t"]),quoted:pt('"\\',['\\"',"\\\\"]),tag:se(`, =
\r	`,["\\,","\\ ","\\=","\\n","\\r","\\t"])};var k="000000000",le=!1;function $e(t){return le=t&&process&&typeof process.hrtime=="function"}$e(!0);var Le,V,Ie=Date.now(),oe=0;function ae(){if(le){let t=process.hrtime(),e=Date.now();V?(t[0]=t[0]-V[0],t[1]=t[1]-V[1],t[1]<0&&(t[0]-=1,t[1]+=1e9),e=Le+t[0]*1e3+Math.floor(t[1]/1e6)):(V=t,Le=e);let r=String(t[1]%1e6);return String(e)+k.substr(0,6-r.length)+r}else{let t=Date.now();t!==Ie?(Ie=t,oe=0):oe++;let e=String(oe);return String(t)+k.substr(0,6-e.length)+e}}function Ue(){if(le){let t=process.hrtime(),e=String(Math.trunc(t[1]/1e3)%1e3);return String(Date.now())+k.substr(0,3-e.length)+e}else return String(Date.now())+k.substr(0,3)}function _e(){return String(Date.now())}function Be(){return String(Math.floor(Date.now()/1e3))}var ue={s:Be,ms:_e,us:Ue,ns:ae,seconds:Be,millis:_e,micros:Ue,nanos:ae},fe={s:t=>`${Math.floor(t.getTime()/1e3)}`,ms:t=>`${t.getTime()}`,us:t=>`${t.getTime()}000`,ns:t=>`${t.getTime()}000000`};function pe(t){return t===void 0?ae():typeof t=="string"?t.length>0?t:void 0:t instanceof Date?`${t.getTime()}000000`:String(typeof t=="number"?Math.floor(t):t)}var Me={error(t,e){console.error("ERROR: "+t,e||"")},warn(t,e){console.warn("WARN: "+t,e||"")}},W=Me,m={error(t,e){W.error(t,e)},warn(t,e){W.warn(t,e)}};function ct(t){let e=W;return W=t,e}var D=Symbol("FLUX_VALUE"),h=class{constructor(e){this.fluxValue=e}toString(){return this.fluxValue}[D](){return this.fluxValue}};function dt(t){return typeof t=="object"&&typeof t[D]=="function"}function P(t){if(t==null)return"";t=t.toString();let e,r=0;function n(){e===void 0&&(e=t.substring(0,r))}for(;r<t.length;r++){let i=t.charAt(r);switch(i){case"\r":n(),e+="\\r";break;case`
`:n(),e+="\\n";break;case"	":n(),e+="\\t";break;case'"':case"\\":n(),e=e+"\\"+i;break;case"$":if(r+1<t.length&&t.charAt(r+1)==="{"){n(),r++,e+="\\${";break}e!=null&&(e+=i);break;default:e!=null&&(e+=i)}}return e!==void 0?e:t}function mt(t){return new h(`"${P(t)}"`)}function de(t){let e=Number(t);if(!isFinite(e)){if(typeof t=="number")return`float(v: "${e}")`;throw new Error(`not a flux float: ${t}`)}let r=e.toString(),n=!1;for(let i of r)if(!(i>="0"&&i<="9"||i=="-")){if(i==="."){n=!0;continue}return`float(v: "${r}")`}return n?r:r+".0"}function ht(t){return new h(de(t))}function me(t){let e=String(t),r=e.startsWith("-"),n=r?e.substring(1):e;if(n.length===0||n.length>19)throw new Error(`not a flux integer: ${e}`);for(let i of n)if(i<"0"||i>"9")throw new Error(`not a flux integer: ${e}`);if(n.length===19){if(r&&n>"9223372036854775808")throw new Error(`flux integer out of bounds: ${e}`);if(!r&&n>"9223372036854775807")throw new Error(`flux integer out of bounds: ${e}`)}return e}function gt(t){return new h(me(t))}function yt(t){return`time(v: "${P(t)}")`}function bt(t){return new h(yt(t))}function xt(t){return new h(`duration(v: "${P(t)}")`)}function ze(t){return t instanceof RegExp?t.toString():new RegExp(t).toString()}function Rt(t){return new h(ze(t))}function wt(t){return t==="true"||t==="false"?new h(t):new h((!!t).toString())}function ce(t){return new h(String(t))}function N(t){if(t===void 0)return"";if(t===null)return"null";if(typeof t=="boolean")return t.toString();if(typeof t=="string")return`"${P(t)}"`;if(typeof t=="number")return Number.isSafeInteger(t)?me(t):de(t);if(typeof t=="object"){if(typeof t[D]=="function")return t[D]();if(t instanceof Date)return t.toISOString();if(t instanceof RegExp)return ze(t);if(Array.isArray(t))return`[${t.map(N).join(",")}]`}else if(typeof t=="bigint")return`${t}.0`;return N(t.toString())}function Tt(t,...e){if(t.length==1&&e.length===0)return ce(t[0]);let r=new Array(t.length+e.length),n=0;for(let i=0;i<t.length;i++){let s=t[i];if(r[n++]=s,i<e.length){let a=e[i],o;if(s.endsWith('"')&&i+1<t.length&&t[i+1].startsWith('"'))o=P(a);else if(o=N(a),o===""&&!dt(a))throw new Error(`Unsupported parameter literal '${a}' at index: ${i}, type: ${typeof a}`);r[n++]=o}else if(i<t.length-1)throw new Error("Too few parameters supplied!")}return ce(r.join(""))}var he=class{constructor(e){this.tags={};this.fields={};e&&(this.name=e)}measurement(e){return this.name=e,this}tag(e,r){return this.tags[e]=r,this}booleanField(e,r){return this.fields[e]=r?"T":"F",this}intField(e,r){let n;if(typeof r=="number"?n=r:n=parseInt(String(r)),isNaN(n)||n<=-9223372036854776e3||n>=9223372036854776e3)throw new Error(`invalid integer value for field '${e}': '${r}'!`);return this.fields[e]=`${Math.floor(n)}i`,this}uintField(e,r){if(typeof r=="number"){if(isNaN(r)||r<0||r>Number.MAX_SAFE_INTEGER)throw new Error(`uint value for field '${e}' out of range: ${r}`);this.fields[e]=`${Math.floor(r)}u`}else{let n=String(r);for(let i=0;i<n.length;i++){let s=n.charCodeAt(i);if(s<48||s>57)throw new Error(`uint value has an unsupported character at pos ${i}: ${r}`)}if(n.length>20||n.length===20&&n.localeCompare("18446744073709551615")>0)throw new Error(`uint value for field '${e}' out of range: ${n}`);this.fields[e]=`${n}u`}return this}floatField(e,r){let n;if(typeof r=="number"?n=r:n=parseFloat(r),!isFinite(n))throw new Error(`invalid float value for field '${e}': ${r}`);return this.fields[e]=String(n),this}stringField(e,r){return r!=null&&(typeof r!="string"&&(r=String(r)),this.fields[e]=R.quoted(r)),this}timestamp(e){return this.time=e,this}toLineProtocol(e){if(!this.name)return;let r="";if(Object.keys(this.fields).sort().forEach(a=>{if(a){let o=this.fields[a];r.length>0&&(r+=","),r+=`${R.tag(a)}=${o}`}}),r.length===0)return;let n="",i=e&&e.defaultTags?{...e.defaultTags,...this.tags}:this.tags;Object.keys(i).sort().forEach(a=>{if(a){let o=i[a];o&&(n+=",",n+=`${R.tag(a)}=${R.tag(o)}`)}});let s=this.time;return e&&e.convertTime?s=e.convertTime(s):s=pe(s),`${R.measurement(this.name)}${n} ${r}${s!==void 0?" "+s:""}`}toString(){let e=this.toLineProtocol(void 0);return e||`invalid point: ${JSON.stringify(this,void 0)}`}};var ge=class{constructor(e){this.options={...ne,...e},this.success()}nextDelay(e,r){let n=Y(e);if(n&&n>0)return n+Math.round(Math.random()*this.options.retryJitter);if(r&&r>0){if(this.options.randomRetry){let s=Math.max(this.options.minRetryDelay,1),a=s*this.options.exponentialBase;for(let o=1;o<r;o++)if(s=a,a=a*this.options.exponentialBase,a>=this.options.maxRetryDelay){a=this.options.maxRetryDelay;break}return s+Math.round(Math.random()*(a-s)+Math.random()*this.options.retryJitter)}let i=Math.max(this.options.minRetryDelay,1);for(let s=1;s<r;s++)if(i=i*this.options.exponentialBase,i>=this.options.maxRetryDelay){i=this.options.maxRetryDelay;break}return i+Math.round(Math.random()*this.options.retryJitter)}else this.currentDelay?this.currentDelay=Math.min(Math.max(this.currentDelay*this.options.exponentialBase,1)+Math.round(Math.random()*this.options.retryJitter),this.options.maxRetryDelay):this.currentDelay=this.options.minRetryDelay+Math.round(Math.random()*this.options.retryJitter);return this.currentDelay}success(){this.currentDelay=void 0}};function Ve(t){return new ge(t)}function Ct(t){let e,r=t,n=t;for(;n.next;)n.next.expires<r.expires&&(e=n,r=n.next),n=n.next;return[r,e]}var F=class{constructor(e,r,n=()=>{}){this.maxLines=e;this.retryLines=r;this.onShrink=n;this.size=0;this.closed=!1;this._timeoutHandle=void 0}addLines(e,r,n,i){if(this.closed||!e.length)return;let s=Date.now()+n;if(i<s&&(s=i),this.first&&this.size+e.length>this.maxLines){let f=this.size,u=f*.7;do{let[p,c]=Ct(this.first);this.size-=p.lines.length,c?c.next=p.next:(this.first=p.next,this.first&&this.scheduleRetry(this.first.retryTime-Date.now())),p.next=void 0,this.onShrink(p)}while(this.first&&this.size+e.length>u);m.error(`RetryBuffer: ${f-this.size} oldest lines removed to keep buffer size under the limit of ${this.maxLines} lines.`)}let a={lines:e,retryCount:r,retryTime:s,expires:i},o=this.first,l;for(;;){if(!o||o.retryTime>s){a.next=o,l?l.next=a:(this.first=a,this.scheduleRetry(s-Date.now()));break}l=o,o=o.next}this.size+=e.length}removeLines(){if(this.first){let e=this.first;return this.first=this.first.next,e.next=void 0,this.size-=e.lines.length,e}}scheduleRetry(e){this._timeoutHandle&&clearTimeout(this._timeoutHandle),this._timeoutHandle=setTimeout(()=>{let r=this.removeLines();r?this.retryLines(r.lines,r.retryCount,r.expires).catch(()=>{}).finally(()=>{this.first&&this.scheduleRetry(this.first.retryTime-Date.now())}):this._timeoutHandle=void 0},Math.max(e,0))}async flush(){let e;for(;e=this.removeLines();)await this.retryLines(e.lines,e.retryCount,e.expires)}close(){return this._timeoutHandle&&(clearTimeout(this._timeoutHandle),this._timeoutHandle=void 0),this.closed=!0,this.size}};function ye(t){let e=t.length;for(let r=0;r<t.length;r++){let n=t.charCodeAt(r);n<128||(n>=128&&n<=2047?e++:n>=2048&&n<=65535?n>=55296&&n<=57343?e++:e+=2:e+=3)}return e}var be=class{constructor(e,r,n,i){this.maxChunkRecords=e;this.maxBatchBytes=r;this.flushFn=n;this.scheduleSend=i;this.length=0;this.bytes=-1;this.lines=new Array(e)}add(e){let r=ye(e);this.length===0?this.scheduleSend():this.bytes+r+1>=this.maxBatchBytes&&this.flush().catch(n=>{}),this.lines[this.length]=e,this.length++,this.bytes+=r+1,(this.length>=this.maxChunkRecords||this.bytes>=this.maxBatchBytes)&&this.flush().catch(n=>{})}flush(){let e=this.reset();return e.length>0?this.flushFn(e):Promise.resolve()}reset(){let e=this.lines.slice(0,this.length);return this.length=0,this.bytes=-1,e}},L=class{constructor(e,r,n,i,s){this.transport=e;this.closed=!1;this._timeoutHandle=void 0;this.path=`/api/v2/write?org=${encodeURIComponent(r)}&bucket=${encodeURIComponent(n)}&precision=${i}`,s!=null&&s.consistency&&(this.path+=`&consistency=${encodeURIComponent(s.consistency)}`),this.writeOptions={...ie,...s},this.currentTime=ue[i],this.dateToProtocolTimestamp=fe[i],this.writeOptions.defaultTags&&this.useDefaultTags(this.writeOptions.defaultTags),this.sendOptions={method:"POST",headers:{"content-type":"text/plain; charset=utf-8",...s==null?void 0:s.headers},gzipThreshold:this.writeOptions.gzipThreshold};let a=()=>{this.writeOptions.flushInterval>0&&(this._clearFlushTimeout(),this.closed||(this._timeoutHandle=setTimeout(()=>this.sendBatch(this.writeBuffer.reset(),this.writeOptions.maxRetries).catch(o=>{}),this.writeOptions.flushInterval)))};this.writeBuffer=new be(this.writeOptions.batchSize,this.writeOptions.maxBatchBytes,o=>(this._clearFlushTimeout(),this.sendBatch(o,this.writeOptions.maxRetries)),a),this.sendBatch=this.sendBatch.bind(this),this.retryStrategy=Ve(this.writeOptions),this.retryBuffer=new F(this.writeOptions.maxBufferLines,this.sendBatch,this.writeOptions.writeRetrySkipped)}sendBatch(e,r,n=Date.now()+this.writeOptions.maxRetryTime){let i=this,s=i.writeOptions.maxRetries+1-r;if(!this.closed&&e.length>0){if(n<=Date.now()){let a=new Error("Max retry time exceeded."),o=i.writeOptions.writeFailed.call(i,a,e,s,n);return o||(m.error(`Write to InfluxDB failed (attempt: ${s}).`,a),Promise.reject(a))}return new Promise((a,o)=>{let l,f,u={responseStarted(p,c){l=c,f=p},error(p){let c=i.writeOptions.writeFailed.call(i,p,e,s,n);if(c){c.then(a,o);return}if(p instanceof g&&p.json&&typeof p.json.error=="string"&&p.json.error.includes("hinted handoff queue not empty")){m.warn("Write to InfluxDB returns: "+p.json.error),l=204,u.complete();return}if(!i.closed&&r>0&&(!(p instanceof g)||p.statusCode>=429)){m.warn(`Write to InfluxDB failed (attempt: ${s}).`,p),i.retryBuffer.addLines(e,r-1,i.retryStrategy.nextDelay(p,s),n),o(p);return}m.error("Write to InfluxDB failed.",p),o(p)},complete(){if(l==204||l==201||l==null)i.writeOptions.writeSuccess.call(i,e),i.retryStrategy.success(),a();else{let p=`204 HTTP response status code expected, but ${l} returned`,c=new g(l,p,void 0,"0",void 0,void 0,f);c.message=p,u.error(c)}}};this.transport.send(this.path,e.join(`
`),this.sendOptions,u)})}else return Promise.resolve()}_clearFlushTimeout(){this._timeoutHandle!==void 0&&(clearTimeout(this._timeoutHandle),this._timeoutHandle=void 0)}writeRecord(e){if(this.closed)throw new Error("writeApi: already closed!");this.writeBuffer.add(e)}writeRecords(e){if(this.closed)throw new Error("writeApi: already closed!");for(let r=0;r<e.length;r++)this.writeBuffer.add(e[r])}writePoint(e){if(this.closed)throw new Error("writeApi: already closed!");let r=e.toLineProtocol(this);r&&this.writeBuffer.add(r)}writePoints(e){if(this.closed)throw new Error("writeApi: already closed!");for(let r=0;r<e.length;r++){let n=e[r].toLineProtocol(this);n&&this.writeBuffer.add(n)}}async flush(e){if(await this.writeBuffer.flush(),e)return await this.retryBuffer.flush()}close(){return this.writeBuffer.flush().finally(()=>{let r=this.retryBuffer.close();r&&m.error(`Retry buffer closed with ${r} items that were not written to InfluxDB!`,null),this.closed=!0})}dispose(){return this._clearFlushTimeout(),this.closed=!0,this.retryBuffer.close()+this.writeBuffer.length}useDefaultTags(e){return this.defaultTags=e,this}convertTime(e){return e===void 0?this.currentTime():typeof e=="string"?e.length>0?e:void 0:e instanceof Date?this.dateToProtocolTimestamp(e):String(typeof e=="number"?Math.floor(e):e)}};var we=require("url"),Ne=Q(require("http")),He=Q(require("https")),H=require("buffer");var xe=require("buffer"),St={concat(t,e){return xe.Buffer.concat([t,e])},toUtf8String(t,e,r){return t.toString("utf-8",e,r)},copy(t,e,r){let n=xe.Buffer.allocUnsafe(r-e);return t.copy(n,0,e,r),n}},ke=St;var I=Q(require("zlib"));function Re(t={}){let e=0,r={next:n=>{if(e===0&&t.next&&n!==null&&n!==void 0)return t.next(n)},error:n=>{e===0&&(e=1,t.error&&t.error(n))},complete:()=>{e===0&&(e=2,t.complete&&t.complete())},responseStarted:(n,i)=>{t.responseStarted&&t.responseStarted(n,i)}};return t.useCancellable&&(r.useCancellable=t.useCancellable.bind(t)),t.useResume&&(r.useResume=t.useResume.bind(t)),r}var We="1.35.0";var qe=require("stream");var vt={flush:I.default.constants.Z_SYNC_FLUSH,finishFlush:I.default.constants.Z_SYNC_FLUSH},Ot=H.Buffer.allocUnsafe(0),Te=class{constructor(){this.cancelled=!1}cancel(){this.cancelled=!0,this.resume&&(this.resume(),this.resume=void 0)}isCancelled(){return this.cancelled}},v,Ce=class{constructor(e){this.chunkCombiner=ke;De(this,v);var l,f,u,p,c,d,_;let{url:r,proxyUrl:n,token:i,transportOptions:s,...a}=e,o=(0,we.parse)(n||r);if(Pe(this,v,i),this.defaultOptions={...re,...a,...s,port:o.port,protocol:o.protocol,hostname:o.hostname},this.contextPath=n?r:(l=o.path)!=null?l:"",this.contextPath.endsWith("/")&&(this.contextPath=this.contextPath.substring(0,this.contextPath.length-1)),Object.keys(this.defaultOptions).forEach(ve=>this.defaultOptions[ve]===void 0&&delete this.defaultOptions[ve]),this.contextPath.endsWith("/api/v2")&&(m.warn(`Please remove '/api/v2' context path from InfluxDB base url, using ${o.protocol}//${o.hostname}:${o.port} !`),this.contextPath=""),o.protocol==="http:")this.requestApi=(p=(u=(f=this.defaultOptions["follow-redirects"])==null?void 0:f.http)==null?void 0:u.request)!=null?p:Ne.request;else if(o.protocol==="https:")this.requestApi=(_=(d=(c=this.defaultOptions["follow-redirects"])==null?void 0:c.https)==null?void 0:d.request)!=null?_:He.request;else throw new Error(`Unsupported protocol "${o.protocol} in URL: "${e.url}"`);this.headers={"User-Agent":`influxdb-client-js/${We}`,...e.headers},n&&(this.headers.Host=(0,we.parse)(r).host)}send(e,r,n,i){let s=new Te;i&&i.useCancellable&&i.useCancellable(s),this.createRequestMessage(e,r,n,a=>{this._request(a,s,i)},a=>(i==null?void 0:i.error)&&i.error(a))}request(e,r,n,i){r?typeof r!="string"&&(r=JSON.stringify(r)):r="";let s=Ot,a,o;return new Promise((l,f)=>{this.send(e,r,n,{responseStarted(u,p){i&&i(u,p),a=String(u["content-type"]),o=p},next:u=>{s=H.Buffer.concat([s,u])},complete:()=>{var p,c;let u=(c=(p=n.headers)==null?void 0:p.accept)!=null?c:a;try{o===204&&l(void 0),u.includes("json")?s.length?l(JSON.parse(s.toString("utf8"))):l(void 0):u.includes("text")||u.startsWith("application/csv")?l(s.toString("utf8")):l(s)}catch(d){f(d)}},error:u=>{f(u)}})})}async*iterate(e,r,n){var u;let i,s;function a(p){i=p,s(p)}let o=await new Promise((p,c)=>{s=c,this.createRequestMessage(e,r,n,p,a)});(u=o.signal)!=null&&u.addEventListener&&o.signal.addEventListener("abort",()=>{a(new x)});let l=await new Promise((p,c)=>{s=c;let d=this.requestApi(o,p);d.on("timeout",()=>a(new C)),d.on("error",a),d.write(o.body),d.end()}),f=await new Promise((p,c)=>{s=c,this._prepareResponse(l,p,a)});for await(let p of f){if(i)throw i;yield p}}createRequestMessage(e,r,n,i,s){let a=H.Buffer.from(r,"utf-8"),o={"content-type":"application/json; charset=utf-8",...this.headers};j(this,v)&&(o.authorization="Token "+j(this,v));let l={...this.defaultOptions,path:this.contextPath+e,method:n.method,headers:{...o,...n.headers}};if(n.signal&&(l.signal=n.signal),n.gzipThreshold!==void 0&&n.gzipThreshold<a.length){I.default.gzip(a,(f,u)=>{if(f)return s(f);l.headers["content-encoding"]="gzip",l.body=u,i(l)});return}l.body=a,l.headers["content-length"]=l.body.length,i(l)}_prepareResponse(e,r,n){var o;e.on("aborted",()=>{n(new x)}),e.on("error",n);let i=(o=e.statusCode)!=null?o:600,s=e.headers["content-encoding"],a;if(s==="gzip"?(a=I.default.createGunzip(vt),a=(0,qe.pipeline)(e,a,l=>l&&n(l))):a=e,i>=300){let l="",f=String(e.headers["content-type"]).startsWith("application/json");a.on("data",u=>{l+=u.toString(),!f&&l.length>1e3&&(l=l.slice(0,1e3),e.resume())}),a.on("end",()=>{l===""&&e.headers["x-influxdb-error"]&&(l=e.headers["x-influxdb-error"].toString()),n(new g(i,e.statusMessage,l,e.headers["retry-after"],e.headers["content-type"],void 0,e.headers))})}else r(a)}_request(e,r,n){var a;let i=Re(n);if(r.isCancelled()){i.complete();return}(a=e.signal)!=null&&a.addEventListener&&e.signal.addEventListener("abort",()=>{i.error(new x)});let s=this.requestApi(e,o=>{if(r.isCancelled()){o.resume(),i.complete();return}i.responseStarted(o.headers,o.statusCode),this._prepareResponse(o,l=>{l.on("data",f=>{if(r.isCancelled())o.resume();else if(i.next(f)===!1){if(!i.useResume){i.error(new Error("Unable to pause, useResume is not configured!")),o.resume();return}o.pause();let u=()=>{o.resume()};r.resume=u,i.useResume(u)}}),l.on("end",i.complete)},i.error)});typeof s.setTimeout=="function"&&e.timeout&&s.setTimeout(e.timeout),s.on("timeout",()=>{i.error(new C)}),s.on("error",o=>{i.error(o)}),e.body&&s.write(e.body),s.end()}};v=new WeakMap;var Qe=Ce;var je={header:!0,delimiter:",",quoteChar:'"',commentPrefix:"#",annotations:["datatype","group","default"]},Se=class t{constructor(e,r,n){this.transport=e;this.createCSVResponse=r;this.options=typeof n=="string"?{org:n}:n}with(e){return new t(this.transport,this.createCSVResponse,{...this.options,...e})}response(e){let{org:r,type:n,gzip:i,headers:s}=this.options,a=`/api/v2/query?org=${encodeURIComponent(r)}`,o=JSON.stringify(this.decorateRequest({query:e.toString(),dialect:je,type:n})),l={method:"POST",headers:{"content-type":"application/json; encoding=utf-8","accept-encoding":i?"gzip":"identity",...s}};return this.createCSVResponse(f=>this.transport.send(a,o,l,f),()=>this.transport.iterate(a,o,l))}iterateLines(e){return this.response(e).iterateLines()}iterateRows(e){return this.response(e).iterateRows()}lines(e){return this.response(e).lines()}rows(e){return this.response(e).rows()}queryLines(e,r){return this.response(e).consumeLines(r)}queryRows(e,r){return this.response(e).consumeRows(r)}collectRows(e,r){return this.response(e).collectRows(r)}collectLines(e){return this.response(e).collectLines()}queryRaw(e){let{org:r,type:n,gzip:i,headers:s}=this.options;return this.transport.request(`/api/v2/query?org=${encodeURIComponent(r)}`,JSON.stringify(this.decorateRequest({query:e.toString(),dialect:je,type:n})),{method:"POST",headers:{accept:"text/csv","accept-encoding":i?"gzip":"identity","content-type":"application/json; encoding=utf-8",...s}})}decorateRequest(e){var r;return typeof this.options.now=="function"&&(e.now=this.options.now()),e.type=(r=this.options.type)!=null?r:"flux",e}},Je=Se;function At(t,e){return e.toObject(t)}var q=class{constructor(e,r,n){this.executor=e;this.iterableResultExecutor=r;this.chunkCombiner=n}iterateLines(){return $(this.iterableResultExecutor())}iterateRows(){return K($(this.iterableResultExecutor()))}lines(){return new S(this.executor,e=>w(e,this.chunkCombiner))}rows(){return new S(this.executor,e=>w(z({next(r,n){e.next({values:r,tableMeta:n})},error(r){e.error(r)},complete(){e.complete()}}),this.chunkCombiner))}consumeLines(e){this.executor(w(e,this.chunkCombiner))}consumeRows(e){this.executor(w(z(e),this.chunkCombiner))}collectRows(e=At){let r=[];return new Promise((n,i)=>{this.consumeRows({next(s,a){let o=e.call(this,s,a);o!==void 0&&r.push(o)},error(s){i(s)},complete(){n(r)}})})}collectLines(){let e=[];return new Promise((r,n)=>{this.consumeLines({next(i){e.push(i)},error(i){n(i)},complete(){r(e)}})})}};var U=class{constructor(e){var n;if(typeof e=="string")this._options={url:e};else if(e!==null&&typeof e=="object")this._options=Object.assign({},e);else throw new y("No url or configuration specified!");let r=this._options.url;if(typeof r!="string")throw new y("No url specified!");r.endsWith("/")&&(this._options.url=r.substring(0,r.length-1)),this.transport=(n=this._options.transport)!=null?n:new Qe(this._options),delete this._options.token,this.processCSVResponse=(i,s)=>new q(i,s,this.transport.chunkCombiner)}getWriteApi(e,r,n="ns",i){return new L(this.transport,e,r,n,i!=null?i:this._options.writeOptions)}getQueryApi(e){return new Je(this.transport,this.processCSVResponse,e)}};0&&(module.exports={AbortError,DEFAULT_ConnectionOptions,DEFAULT_RetryDelayStrategyOptions,DEFAULT_WriteOptions,FLUX_VALUE,HttpError,IllegalArgumentError,InfluxDB,LineSplitter,Log,Point,RequestTimedOutError,UNKNOWN_COLUMN,canRetryHttpCall,chunksToLines,chunksToLinesIterable,consoleLogger,convertTimeToNanos,createFluxTableColumn,createFluxTableMetaData,createTextDecoderCombiner,currentTime,dateToProtocolTimestamp,escape,flux,fluxBool,fluxDateTime,fluxDuration,fluxExpression,fluxFloat,fluxInteger,fluxRegExp,fluxString,getRetryDelay,isStatusCodeRetriable,linesToRowsIterable,linesToTables,newFluxTableColumn,sanitizeFloat,sanitizeInteger,serializeDateTimeAsDate,serializeDateTimeAsNumber,serializeDateTimeAsString,setLogger,stringToLines,symbolObservable,toFluxValue,typeSerializers,useProcessHrtime});
//# sourceMappingURL=index.js.map
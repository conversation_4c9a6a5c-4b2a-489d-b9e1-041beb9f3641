var le=t=>{throw TypeError(t)};var ue=(t,e,r)=>e.has(t)||le("Cannot "+r);var k=(t,e,r)=>(ue(t,e,"read from private field"),r?r.call(t):e.get(t)),fe=(t,e,r)=>e.has(t)?le("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r),pe=(t,e,r,n)=>(ue(t,e,"write to private field"),n?n.call(t,r):e.set(t,r),r);function P(){let t=new TextDecoder("utf-8");return{concat(e,r){let n=new Uint8Array(e.length+r.length);return n.set(e),n.set(r,e.length),n},copy(e,r,n){let i=new Uint8Array(n-r);return i.set(e.subarray(r,n)),i},toUtf8String(e,r,n){return t.decode(e.subarray(r,n))}}}function C(t,e){let r=e!=null?e:P(),n,i=!1,s=!1,a=!1,o;function l(u){let p,c=0;for(n?(p=u.length===0?0:n.length,u=r.concat(n,u)):p=0;p<u.length;){let d=u[p];if(d===10){if(!s){let D=p>0&&u[p-1]===13?p-1:p;if(i)return;if(a=t.next(r.toUtf8String(u,c,D))===!1,c=p+1,a)break}}else d===34&&(s=!s);p++}if(c<u.length?n=r.copy(u,c,u.length):n=void 0,a){if(t.useResume){t.useResume(()=>{a=!1,l(new Uint8Array(0))});return}f.error(new Error("Unable to pause, useResume is not configured!")),a=!1}o&&(o(),o=void 0)}let f={next(u){if(!i)try{return l(u),!a}catch(p){this.error(p)}return!0},error(u){i||(i=!0,t.error(u))},complete(){i||(n&&t.next(r.toUtf8String(n,0,n.length)),i=!0,t.complete())}};return t.useCancellable&&(f.useCancellable=u=>{t.useCancellable&&t.useCancellable({cancel(){u.cancel(),n=void 0,f.complete()},isCancelled(){return u.isCancelled()}})}),t.useResume&&(f.useResume=u=>{o=u}),f}async function*W(t,e){let r=e!=null?e:P(),n,i=!1;for await(let s of t){let a,o=0;for(n?(a=n.length,s=r.concat(n,s)):a=0;a<s.length;){let l=s[a];if(l===10){if(!i){let f=a>0&&s[a-1]===13?a-1:a;yield r.toUtf8String(s,o,f),o=a+1}}else l===34&&(i=!i);a++}o<s.length?n=r.copy(s,o,s.length):n=void 0}n&&(yield r.toUtf8String(n,0,n.length))}var b=class{constructor(){this._reuse=!1}get reuse(){return this._reuse}set reuse(e){e&&!this.reusedValues&&(this.reusedValues=new Array(10)),this._reuse=e}withReuse(){return this.reuse=!0,this}splitLine(e){if(e==null)return this.lastSplitLength=0,[];let r=0,n=0,i=this._reuse?this.reusedValues:[],s=0;for(let o=0;o<e.length;o++){let l=e[o];if(l===","){if(r%2===0){let f=this.getValue(e,n,o,r);this._reuse?i[s++]=f:i.push(f),n=o+1,r=0}}else l==='"'&&r++}let a=this.getValue(e,n,e.length,r);return this._reuse?(i[s]=a,this.lastSplitLength=s+1):(i.push(a),this.lastSplitLength=i.length),i}getValue(e,r,n,i){return r===e.length?"":i===0?e.substring(r,n):i===2?e.substring(r+1,n-1):e.substring(r+1,n-1).replace(/""/gi,'"')}};var N=t=>t,S={boolean:t=>t===""?null:t==="true",unsignedLong:t=>t===""?null:+t,long:t=>t===""?null:+t,double(t){switch(t){case"":return null;case"+Inf":return Number.POSITIVE_INFINITY;case"-Inf":return Number.NEGATIVE_INFINITY;default:return+t}},string:N,base64Binary:N,duration:t=>t===""?null:t,"dateTime:RFC3339":t=>t===""?null:t},F=class{get(e){var n;let r=e[this.index];return(r===""||r===void 0)&&this.defaultValue&&(r=this.defaultValue),((n=S[this.dataType])!=null?n:N)(r)}},ce=Object.freeze({label:"",dataType:"",group:!1,defaultValue:"",index:Number.MAX_SAFE_INTEGER,get:()=>{}});function L(){return new F}function ct(t){var r,n;let e=new F;return e.label=String(t.label),e.dataType=t.dataType,e.group=!!t.group,e.defaultValue=(r=t.defaultValue)!=null?r:"",e.index=(n=t.index)!=null?n:0,e}var We=[404,408,425,429,500,502,503,504];function Ne(t){return We.includes(t)}var y=class t extends Error{constructor(e){super(e),this.name="IllegalArgumentError",Object.setPrototypeOf(this,t.prototype)}},g=class t extends Error{constructor(r,n,i,s,a,o,l){super();this.statusCode=r;this.statusMessage=n;this.body=i;this.contentType=a;if(Object.setPrototypeOf(this,t.prototype),this.headers=l,o)this.message=o;else if(i){if(a!=null&&a.startsWith("application/json"))try{this.json=JSON.parse(i),this.message=this.json.message,this.code=this.json.code}catch(f){}this.message||(this.message=`${r} ${n} : ${i}`)}else this.message=`${r} ${n}`;this.name="HttpError",this.setRetryAfter(s)}setRetryAfter(r){typeof r=="string"?/^[0-9]+$/.test(r)?this._retryAfter=parseInt(r):this._retryAfter=0:this._retryAfter=0}canRetry(){return Ne(this.statusCode)}retryAfter(){return this._retryAfter}},He=["ECONNRESET","ENOTFOUND","ESOCKETTIMEDOUT","ETIMEDOUT","ECONNREFUSED","EHOSTUNREACH","EPIPE"];function mt(t){if(t){if(typeof t.canRetry=="function")return!!t.canRetry();if(t.code&&He.includes(t.code))return!0}else return!1;return!1}function de(t,e){if(t){let r;return typeof t.retryAfter=="function"?t.retryAfter():(r=0,e&&e>0?r+Math.round(Math.random()*e):r)}else return 0}var v=class t extends Error{constructor(){super(),Object.setPrototypeOf(this,t.prototype),this.name="RequestTimedOutError",this.message="Request timed out"}canRetry(){return!0}retryAfter(){return 0}},x=class t extends Error{constructor(){super(),this.name="AbortError",Object.setPrototypeOf(this,t.prototype),this.message="Response aborted"}canRetry(){return!0}retryAfter(){return 0}};function xt(){S["dateTime:RFC3339"]=t=>t===""?null:new Date(Date.parse(t))}function Rt(){S["dateTime:RFC3339"]=t=>t===""?null:Date.parse(t)}function wt(){S["dateTime:RFC3339"]=t=>t===""?null:t}var H=class{constructor(e){e.forEach((r,n)=>r.index=n),this.columns=e}column(e,r=!0){for(let n=0;n<this.columns.length;n++){let i=this.columns[n];if(i.label===e)return i}if(r)throw new y(`Column ${e} not found!`);return ce}toObject(e){let r={};for(let n=0;n<this.columns.length&&n<e.length;n++){let i=this.columns[n];r[i.label]=i.get(e)}return r}get(e,r){return this.column(r,!1).get(e)}};function I(t){return new H(t)}function q(t){let e=new b().withReuse(),r,n=!0,i=0,s,a={error(o){t.error(o)},next(o){if(o==="")n=!0,r=void 0;else{let l=e.splitLine(o),f=e.lastSplitLength;if(n){if(!r){r=new Array(f);for(let u=0;u<f;u++)r[u]=L()}if(l[0].startsWith("#")){if(l[0]==="#datatype")for(let u=1;u<f;u++)r[u].dataType=l[u];else if(l[0]==="#default")for(let u=1;u<f;u++)r[u].defaultValue=l[u];else if(l[0]==="#group")for(let u=1;u<f;u++)r[u].group=l[u][0]==="t"}else{l[0]===""?(i=1,r=r.slice(1)):i=0;for(let u=i;u<f;u++)r[u-i].label=l[u];s=I(r),n=!1}}else return t.next(l.slice(i,f),s)}return!0},complete(){t.complete()}};return t.useCancellable&&(a.useCancellable=t.useCancellable.bind(t)),t.useResume&&(a.useResume=t.useResume.bind(t)),a}async function*me(t){let e=new b().withReuse(),r,n=!0,i=0,s;for await(let a of t)if(a==="")n=!0,r=void 0;else{let o=e.splitLine(a),l=e.lastSplitLength;if(n){if(!r){r=new Array(l);for(let f=0;f<l;f++)r[f]=L()}if(o[0].startsWith("#")){if(o[0]==="#datatype")for(let f=1;f<l;f++)r[f].dataType=o[f];else if(o[0]==="#default")for(let f=1;f<l;f++)r[f].defaultValue=o[f];else if(o[0]==="#group")for(let f=1;f<l;f++)r[f].group=o[f][0]==="t"}else{o[0]===""?(i=1,r=r.slice(1)):i=0;for(let f=i;f<l;f++)r[f-i].label=o[f];s=I(r),n=!1}}else yield{values:o.slice(i,l),tableMeta:s}}}function Mt(t,e){let r=!1,n=0,i=0;for(;i<t.length;){let s=t.charCodeAt(i);if(s===10){if(!r){let a=i>0&&t.charCodeAt(i-1)===13?i-1:i;e.next(t.substring(n,a)),n=i+1}}else s===34&&(r=!r);i++}n<i&&e.next(t.substring(n,i)),e.complete()}var he=typeof Symbol=="function"&&Symbol.observable||"@@observable";var j=class{constructor(e,r){this.isClosed=!1;try{r({next:n=>{e.next(n)},error:n=>{this.isClosed=!0,e.error(n)},complete:()=>{this.isClosed=!0,e.complete()},useCancellable:n=>{this.cancellable=n}})}catch(n){this.isClosed=!0,e.error(n)}}get closed(){return this.isClosed}unsubscribe(){var e;(e=this.cancellable)==null||e.cancel(),this.isClosed=!0}};function Q(){}function qe(t){let{next:e,error:r,complete:n}=t;return{next:e?e.bind(t):Q,error:r?r.bind(t):Q,complete:n?n.bind(t):Q}}var R=class{constructor(e,r){this.executor=e;this.decorator=r}subscribe(e,r,n){let i=qe(typeof e!="object"||e===null?{next:e,error:r,complete:n}:e);return new j(this.decorator(i),this.executor)}[he](){return this}};var ge={timeout:1e4},ye={retryJitter:200,minRetryDelay:5e3,maxRetryDelay:125e3,exponentialBase:5,randomRetry:!0},be={batchSize:1e3,maxBatchBytes:5e7,flushInterval:6e4,writeFailed:function(){},writeSuccess:function(){},writeRetrySkipped:function(){},maxRetries:5,maxRetryTime:18e4,maxBufferLines:32e3,retryJitter:200,minRetryDelay:5e3,maxRetryDelay:125e3,exponentialBase:2,gzipThreshold:1e3,randomRetry:!0};function J(t,e){return function(r){let n="",i=0,s=0;for(;s<r.length;){let a=t.indexOf(r[s]);a>=0&&(n+=r.substring(i,s),n+=e[a],i=s+1),s++}return i==0?r:(i<r.length&&(n+=r.substring(i,r.length)),n)}}function Qe(t,e){let r=J(t,e);return n=>'"'+r(n)+'"'}var w={measurement:J(`, 
\r	`,["\\,","\\ ","\\n","\\r","\\t"]),quoted:Qe('"\\',['\\"',"\\\\"]),tag:J(`, =
\r	`,["\\,","\\ ","\\=","\\n","\\r","\\t"])};var _="000000000",G=!1;function je(t){return G=t&&process&&typeof process.hrtime=="function"}je(!0);var xe,U,Re=Date.now(),X=0;function Y(){if(G){let t=process.hrtime(),e=Date.now();U?(t[0]=t[0]-U[0],t[1]=t[1]-U[1],t[1]<0&&(t[0]-=1,t[1]+=1e9),e=xe+t[0]*1e3+Math.floor(t[1]/1e6)):(U=t,xe=e);let r=String(t[1]%1e6);return String(e)+_.substr(0,6-r.length)+r}else{let t=Date.now();t!==Re?(Re=t,X=0):X++;let e=String(X);return String(t)+_.substr(0,6-e.length)+e}}function we(){if(G){let t=process.hrtime(),e=String(Math.trunc(t[1]/1e3)%1e3);return String(Date.now())+_.substr(0,3-e.length)+e}else return String(Date.now())+_.substr(0,3)}function Te(){return String(Date.now())}function Ce(){return String(Math.floor(Date.now()/1e3))}var Se={s:Ce,ms:Te,us:we,ns:Y,seconds:Ce,millis:Te,micros:we,nanos:Y},ve={s:t=>`${Math.floor(t.getTime()/1e3)}`,ms:t=>`${t.getTime()}`,us:t=>`${t.getTime()}000`,ns:t=>`${t.getTime()}000000`};function Oe(t){return t===void 0?Y():typeof t=="string"?t.length>0?t:void 0:t instanceof Date?`${t.getTime()}000000`:String(typeof t=="number"?Math.floor(t):t)}var Je={error(t,e){console.error("ERROR: "+t,e||"")},warn(t,e){console.warn("WARN: "+t,e||"")}},B=Je,h={error(t,e){B.error(t,e)},warn(t,e){B.warn(t,e)}};function yr(t){let e=B;return B=t,e}var $=Symbol("FLUX_VALUE"),m=class{constructor(e){this.fluxValue=e}toString(){return this.fluxValue}[$](){return this.fluxValue}};function Xe(t){return typeof t=="object"&&typeof t[$]=="function"}function O(t){if(t==null)return"";t=t.toString();let e,r=0;function n(){e===void 0&&(e=t.substring(0,r))}for(;r<t.length;r++){let i=t.charAt(r);switch(i){case"\r":n(),e+="\\r";break;case`
`:n(),e+="\\n";break;case"	":n(),e+="\\t";break;case'"':case"\\":n(),e=e+"\\"+i;break;case"$":if(r+1<t.length&&t.charAt(r+1)==="{"){n(),r++,e+="\\${";break}e!=null&&(e+=i);break;default:e!=null&&(e+=i)}}return e!==void 0?e:t}function xr(t){return new m(`"${O(t)}"`)}function Ee(t){let e=Number(t);if(!isFinite(e)){if(typeof t=="number")return`float(v: "${e}")`;throw new Error(`not a flux float: ${t}`)}let r=e.toString(),n=!1;for(let i of r)if(!(i>="0"&&i<="9"||i=="-")){if(i==="."){n=!0;continue}return`float(v: "${r}")`}return n?r:r+".0"}function Rr(t){return new m(Ee(t))}function De(t){let e=String(t),r=e.startsWith("-"),n=r?e.substring(1):e;if(n.length===0||n.length>19)throw new Error(`not a flux integer: ${e}`);for(let i of n)if(i<"0"||i>"9")throw new Error(`not a flux integer: ${e}`);if(n.length===19){if(r&&n>"9223372036854775808")throw new Error(`flux integer out of bounds: ${e}`);if(!r&&n>"9223372036854775807")throw new Error(`flux integer out of bounds: ${e}`)}return e}function wr(t){return new m(De(t))}function Ye(t){return`time(v: "${O(t)}")`}function Tr(t){return new m(Ye(t))}function Cr(t){return new m(`duration(v: "${O(t)}")`)}function Pe(t){return t instanceof RegExp?t.toString():new RegExp(t).toString()}function Sr(t){return new m(Pe(t))}function vr(t){return t==="true"||t==="false"?new m(t):new m((!!t).toString())}function Ae(t){return new m(String(t))}function K(t){if(t===void 0)return"";if(t===null)return"null";if(typeof t=="boolean")return t.toString();if(typeof t=="string")return`"${O(t)}"`;if(typeof t=="number")return Number.isSafeInteger(t)?De(t):Ee(t);if(typeof t=="object"){if(typeof t[$]=="function")return t[$]();if(t instanceof Date)return t.toISOString();if(t instanceof RegExp)return Pe(t);if(Array.isArray(t))return`[${t.map(K).join(",")}]`}else if(typeof t=="bigint")return`${t}.0`;return K(t.toString())}function Or(t,...e){if(t.length==1&&e.length===0)return Ae(t[0]);let r=new Array(t.length+e.length),n=0;for(let i=0;i<t.length;i++){let s=t[i];if(r[n++]=s,i<e.length){let a=e[i],o;if(s.endsWith('"')&&i+1<t.length&&t[i+1].startsWith('"'))o=O(a);else if(o=K(a),o===""&&!Xe(a))throw new Error(`Unsupported parameter literal '${a}' at index: ${i}, type: ${typeof a}`);r[n++]=o}else if(i<t.length-1)throw new Error("Too few parameters supplied!")}return Ae(r.join(""))}var Fe=class{constructor(e){this.tags={};this.fields={};e&&(this.name=e)}measurement(e){return this.name=e,this}tag(e,r){return this.tags[e]=r,this}booleanField(e,r){return this.fields[e]=r?"T":"F",this}intField(e,r){let n;if(typeof r=="number"?n=r:n=parseInt(String(r)),isNaN(n)||n<=-9223372036854776e3||n>=9223372036854776e3)throw new Error(`invalid integer value for field '${e}': '${r}'!`);return this.fields[e]=`${Math.floor(n)}i`,this}uintField(e,r){if(typeof r=="number"){if(isNaN(r)||r<0||r>Number.MAX_SAFE_INTEGER)throw new Error(`uint value for field '${e}' out of range: ${r}`);this.fields[e]=`${Math.floor(r)}u`}else{let n=String(r);for(let i=0;i<n.length;i++){let s=n.charCodeAt(i);if(s<48||s>57)throw new Error(`uint value has an unsupported character at pos ${i}: ${r}`)}if(n.length>20||n.length===20&&n.localeCompare("18446744073709551615")>0)throw new Error(`uint value for field '${e}' out of range: ${n}`);this.fields[e]=`${n}u`}return this}floatField(e,r){let n;if(typeof r=="number"?n=r:n=parseFloat(r),!isFinite(n))throw new Error(`invalid float value for field '${e}': ${r}`);return this.fields[e]=String(n),this}stringField(e,r){return r!=null&&(typeof r!="string"&&(r=String(r)),this.fields[e]=w.quoted(r)),this}timestamp(e){return this.time=e,this}toLineProtocol(e){if(!this.name)return;let r="";if(Object.keys(this.fields).sort().forEach(a=>{if(a){let o=this.fields[a];r.length>0&&(r+=","),r+=`${w.tag(a)}=${o}`}}),r.length===0)return;let n="",i=e&&e.defaultTags?{...e.defaultTags,...this.tags}:this.tags;Object.keys(i).sort().forEach(a=>{if(a){let o=i[a];o&&(n+=",",n+=`${w.tag(a)}=${w.tag(o)}`)}});let s=this.time;return e&&e.convertTime?s=e.convertTime(s):s=Oe(s),`${w.measurement(this.name)}${n} ${r}${s!==void 0?" "+s:""}`}toString(){let e=this.toLineProtocol(void 0);return e||`invalid point: ${JSON.stringify(this,void 0)}`}};var Z=class{constructor(e){this.options={...ye,...e},this.success()}nextDelay(e,r){let n=de(e);if(n&&n>0)return n+Math.round(Math.random()*this.options.retryJitter);if(r&&r>0){if(this.options.randomRetry){let s=Math.max(this.options.minRetryDelay,1),a=s*this.options.exponentialBase;for(let o=1;o<r;o++)if(s=a,a=a*this.options.exponentialBase,a>=this.options.maxRetryDelay){a=this.options.maxRetryDelay;break}return s+Math.round(Math.random()*(a-s)+Math.random()*this.options.retryJitter)}let i=Math.max(this.options.minRetryDelay,1);for(let s=1;s<r;s++)if(i=i*this.options.exponentialBase,i>=this.options.maxRetryDelay){i=this.options.maxRetryDelay;break}return i+Math.round(Math.random()*this.options.retryJitter)}else this.currentDelay?this.currentDelay=Math.min(Math.max(this.currentDelay*this.options.exponentialBase,1)+Math.round(Math.random()*this.options.retryJitter),this.options.maxRetryDelay):this.currentDelay=this.options.minRetryDelay+Math.round(Math.random()*this.options.retryJitter);return this.currentDelay}success(){this.currentDelay=void 0}};function Le(t){return new Z(t)}function Ge(t){let e,r=t,n=t;for(;n.next;)n.next.expires<r.expires&&(e=n,r=n.next),n=n.next;return[r,e]}var A=class{constructor(e,r,n=()=>{}){this.maxLines=e;this.retryLines=r;this.onShrink=n;this.size=0;this.closed=!1;this._timeoutHandle=void 0}addLines(e,r,n,i){if(this.closed||!e.length)return;let s=Date.now()+n;if(i<s&&(s=i),this.first&&this.size+e.length>this.maxLines){let f=this.size,u=f*.7;do{let[p,c]=Ge(this.first);this.size-=p.lines.length,c?c.next=p.next:(this.first=p.next,this.first&&this.scheduleRetry(this.first.retryTime-Date.now())),p.next=void 0,this.onShrink(p)}while(this.first&&this.size+e.length>u);h.error(`RetryBuffer: ${f-this.size} oldest lines removed to keep buffer size under the limit of ${this.maxLines} lines.`)}let a={lines:e,retryCount:r,retryTime:s,expires:i},o=this.first,l;for(;;){if(!o||o.retryTime>s){a.next=o,l?l.next=a:(this.first=a,this.scheduleRetry(s-Date.now()));break}l=o,o=o.next}this.size+=e.length}removeLines(){if(this.first){let e=this.first;return this.first=this.first.next,e.next=void 0,this.size-=e.lines.length,e}}scheduleRetry(e){this._timeoutHandle&&clearTimeout(this._timeoutHandle),this._timeoutHandle=setTimeout(()=>{let r=this.removeLines();r?this.retryLines(r.lines,r.retryCount,r.expires).catch(()=>{}).finally(()=>{this.first&&this.scheduleRetry(this.first.retryTime-Date.now())}):this._timeoutHandle=void 0},Math.max(e,0))}async flush(){let e;for(;e=this.removeLines();)await this.retryLines(e.lines,e.retryCount,e.expires)}close(){return this._timeoutHandle&&(clearTimeout(this._timeoutHandle),this._timeoutHandle=void 0),this.closed=!0,this.size}};function ee(t){let e=t.length;for(let r=0;r<t.length;r++){let n=t.charCodeAt(r);n<128||(n>=128&&n<=2047?e++:n>=2048&&n<=65535?n>=55296&&n<=57343?e++:e+=2:e+=3)}return e}var te=class{constructor(e,r,n,i){this.maxChunkRecords=e;this.maxBatchBytes=r;this.flushFn=n;this.scheduleSend=i;this.length=0;this.bytes=-1;this.lines=new Array(e)}add(e){let r=ee(e);this.length===0?this.scheduleSend():this.bytes+r+1>=this.maxBatchBytes&&this.flush().catch(n=>{}),this.lines[this.length]=e,this.length++,this.bytes+=r+1,(this.length>=this.maxChunkRecords||this.bytes>=this.maxBatchBytes)&&this.flush().catch(n=>{})}flush(){let e=this.reset();return e.length>0?this.flushFn(e):Promise.resolve()}reset(){let e=this.lines.slice(0,this.length);return this.length=0,this.bytes=-1,e}},E=class{constructor(e,r,n,i,s){this.transport=e;this.closed=!1;this._timeoutHandle=void 0;this.path=`/api/v2/write?org=${encodeURIComponent(r)}&bucket=${encodeURIComponent(n)}&precision=${i}`,s!=null&&s.consistency&&(this.path+=`&consistency=${encodeURIComponent(s.consistency)}`),this.writeOptions={...be,...s},this.currentTime=Se[i],this.dateToProtocolTimestamp=ve[i],this.writeOptions.defaultTags&&this.useDefaultTags(this.writeOptions.defaultTags),this.sendOptions={method:"POST",headers:{"content-type":"text/plain; charset=utf-8",...s==null?void 0:s.headers},gzipThreshold:this.writeOptions.gzipThreshold};let a=()=>{this.writeOptions.flushInterval>0&&(this._clearFlushTimeout(),this.closed||(this._timeoutHandle=setTimeout(()=>this.sendBatch(this.writeBuffer.reset(),this.writeOptions.maxRetries).catch(o=>{}),this.writeOptions.flushInterval)))};this.writeBuffer=new te(this.writeOptions.batchSize,this.writeOptions.maxBatchBytes,o=>(this._clearFlushTimeout(),this.sendBatch(o,this.writeOptions.maxRetries)),a),this.sendBatch=this.sendBatch.bind(this),this.retryStrategy=Le(this.writeOptions),this.retryBuffer=new A(this.writeOptions.maxBufferLines,this.sendBatch,this.writeOptions.writeRetrySkipped)}sendBatch(e,r,n=Date.now()+this.writeOptions.maxRetryTime){let i=this,s=i.writeOptions.maxRetries+1-r;if(!this.closed&&e.length>0){if(n<=Date.now()){let a=new Error("Max retry time exceeded."),o=i.writeOptions.writeFailed.call(i,a,e,s,n);return o||(h.error(`Write to InfluxDB failed (attempt: ${s}).`,a),Promise.reject(a))}return new Promise((a,o)=>{let l,f,u={responseStarted(p,c){l=c,f=p},error(p){let c=i.writeOptions.writeFailed.call(i,p,e,s,n);if(c){c.then(a,o);return}if(p instanceof g&&p.json&&typeof p.json.error=="string"&&p.json.error.includes("hinted handoff queue not empty")){h.warn("Write to InfluxDB returns: "+p.json.error),l=204,u.complete();return}if(!i.closed&&r>0&&(!(p instanceof g)||p.statusCode>=429)){h.warn(`Write to InfluxDB failed (attempt: ${s}).`,p),i.retryBuffer.addLines(e,r-1,i.retryStrategy.nextDelay(p,s),n),o(p);return}h.error("Write to InfluxDB failed.",p),o(p)},complete(){if(l==204||l==201||l==null)i.writeOptions.writeSuccess.call(i,e),i.retryStrategy.success(),a();else{let p=`204 HTTP response status code expected, but ${l} returned`,c=new g(l,p,void 0,"0",void 0,void 0,f);c.message=p,u.error(c)}}};this.transport.send(this.path,e.join(`
`),this.sendOptions,u)})}else return Promise.resolve()}_clearFlushTimeout(){this._timeoutHandle!==void 0&&(clearTimeout(this._timeoutHandle),this._timeoutHandle=void 0)}writeRecord(e){if(this.closed)throw new Error("writeApi: already closed!");this.writeBuffer.add(e)}writeRecords(e){if(this.closed)throw new Error("writeApi: already closed!");for(let r=0;r<e.length;r++)this.writeBuffer.add(e[r])}writePoint(e){if(this.closed)throw new Error("writeApi: already closed!");let r=e.toLineProtocol(this);r&&this.writeBuffer.add(r)}writePoints(e){if(this.closed)throw new Error("writeApi: already closed!");for(let r=0;r<e.length;r++){let n=e[r].toLineProtocol(this);n&&this.writeBuffer.add(n)}}async flush(e){if(await this.writeBuffer.flush(),e)return await this.retryBuffer.flush()}close(){return this.writeBuffer.flush().finally(()=>{let r=this.retryBuffer.close();r&&h.error(`Retry buffer closed with ${r} items that were not written to InfluxDB!`,null),this.closed=!0})}dispose(){return this._clearFlushTimeout(),this.closed=!0,this.retryBuffer.close()+this.writeBuffer.length}useDefaultTags(e){return this.defaultTags=e,this}convertTime(e){return e===void 0?this.currentTime():typeof e=="string"?e.length>0?e:void 0:e instanceof Date?this.dateToProtocolTimestamp(e):String(typeof e=="number"?Math.floor(e):e)}};import{parse as Be}from"url";import*as $e from"http";import*as Me from"https";import{Buffer as ne}from"buffer";import{Buffer as Ie}from"buffer";var Ke={concat(t,e){return Ie.concat([t,e])},toUtf8String(t,e,r){return t.toString("utf-8",e,r)},copy(t,e,r){let n=Ie.allocUnsafe(r-e);return t.copy(n,0,e,r),n}},Ue=Ke;import M from"zlib";function re(t={}){let e=0,r={next:n=>{if(e===0&&t.next&&n!==null&&n!==void 0)return t.next(n)},error:n=>{e===0&&(e=1,t.error&&t.error(n))},complete:()=>{e===0&&(e=2,t.complete&&t.complete())},responseStarted:(n,i)=>{t.responseStarted&&t.responseStarted(n,i)}};return t.useCancellable&&(r.useCancellable=t.useCancellable.bind(t)),t.useResume&&(r.useResume=t.useResume.bind(t)),r}var _e="1.35.0";import{pipeline as Ze}from"stream";var et={flush:M.constants.Z_SYNC_FLUSH,finishFlush:M.constants.Z_SYNC_FLUSH},tt=ne.allocUnsafe(0),ie=class{constructor(){this.cancelled=!1}cancel(){this.cancelled=!0,this.resume&&(this.resume(),this.resume=void 0)}isCancelled(){return this.cancelled}},T,se=class{constructor(e){this.chunkCombiner=Ue;fe(this,T);var l,f,u,p,c,d,D;let{url:r,proxyUrl:n,token:i,transportOptions:s,...a}=e,o=Be(n||r);if(pe(this,T,i),this.defaultOptions={...ge,...a,...s,port:o.port,protocol:o.protocol,hostname:o.hostname},this.contextPath=n?r:(l=o.path)!=null?l:"",this.contextPath.endsWith("/")&&(this.contextPath=this.contextPath.substring(0,this.contextPath.length-1)),Object.keys(this.defaultOptions).forEach(ae=>this.defaultOptions[ae]===void 0&&delete this.defaultOptions[ae]),this.contextPath.endsWith("/api/v2")&&(h.warn(`Please remove '/api/v2' context path from InfluxDB base url, using ${o.protocol}//${o.hostname}:${o.port} !`),this.contextPath=""),o.protocol==="http:")this.requestApi=(p=(u=(f=this.defaultOptions["follow-redirects"])==null?void 0:f.http)==null?void 0:u.request)!=null?p:$e.request;else if(o.protocol==="https:")this.requestApi=(D=(d=(c=this.defaultOptions["follow-redirects"])==null?void 0:c.https)==null?void 0:d.request)!=null?D:Me.request;else throw new Error(`Unsupported protocol "${o.protocol} in URL: "${e.url}"`);this.headers={"User-Agent":`influxdb-client-js/${_e}`,...e.headers},n&&(this.headers.Host=Be(r).host)}send(e,r,n,i){let s=new ie;i&&i.useCancellable&&i.useCancellable(s),this.createRequestMessage(e,r,n,a=>{this._request(a,s,i)},a=>(i==null?void 0:i.error)&&i.error(a))}request(e,r,n,i){r?typeof r!="string"&&(r=JSON.stringify(r)):r="";let s=tt,a,o;return new Promise((l,f)=>{this.send(e,r,n,{responseStarted(u,p){i&&i(u,p),a=String(u["content-type"]),o=p},next:u=>{s=ne.concat([s,u])},complete:()=>{var p,c;let u=(c=(p=n.headers)==null?void 0:p.accept)!=null?c:a;try{o===204&&l(void 0),u.includes("json")?s.length?l(JSON.parse(s.toString("utf8"))):l(void 0):u.includes("text")||u.startsWith("application/csv")?l(s.toString("utf8")):l(s)}catch(d){f(d)}},error:u=>{f(u)}})})}async*iterate(e,r,n){var u;let i,s;function a(p){i=p,s(p)}let o=await new Promise((p,c)=>{s=c,this.createRequestMessage(e,r,n,p,a)});(u=o.signal)!=null&&u.addEventListener&&o.signal.addEventListener("abort",()=>{a(new x)});let l=await new Promise((p,c)=>{s=c;let d=this.requestApi(o,p);d.on("timeout",()=>a(new v)),d.on("error",a),d.write(o.body),d.end()}),f=await new Promise((p,c)=>{s=c,this._prepareResponse(l,p,a)});for await(let p of f){if(i)throw i;yield p}}createRequestMessage(e,r,n,i,s){let a=ne.from(r,"utf-8"),o={"content-type":"application/json; charset=utf-8",...this.headers};k(this,T)&&(o.authorization="Token "+k(this,T));let l={...this.defaultOptions,path:this.contextPath+e,method:n.method,headers:{...o,...n.headers}};if(n.signal&&(l.signal=n.signal),n.gzipThreshold!==void 0&&n.gzipThreshold<a.length){M.gzip(a,(f,u)=>{if(f)return s(f);l.headers["content-encoding"]="gzip",l.body=u,i(l)});return}l.body=a,l.headers["content-length"]=l.body.length,i(l)}_prepareResponse(e,r,n){var o;e.on("aborted",()=>{n(new x)}),e.on("error",n);let i=(o=e.statusCode)!=null?o:600,s=e.headers["content-encoding"],a;if(s==="gzip"?(a=M.createGunzip(et),a=Ze(e,a,l=>l&&n(l))):a=e,i>=300){let l="",f=String(e.headers["content-type"]).startsWith("application/json");a.on("data",u=>{l+=u.toString(),!f&&l.length>1e3&&(l=l.slice(0,1e3),e.resume())}),a.on("end",()=>{l===""&&e.headers["x-influxdb-error"]&&(l=e.headers["x-influxdb-error"].toString()),n(new g(i,e.statusMessage,l,e.headers["retry-after"],e.headers["content-type"],void 0,e.headers))})}else r(a)}_request(e,r,n){var a;let i=re(n);if(r.isCancelled()){i.complete();return}(a=e.signal)!=null&&a.addEventListener&&e.signal.addEventListener("abort",()=>{i.error(new x)});let s=this.requestApi(e,o=>{if(r.isCancelled()){o.resume(),i.complete();return}i.responseStarted(o.headers,o.statusCode),this._prepareResponse(o,l=>{l.on("data",f=>{if(r.isCancelled())o.resume();else if(i.next(f)===!1){if(!i.useResume){i.error(new Error("Unable to pause, useResume is not configured!")),o.resume();return}o.pause();let u=()=>{o.resume()};r.resume=u,i.useResume(u)}}),l.on("end",i.complete)},i.error)});typeof s.setTimeout=="function"&&e.timeout&&s.setTimeout(e.timeout),s.on("timeout",()=>{i.error(new v)}),s.on("error",o=>{i.error(o)}),e.body&&s.write(e.body),s.end()}};T=new WeakMap;var ze=se;var Ve={header:!0,delimiter:",",quoteChar:'"',commentPrefix:"#",annotations:["datatype","group","default"]},oe=class t{constructor(e,r,n){this.transport=e;this.createCSVResponse=r;this.options=typeof n=="string"?{org:n}:n}with(e){return new t(this.transport,this.createCSVResponse,{...this.options,...e})}response(e){let{org:r,type:n,gzip:i,headers:s}=this.options,a=`/api/v2/query?org=${encodeURIComponent(r)}`,o=JSON.stringify(this.decorateRequest({query:e.toString(),dialect:Ve,type:n})),l={method:"POST",headers:{"content-type":"application/json; encoding=utf-8","accept-encoding":i?"gzip":"identity",...s}};return this.createCSVResponse(f=>this.transport.send(a,o,l,f),()=>this.transport.iterate(a,o,l))}iterateLines(e){return this.response(e).iterateLines()}iterateRows(e){return this.response(e).iterateRows()}lines(e){return this.response(e).lines()}rows(e){return this.response(e).rows()}queryLines(e,r){return this.response(e).consumeLines(r)}queryRows(e,r){return this.response(e).consumeRows(r)}collectRows(e,r){return this.response(e).collectRows(r)}collectLines(e){return this.response(e).collectLines()}queryRaw(e){let{org:r,type:n,gzip:i,headers:s}=this.options;return this.transport.request(`/api/v2/query?org=${encodeURIComponent(r)}`,JSON.stringify(this.decorateRequest({query:e.toString(),dialect:Ve,type:n})),{method:"POST",headers:{accept:"text/csv","accept-encoding":i?"gzip":"identity","content-type":"application/json; encoding=utf-8",...s}})}decorateRequest(e){var r;return typeof this.options.now=="function"&&(e.now=this.options.now()),e.type=(r=this.options.type)!=null?r:"flux",e}},ke=oe;function rt(t,e){return e.toObject(t)}var z=class{constructor(e,r,n){this.executor=e;this.iterableResultExecutor=r;this.chunkCombiner=n}iterateLines(){return W(this.iterableResultExecutor())}iterateRows(){return me(W(this.iterableResultExecutor()))}lines(){return new R(this.executor,e=>C(e,this.chunkCombiner))}rows(){return new R(this.executor,e=>C(q({next(r,n){e.next({values:r,tableMeta:n})},error(r){e.error(r)},complete(){e.complete()}}),this.chunkCombiner))}consumeLines(e){this.executor(C(e,this.chunkCombiner))}consumeRows(e){this.executor(C(q(e),this.chunkCombiner))}collectRows(e=rt){let r=[];return new Promise((n,i)=>{this.consumeRows({next(s,a){let o=e.call(this,s,a);o!==void 0&&r.push(o)},error(s){i(s)},complete(){n(r)}})})}collectLines(){let e=[];return new Promise((r,n)=>{this.consumeLines({next(i){e.push(i)},error(i){n(i)},complete(){r(e)}})})}};var V=class{constructor(e){var n;if(typeof e=="string")this._options={url:e};else if(e!==null&&typeof e=="object")this._options=Object.assign({},e);else throw new y("No url or configuration specified!");let r=this._options.url;if(typeof r!="string")throw new y("No url specified!");r.endsWith("/")&&(this._options.url=r.substring(0,r.length-1)),this.transport=(n=this._options.transport)!=null?n:new ze(this._options),delete this._options.token,this.processCSVResponse=(i,s)=>new z(i,s,this.transport.chunkCombiner)}getWriteApi(e,r,n="ns",i){return new E(this.transport,e,r,n,i!=null?i:this._options.writeOptions)}getQueryApi(e){return new ke(this.transport,this.processCSVResponse,e)}};export{x as AbortError,ge as DEFAULT_ConnectionOptions,ye as DEFAULT_RetryDelayStrategyOptions,be as DEFAULT_WriteOptions,$ as FLUX_VALUE,g as HttpError,y as IllegalArgumentError,V as InfluxDB,b as LineSplitter,h as Log,Fe as Point,v as RequestTimedOutError,ce as UNKNOWN_COLUMN,mt as canRetryHttpCall,C as chunksToLines,W as chunksToLinesIterable,Je as consoleLogger,Oe as convertTimeToNanos,ct as createFluxTableColumn,I as createFluxTableMetaData,P as createTextDecoderCombiner,Se as currentTime,ve as dateToProtocolTimestamp,w as escape,Or as flux,vr as fluxBool,Tr as fluxDateTime,Cr as fluxDuration,Ae as fluxExpression,Rr as fluxFloat,wr as fluxInteger,Sr as fluxRegExp,xr as fluxString,de as getRetryDelay,Ne as isStatusCodeRetriable,me as linesToRowsIterable,q as linesToTables,L as newFluxTableColumn,Ee as sanitizeFloat,De as sanitizeInteger,xt as serializeDateTimeAsDate,Rt as serializeDateTimeAsNumber,wt as serializeDateTimeAsString,yr as setLogger,Mt as stringToLines,he as symbolObservable,K as toFluxValue,S as typeSerializers,je as useProcessHrtime};
//# sourceMappingURL=index.mjs.map
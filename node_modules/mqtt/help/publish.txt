Usage: mqtt publish [opts] topic [message]

Available options:

  -h/--hostname HOST    the broker host
  -p/--port PORT        the broker port
  -i/--client-id ID     the client id
  -q/--qos 0/1/2        the QoS of the message
  -t/--topic TOPIC      the message topic
  -m/--message MSG      the message body
  -r/--retain           send a retained message
  -s/--stdin            read the message body from stdin
  -M/--multiline        read lines from stdin as multiple messages 
  -u/--username USER    the username
  -P/--password PASS    the password
  -C/--protocol PROTO   the protocol to use, 'mqtt',
                        'mqtts', 'ws' or 'wss'
  --key PATH            path to the key file
  --cert PATH           path to the cert file
  --ca PATH             path to the ca certificate
  --insecure            do not verify the server certificate
  --will-topic TOPIC    the will topic
  --will-payload BODY   the will message
  --will-qos 0/1/2      the will qos
  --will-retain         send a will retained message 
  -H/--help             show this

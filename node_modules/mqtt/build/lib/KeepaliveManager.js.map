{"version": 3, "file": "KeepaliveManager.js", "sourceRoot": "", "sources": ["../../src/lib/KeepaliveManager.ts"], "names": [], "mappings": ";;;;;AACA,4DAAkD;AAGlD,MAAqB,gBAAgB;IAkBpC,IAAI,yBAAyB;QAC5B,OAAO,IAAI,CAAC,0BAA0B,CAAA;IACvC,CAAC;IAGD,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC,cAAc,CAAA;IAC3B,CAAC;IAED,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,UAAU,CAAA;IACvB,CAAC;IAED,YAAY,MAAkB,EAAE,OAA6B;QAxBrD,cAAS,GAAG,KAAK,CAAA;QAyBxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,KAAK;YACT,OAAO,OAAO,KAAK,QAAQ;gBAC3B,KAAK,IAAI,OAAO;gBAChB,OAAO,IAAI,OAAO;gBACjB,CAAC,CAAC,OAAO;gBACT,CAAC,CAAC,IAAA,mBAAQ,EAAC,OAAO,CAAC,CAAA;QACrB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAC5C,CAAC;IAEO,KAAK;QACZ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACpB,CAAC;IACF,CAAC;IAGD,YAAY,CAAC,KAAa;QAEzB,KAAK,IAAI,IAAI,CAAA;QAEb,IAEC,KAAK,CAAC,KAAK,CAAC;YACZ,KAAK,IAAI,CAAC;YACV,KAAK,GAAG,UAAU,EACjB,CAAC;YACF,MAAM,IAAI,KAAK,CACd,kFAAkF,KAAK,EAAE,CACzF,CAAA;QACF,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QAEvB,IAAI,CAAC,UAAU,EAAE,CAAA;QAEjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,sCAAsC,KAAK,IAAI,CAAC,CAAA;IACpE,CAAC;IAED,OAAO;QACN,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;IACtB,CAAC;IAED,UAAU;QACT,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAM;QACP,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAA;QACZ,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;QAGhB,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,CAAA;QAEzD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAA;QAC/D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;QAEpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YAElC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpB,OAAM;YACP,CAAC;YAED,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA;YAGjB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;YACvB,CAAC;iBAAM,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAA;YACjC,CAAC;QACF,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;IACxB,CAAC;CACD;AA3GD,mCA2GC"}
import MqttClient, { type IClientOptions } from '../client';
declare function connect(brokerUrl: string): MqttClient;
declare function connect(opts: IClientOptions): MqttClient;
declare function connect(brokerUrl: string, opts?: IClientOptions): MqttClient;
declare function connectAsync(brokerUrl: string): Promise<MqttClient>;
declare function connectAsync(opts: IClientOptions): Promise<MqttClient>;
declare function connectAsync(brokerUrl: string, opts?: IClientOptions): Promise<MqttClient>;
declare function connectAsync(brokerUrl: string, opts: IClientOptions, allowRetries: boolean): Promise<MqttClient>;
export default connect;
export { connectAsync };

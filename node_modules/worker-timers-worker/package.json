{"author": "<PERSON>", "bugs": {"url": "https://github.com/chrisguttandin/worker-timers-worker/issues"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "contributors": [{"email": "<EMAIL>", "name": "K<PERSON>sing"}], "dependencies": {"@babel/runtime": "^7.24.5", "tslib": "^2.6.2"}, "description": "The worker which is used by the worker-timers package.", "devDependencies": {"@babel/core": "^7.24.5", "@babel/plugin-external-helpers": "^7.24.1", "@babel/plugin-transform-runtime": "^7.24.3", "@babel/preset-env": "^7.24.5", "@commitlint/cli": "^19.3.0", "@commitlint/config-angular": "^19.3.0", "@rollup/plugin-babel": "^6.0.4", "chai": "^4.3.10", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-holy-grail": "^59.0.8", "grunt": "^1.6.1", "grunt-cli": "^1.4.3", "grunt-sh": "^0.2.1", "husky": "^8.0.3", "karma": "^6.4.3", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.2.0", "karma-cli": "^2.0.0", "karma-firefox-launcher": "^2.1.3", "karma-mocha": "^2.0.1", "karma-mocha-webworker": "^1.3.0", "karma-sinon-chai": "^2.0.2", "karma-webkit-launcher": "^2.4.0", "karma-webpack": "^5.0.1", "lint-staged": "^15.2.2", "load-grunt-config": "^4.0.1", "memory-fs": "^0.5.0", "mocha": "^10.4.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.17.2", "sinon": "^17.0.1", "sinon-chai": "^3.7.0", "ts-loader": "^9.5.1", "tsconfig-holy-grail": "^15.0.1", "tslint": "^6.1.3", "tslint-config-holy-grail": "^56.0.1", "typescript": "^5.4.5", "webpack": "^5.91.0"}, "files": ["build/es2019/", "build/es5/", "src/"], "homepage": "https://github.com/chrisguttandin/worker-timers-worker", "license": "MIT", "main": "build/es5/bundle.js", "module": "build/es2019/module.js", "name": "worker-timers-worker", "repository": {"type": "git", "url": "https://github.com/chrisguttandin/worker-timers-worker.git"}, "scripts": {"build": "rimraf build/* && tsc --project src/tsconfig.json && rollup --config config/rollup/bundle.mjs", "lint": "npm run lint:config && npm run lint:src && npm run lint:test", "lint:config": "eslint --config config/eslint/config.json --ext .js --report-unused-disable-directives config/", "lint:src": "tslint --config config/tslint/src.json --project src/tsconfig.json src/*.ts src/**/*.ts", "lint:test": "eslint --config config/eslint/test.json --ext .js --report-unused-disable-directives test/", "prepare": "husky install", "prepublishOnly": "npm run build", "test": "grunt lint && grunt test"}, "types": "build/es2019/module.d.ts", "version": "7.0.71"}
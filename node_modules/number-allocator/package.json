{"name": "number-allocator", "version": "1.0.14", "description": "A library for the unique number allocator", "main": "index.js", "types": "types/index.d.ts", "directories": {"lib": "lib", "test": "test"}, "scripts": {"test": "node_modules/.bin/nyc --reporter=lcov --reporter=text ./node_modules/mocha/bin/_mocha", "pretest": "standard ./*.js ./lib/**/*.js ./test/*.js | snazzy", "codecov": "codecov", "browser-build": "rimraf dist/ && mkdirp dist/ && browserify index.js --standalone number-allocator > dist/number-allocator.js && uglifyjs dist/number-allocator.js --compress --mangle --output dist/number-allocator.min.js", "typescript-compile-test": "tsc -p test/typescript/tsconfig.json", "typescript-compile-execute": "node test/typescript/*.js", "typescript-test": "npm run typescript-compile-test && npm run typescript-compile-execute", "test-web": "karma start", "generate-docs": "node_modules/.bin/jsdoc -d docs lib"}, "repository": {"type": "git", "url": "git+https://github.com/redboltz/number-allocator.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "keywords": ["unique", "number", "id", "value", "allocator"], "bugs": {"url": "https://github.com/redboltz/number-allocator/issues"}, "homepage": "https://github.com/redboltz/number-allocator#readme", "dependencies": {"debug": "^4.3.1", "js-sdsl": "4.3.0"}, "devDependencies": {"airtap": "^4.0.1", "browserify": "^17.0.0", "chai": "^4.2.0", "codecov": "^3.8.1", "eslint": "^7.18.0", "eslint-config-standard": "16.0.2", "jsdoc": "^3.6.6", "karma": "^6.0.3", "karma-browserify": "^8.0.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "mocha": "^10.2.0", "nyc": "^15.1.0", "snazzy": "^9.0.0", "standard": "^16.0.3", "tslint": "^5.11.0", "tslint-config-standard": "^9.0.0", "typescript": "^4.1.3", "uglify-es": "^3.3.9"}, "standard": {"env": ["mocha"]}}
## 1.0.14
- Fixed README.md version.

## 1.0.13
- **Important** Fixed invalid free operator.
- Updated js-sdsl.

## 1.0.12
- Updated js-sdsl. updateKeyByIterator() is used.

## 1.0.11
- Updated js-sdsl. Bidirectional iterator is used.

## 1.0.10
- Fixed TypeScript number type

## 1.0.9
- Migrated from collections.js to js-sdsl to remove intrinsic library extention

## 1.0.8
- Updated collections.js to solve https://github.com/montagejs/collections/issues/241

## 1.0.7
- Fixed codecov badge on README.md.

## 1.0.6
- Fixed document.
- Added keywords.

## 1.0.5
- Added debug logs.
- Improved free() with vacant value behavior.

## 1.0.4
- Fixed module export point again. `module.exports.NumberAllocator = NumberAllocator`

## 1.0.3
- Fixed module export point.

## 1.0.2
- Fixed npm version.

## 1.0.1
- Fixed .gitignore.

## 1.0.0
- Initial version.

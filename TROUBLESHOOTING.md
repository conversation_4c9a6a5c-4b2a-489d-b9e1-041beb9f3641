# Troubleshooting Guide

## Common Issues and Solutions

### 🐳 Docker Issues

#### Docker daemon not running
```bash
# Error: Cannot connect to the Docker daemon
sudo systemctl start docker
# or on macOS: open Docker Desktop
```

#### Port conflicts
```bash
# Error: Port already in use
docker compose down
sudo lsof -i :1883  # Check MQTT port
sudo lsof -i :8086  # Check InfluxDB port
sudo lsof -i :3000  # Check Grafana port
```

#### Container startup issues
```bash
# Check container logs
docker compose logs influxdb
docker compose logs mosquitto
docker compose logs grafana

# Restart specific service
docker compose restart influxdb
```

### 📡 MQTT Connection Issues

#### Broker connection failed
```bash
# Test MQTT broker manually
mosquitto_pub -h localhost -t test -m "hello"
mosquitto_sub -h localhost -t test

# Check if broker is running
docker compose ps mosquitto
```

#### Authentication errors
- Check MQTT credentials in `.env` file
- Verify mosquitto.conf allows anonymous connections
- Test with MQTT client tools

### 🗄️ InfluxDB Issues

#### Connection timeout
```bash
# Check InfluxDB health
curl http://localhost:8086/health

# Verify token and organization
curl -H "Authorization: Token my-super-secret-auth-token" \
     http://localhost:8086/api/v2/buckets?org=iot-org
```

#### Bucket not found
- Verify bucket name in `.env` matches Docker setup
- Check InfluxDB initialization logs
- Recreate bucket if necessary

#### Query errors
- Verify Flux query syntax
- Check field and tag names
- Ensure data exists in the time range

### 📊 Grafana Issues

#### Data source connection failed
1. Go to Configuration → Data Sources
2. Test InfluxDB connection
3. Verify URL: `http://influxdb:8086`
4. Check token and organization settings

#### No data in panels
- Verify time range selection
- Check query syntax
- Ensure devices are sending data
- Review InfluxDB data with CLI

#### Dashboard import errors
- Check JSON syntax
- Verify data source UID matches
- Update panel queries if needed

### 🔧 Application Issues

#### Module not found errors
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Permission denied
```bash
# Fix script permissions
chmod +x quick-start.sh
chmod +x test-simulator.js
```

#### Memory issues
- Reduce number of simulated devices
- Increase Docker memory limits
- Monitor system resources

## 🔍 Debugging Commands

### Check system status
```bash
# All services
docker compose ps

# System resources
docker stats

# Network connectivity
docker compose exec mosquitto mosquitto_pub -t test -m "hello"
```

### View logs
```bash
# All services
docker compose logs -f

# Specific service
docker compose logs -f influxdb

# Application logs
npm run simulator  # Check console output
npm run collector  # Check console output
```

### Test components
```bash
# Test device simulation
npm test

# Test MQTT connectivity
node -e "
const mqtt = require('mqtt');
const client = mqtt.connect('mqtt://localhost:1883');
client.on('connect', () => {
  console.log('MQTT connected');
  client.end();
});
"

# Test InfluxDB connectivity
curl -v http://localhost:8086/health
```

## 🚨 Emergency Recovery

### Complete reset
```bash
# Stop everything
docker compose down -v

# Clean Docker system
docker system prune -f

# Remove all data
rm -rf .env

# Start fresh
cp .env.example .env
docker compose up -d
npm run setup
```

### Data recovery
```bash
# Backup InfluxDB data
docker compose exec influxdb influx backup /tmp/backup

# Restore InfluxDB data
docker compose exec influxdb influx restore /tmp/backup
```

## 📞 Getting Help

### Log Collection
When reporting issues, include:
1. Docker compose logs: `docker compose logs > logs.txt`
2. Application output
3. System information: `docker version`, `node --version`
4. Configuration files (without sensitive data)

### Useful Commands
```bash
# System information
uname -a
docker --version
node --version
npm --version

# Network diagnostics
netstat -tulpn | grep -E '(1883|8086|3000)'
ping localhost

# Disk space
df -h
docker system df
```

### Community Resources
- GitHub Issues: Report bugs and feature requests
- Documentation: Check README and setup guides
- Examples: Review sample configurations
- Stack Overflow: Search for similar issues

## 🎯 Performance Optimization

### Reduce resource usage
```javascript
// In .env file
DEVICE_COUNT=5          # Reduce from 10
SIMULATION_INTERVAL=10000  # Increase from 5000ms
ENABLE_DEVICE_FAILURES=false  # Disable for testing
```

### Database optimization
- Configure InfluxDB retention policies
- Use appropriate aggregation windows
- Limit query time ranges

### Network optimization
- Use local MQTT broker
- Reduce message frequency
- Implement message batching

## 🔐 Security Considerations

### Production deployment
- Change default passwords
- Enable MQTT authentication
- Use TLS/SSL certificates
- Restrict network access
- Regular security updates

### Data protection
- Encrypt sensitive data
- Implement access controls
- Regular backups
- Monitor access logs

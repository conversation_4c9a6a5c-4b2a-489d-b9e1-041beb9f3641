#!/bin/bash

# IoT Device Simulator Quick Start Script

echo "🚀 IoT Device Simulator Quick Start"
echo "=================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    echo "   sudo systemctl start docker"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️  Creating environment configuration..."
    cp .env.example .env
fi

# Start infrastructure services
echo "🐳 Starting infrastructure services..."
docker compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
echo "🔍 Checking service status..."
docker compose ps

# Setup InfluxDB
echo "🗄️  Setting up InfluxDB..."
npm run setup

echo ""
echo "✅ Setup complete! You can now:"
echo ""
echo "1. Start the device simulator:"
echo "   npm run simulator"
echo ""
echo "2. Start the data collector (in another terminal):"
echo "   npm run collector"
echo ""
echo "3. Or start both together:"
echo "   npm start"
echo ""
echo "4. Access services:"
echo "   📊 Grafana: http://localhost:3000 (admin/admin123)"
echo "   🗄️  InfluxDB: http://localhost:8086 (admin/password123)"
echo ""
echo "5. Stop services when done:"
echo "   docker compose down"
echo ""
echo "Happy monitoring! 🎉"

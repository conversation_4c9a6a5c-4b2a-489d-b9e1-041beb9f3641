{"name": "iot-device-simulator", "version": "1.0.0", "description": "IoT Device Simulator with MQTT and InfluxDB for Grafana learning", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "simulator": "node src/simulator.js", "collector": "node src/collector.js", "setup": "node src/setup.js", "test": "node test-simulator.js", "quick-start": "./quick-start.sh", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "docker:logs": "docker compose logs -f", "clean": "docker compose down -v && docker system prune -f"}, "keywords": ["iot", "mqtt", "influxdb", "grafana", "simulator", "monitoring"], "author": "Your Name", "license": "MIT", "dependencies": {"mqtt": "^5.3.4", "@influxdata/influxdb-client": "^1.33.2", "dotenv": "^16.3.1", "uuid": "^9.0.1", "chalk": "^5.3.0"}, "devDependencies": {"nodemon": "^3.0.2"}}
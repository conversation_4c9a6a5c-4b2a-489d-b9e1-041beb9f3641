# IoT Device Simulator

A comprehensive IoT device simulator using Node.js, MQTT, and InfluxDB, designed for learning Grafana monitoring and visualization.

## 🎯 Project Overview

This project simulates various IoT devices (temperature sensors, motion detectors, etc.) that publish data via MQTT to InfluxDB, which can then be visualized in Grafana dashboards.

## 📊 Metrics Tracked

- **Sensor Readings**: Temperature, humidity, motion, light levels
- **Device Health**: Battery levels, uptime/downtime, connection status
- **System Metrics**: Message delivery rates, error rates, device count
- **Alerts**: Device failures, abnormal readings, connectivity issues

## 🏗️ Architecture

```
IoT Devices (Simulated) → MQTT Broker → Data Collector → InfluxDB → Grafana
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Docker & Docker Compose (for MQTT broker and InfluxDB)
- Grafana (can be installed via Docker)

### Installation

1. Clone and setup:
```bash
npm install
cp .env.example .env
# Edit .env with your configuration
```

2. Start Docker daemon (if not running):
```bash
sudo systemctl start docker
# Or on macOS: open Docker Desktop
```

3. Start infrastructure:
```bash
docker compose up -d
```

4. Verify services are running:
```bash
docker compose ps
```

5. Setup InfluxDB:
```bash
npm run setup
```

6. Start the simulator:
```bash
npm run simulator
```

7. Start the data collector (in another terminal):
```bash
npm run collector
```

### Alternative Setup (Without Docker)

If you prefer to install services locally:

1. **Install MQTT Broker (Mosquitto)**:
```bash
# Ubuntu/Debian
sudo apt-get install mosquitto mosquitto-clients

# macOS
brew install mosquitto
```

2. **Install InfluxDB**:
```bash
# Ubuntu/Debian
wget -qO- https://repos.influxdata.com/influxdb.key | sudo apt-key add -
echo "deb https://repos.influxdata.com/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/influxdb.list
sudo apt-get update && sudo apt-get install influxdb2

# macOS
brew install influxdb
```

3. **Install Grafana**:
```bash
# Ubuntu/Debian
sudo apt-get install -y software-properties-common
sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
sudo apt-get update && sudo apt-get install grafana

# macOS
brew install grafana
```

## 📁 Project Structure

```
src/
├── devices/          # Device simulators
├── collectors/       # Data collection services
├── config/          # Configuration management
├── utils/           # Utility functions
└── index.js         # Main entry point
```

## 🔧 Configuration

Edit `.env` file to customize:
- MQTT broker settings
- InfluxDB connection
- Number of simulated devices
- Simulation intervals
- Failure simulation settings

## 📈 Grafana Dashboards

Import the provided dashboard templates from `grafana/` directory to visualize:
- Real-time sensor readings
- Device health monitoring
- System performance metrics
- Alert management

## 🚨 Alerts

Configure Grafana alerts for:
- Device offline detection
- Abnormal sensor readings
- High error rates
- Low battery warnings

## 🧪 Testing

The simulator includes realistic scenarios:
- Random device failures
- Battery drain simulation
- Network connectivity issues
- Sensor reading variations

## 📊 Sample Grafana Queries

Here are some useful InfluxDB Flux queries for your dashboards:

### Temperature Readings
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "temperature")
  |> filter(fn: (r) => r["_field"] == "temperature")
  |> aggregateWindow(every: v.windowPeriod, fn: mean, createEmpty: false)
```

### Device Battery Levels
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["_field"] == "battery_level")
  |> group(columns: ["device_id"])
  |> last()
```

### Motion Detection Events
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "motion")
  |> filter(fn: (r) => r["_field"] == "motion_detected")
  |> filter(fn: (r) => r["_value"] == true)
```

### Device Alerts
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "device_alert")
  |> group(columns: ["alert_type"])
  |> count()
```

## 📚 Learning Objectives

This project helps you learn:
- MQTT pub/sub patterns
- Time-series data handling
- InfluxDB queries and data modeling
- Grafana dashboard creation
- Real-time monitoring setup
- IoT device management concepts

## 🎯 Next Steps

1. **Extend Device Types**: Add more sensor types (pressure, CO2, etc.)
2. **Advanced Scenarios**: Implement device groups, firmware updates
3. **Machine Learning**: Add anomaly detection for sensor readings
4. **Mobile Dashboard**: Create mobile-friendly Grafana views
5. **Data Export**: Add CSV/JSON export functionality
6. **Historical Analysis**: Implement long-term trend analysis

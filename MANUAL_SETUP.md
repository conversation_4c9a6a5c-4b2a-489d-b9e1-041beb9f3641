# 🔧 Manual Grafana Integration Setup

Since Docker is having issues, here's how to set up Grafana integration manually:

## 📋 **Step-by-Step Manual Setup**

### **Step 1: Install Required Services**

#### **Install MQTT Broker (<PERSON>s<PERSON>tto)**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mosquitto mosquitto-clients

# macOS
brew install mosquitto

# Start the service
sudo systemctl start mosquitto
sudo systemctl enable mosquitto
```

#### **Install InfluxDB**
```bash
# Ubuntu/Debian
wget -qO- https://repos.influxdata.com/influxdb.key | sudo apt-key add -
echo "deb https://repos.influxdata.com/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/influxdb.list
sudo apt update
sudo apt install influxdb2

# macOS
brew install influxdb

# Start InfluxDB
sudo systemctl start influxdb
sudo systemctl enable influxdb
```

#### **Install Grafana**
```bash
# Ubuntu/Debian
sudo apt-get install -y software-properties-common
sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
sudo apt update
sudo apt install grafana

# macOS
brew install grafana

# Start Grafana
sudo systemctl start grafana-server
sudo systemctl enable grafana-server
```

### **Step 2: Configure InfluxDB**

1. **Access InfluxDB UI**: http://localhost:8086
2. **Initial Setup**:
   - Username: `admin`
   - Password: `password123`
   - Organization: `iot-org`
   - Bucket: `iot-sensors`
3. **Generate Token**: Go to Data → Tokens → Generate Token
4. **Copy the token** for later use

### **Step 3: Configure Environment**

Create your `.env` file:
```bash
cp .env.example .env
```

Edit `.env` with your settings:
```env
# MQTT Configuration
MQTT_BROKER_URL=mqtt://localhost:1883

# InfluxDB Configuration  
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-token-here
INFLUXDB_ORG=iot-org
INFLUXDB_BUCKET=iot-sensors

# Simulation Settings
DEVICE_COUNT=5
SIMULATION_INTERVAL=5000
```

### **Step 4: Test the System**

1. **Test Components**:
```bash
npm test
```

2. **Start Data Collector** (Terminal 1):
```bash
npm run collector
```

3. **Start Device Simulator** (Terminal 2):
```bash
npm run simulator
```

### **Step 5: Configure Grafana**

1. **Access Grafana**: http://localhost:3000
   - Username: `admin`
   - Password: `admin` (change on first login)

2. **Add InfluxDB Data Source**:
   - Go to Configuration → Data Sources
   - Click "Add data source"
   - Select "InfluxDB"
   - Configure:
     - **URL**: `http://localhost:8086`
     - **Database**: Leave empty (using Flux)
     - **User**: `admin`
     - **Password**: `password123`
     - **HTTP Method**: `GET`
   - Under "InfluxDB Details":
     - **Organization**: `iot-org`
     - **Token**: `your-token-here`
     - **Default Bucket**: `iot-sensors`
     - **Min time interval**: `5s`

3. **Test Connection**: Click "Save & Test"

## 📊 **Creating Your First Dashboard**

### **Step 1: Create New Dashboard**
1. Click "+" → Dashboard
2. Click "Add new panel"

### **Step 2: Temperature Panel**
1. **Query**:
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "temperature")
  |> filter(fn: (r) => r["_field"] == "temperature")
  |> aggregateWindow(every: v.windowPeriod, fn: mean, createEmpty: false)
```

2. **Panel Settings**:
   - **Title**: "Temperature Readings"
   - **Visualization**: Time series
   - **Unit**: Celsius (°C)
   - **Min**: 0, **Max**: 50

### **Step 3: Device Status Panel**
1. **Add another panel**
2. **Query**:
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["_field"] == "battery_level")
  |> group(columns: ["device_id"])
  |> last()
```

3. **Panel Settings**:
   - **Title**: "Device Battery Levels"
   - **Visualization**: Stat
   - **Unit**: Percent (0-100)
   - **Thresholds**: Green > 50, Yellow > 20, Red < 20

### **Step 4: Motion Detection Panel**
1. **Add another panel**
2. **Query**:
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "motion")
  |> filter(fn: (r) => r["_field"] == "motion_detected")
  |> filter(fn: (r) => r["_value"] == true)
  |> aggregateWindow(every: v.windowPeriod, fn: count, createEmpty: false)
```

3. **Panel Settings**:
   - **Title**: "Motion Events"
   - **Visualization**: Bar chart
   - **Unit**: Count

## 🚨 **Setting Up Alerts**

### **Step 1: Create Alert Rule**
1. Go to Alerting → Alert Rules
2. Click "New rule"

### **Step 2: Low Battery Alert**
1. **Query**:
```flux
from(bucket: "iot-sensors")
  |> range(start: -5m)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["_field"] == "battery_level")
  |> group(columns: ["device_id"])
  |> last()
```

2. **Condition**: `IS BELOW 20`
3. **Evaluation**: Every `1m` for `2m`
4. **Alert Details**:
   - **Name**: "Low Battery Alert"
   - **Message**: "Device {{ $labels.device_id }} battery is low: {{ $values.B.Value }}%"

### **Step 3: Device Offline Alert**
1. **Query**:
```flux
from(bucket: "iot-sensors")
  |> range(start: -10m)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> group(columns: ["device_id"])
  |> last()
  |> map(fn: (r) => ({ r with _value: if (uint(v: now()) - uint(v: r._time)) > uint(v: 5m) then 1 else 0 }))
```

2. **Condition**: `IS ABOVE 0`
3. **Alert Details**:
   - **Name**: "Device Offline"
   - **Message**: "Device {{ $labels.device_id }} has been offline for more than 5 minutes"

## 🎯 **Testing Your Setup**

### **Verify Data Flow**
1. **Check MQTT**: 
```bash
mosquitto_sub -h localhost -t "iot/sensors/+/+"
```

2. **Check InfluxDB**:
```bash
influx query 'from(bucket:"iot-sensors") |> range(start:-1h) |> limit(n:10)'
```

3. **Check Grafana**: Look for data in your panels

### **Troubleshooting**
- **No data in Grafana**: Check InfluxDB token and organization
- **MQTT not working**: Verify mosquitto is running: `sudo systemctl status mosquitto`
- **InfluxDB connection failed**: Check if InfluxDB is running: `sudo systemctl status influxdb`

## 🎉 **You're Done!**

Your manual Grafana integration is complete! You should now see:
- ✅ Real-time temperature data
- ✅ Device battery levels
- ✅ Motion detection events
- ✅ Alert notifications for low battery and offline devices

## 📈 **Next Steps**
1. **Customize dashboards** with more panels
2. **Set up notification channels** (email, Slack)
3. **Create dashboard variables** for filtering
4. **Add more sensor types** using the examples
5. **Implement advanced queries** for analytics

**Happy monitoring!** 📊✨

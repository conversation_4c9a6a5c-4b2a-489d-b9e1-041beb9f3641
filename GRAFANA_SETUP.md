# Grafana Setup Guide for IoT Device Monitoring

This guide will help you set up comprehensive monitoring dashboards and alerts for your IoT device simulator.

## 🚀 Quick Setup

1. **Access Grafana**: http://localhost:3000
   - Username: `admin`
   - Password: `admin123`

2. **Data Source**: InfluxDB should be automatically configured
   - URL: `http://influxdb:8086`
   - Organization: `iot-org`
   - Bucket: `iot-sensors`
   - Token: `my-super-secret-auth-token`

## 📊 Dashboard Creation

### 1. Device Overview Dashboard

Create panels for:
- **Active Devices Count**
- **Average Battery Level**
- **Message Rate**
- **Error Rate**

### 2. Sensor Readings Dashboard

#### Temperature Panel
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "temperature")
  |> filter(fn: (r) => r["_field"] == "temperature")
  |> aggregateWindow(every: v.windowPeriod, fn: mean, createEmpty: false)
```

#### Motion Detection Panel
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "motion")
  |> filter(fn: (r) => r["_field"] == "motion_detected")
  |> filter(fn: (r) => r["_value"] == true)
  |> aggregateWindow(every: v.windowPeriod, fn: count, createEmpty: false)
```

#### Light Levels Panel
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["sensor_type"] == "light")
  |> filter(fn: (r) => r["_field"] == "illuminance")
  |> aggregateWindow(every: v.windowPeriod, fn: mean, createEmpty: false)
```

### 3. Device Health Dashboard

#### Battery Levels
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> filter(fn: (r) => r["_field"] == "battery_level")
  |> group(columns: ["device_id", "device_type"])
  |> last()
```

#### Device Status
```flux
from(bucket: "iot-sensors")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "device_status")
  |> filter(fn: (r) => r["_field"] == "message_count")
  |> group(columns: ["device_id"])
  |> last()
```

## 🚨 Alert Configuration

### 1. Device Offline Alert
- **Condition**: No data received for > 5 minutes
- **Severity**: Warning
- **Query**: Check last heartbeat timestamp

### 2. Low Battery Alert
- **Condition**: Battery level < 20%
- **Severity**: Warning
- **Query**: Current battery level

### 3. Critical Battery Alert
- **Condition**: Battery level < 5%
- **Severity**: Critical
- **Query**: Current battery level

### 4. Temperature Alerts
- **High Temperature**: > 35°C for 2 minutes
- **Temperature Spike**: Increase > 10°C in 5 minutes

### 5. System Health Alerts
- **High Error Rate**: Error rate > 10% for 2 minutes
- **No Motion**: No motion detected for > 1 hour (info level)

## 📱 Notification Channels

### Slack Integration
1. Go to Alerting → Notification channels
2. Add new channel: Slack
3. Configure webhook URL
4. Test notification

### Email Notifications
1. Configure SMTP settings in grafana.ini
2. Add email notification channel
3. Set up alert rules to use email channel

### Webhook Notifications
1. Create webhook endpoint
2. Configure webhook notification channel
3. Send JSON payload with alert details

## 🎨 Dashboard Best Practices

### Panel Organization
- Group related metrics together
- Use consistent time ranges
- Add meaningful titles and descriptions
- Use appropriate visualization types

### Color Coding
- Green: Normal/Good values
- Yellow: Warning thresholds
- Red: Critical/Error states
- Blue: Informational data

### Variables and Templating
Create dashboard variables for:
- Device Type filter
- Location filter
- Time range selector
- Device ID selector

### Example Variable Queries
```flux
// Device Types
from(bucket: "iot-sensors")
  |> range(start: -24h)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> keep(columns: ["device_type"])
  |> group()
  |> distinct(column: "device_type")

// Locations
from(bucket: "iot-sensors")
  |> range(start: -24h)
  |> filter(fn: (r) => r["_measurement"] == "sensor_reading")
  |> keep(columns: ["location"])
  |> group()
  |> distinct(column: "location")
```

## 🔧 Advanced Features

### Annotations
Add annotations for:
- Device maintenance events
- System deployments
- Configuration changes

### Dashboard Links
Create navigation between:
- Overview → Detailed device dashboards
- Alert dashboard → Device-specific views
- System health → Individual sensor dashboards

### Export/Import
- Export dashboards as JSON
- Version control dashboard configurations
- Share dashboards with team members

## 📈 Performance Tips

1. **Query Optimization**
   - Use appropriate time ranges
   - Limit data points with aggregateWindow
   - Use filters early in the query

2. **Refresh Rates**
   - Real-time panels: 5-10 seconds
   - Historical data: 1-5 minutes
   - Summary dashboards: 15-30 seconds

3. **Data Retention**
   - Configure InfluxDB retention policies
   - Archive old data appropriately
   - Monitor storage usage

## 🎯 Learning Exercises

1. **Create Custom Panels**
   - Device uptime percentage
   - Sensor accuracy metrics
   - Geographic distribution of devices

2. **Advanced Queries**
   - Calculate moving averages
   - Detect anomalies in sensor data
   - Correlate multiple sensor readings

3. **Alert Tuning**
   - Adjust thresholds based on historical data
   - Implement alert escalation
   - Create alert dependencies

4. **Dashboard Automation**
   - Provision dashboards via API
   - Automate alert rule creation
   - Implement dashboard as code

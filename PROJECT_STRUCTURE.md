# Project Structure

## 📁 Directory Layout

```
iot-device-simulator/
├── 📄 README.md                    # Main project documentation
├── 📄 GRAFANA_SETUP.md            # Grafana configuration guide
├── 📄 TROUBLESHOOTING.md          # Common issues and solutions
├── 📄 PROJECT_STRUCTURE.md        # This file
├── 📄 package.json                # Node.js dependencies and scripts
├── 📄 docker-compose.yml          # Infrastructure services
├── 📄 .env.example                # Environment configuration template
├── 📄 .env                        # Your environment configuration
├── 📄 quick-start.sh              # Quick setup script
├── 📄 test-simulator.js           # Component testing script
│
├── 📂 src/                        # Application source code
│   ├── 📄 index.js                # Main application entry point
│   ├── 📄 simulator.js            # Device simulator entry point
│   ├── 📄 collector.js            # Data collector entry point
│   ├── 📄 setup.js                # Database setup script
│   ├── 📄 DeviceManager.js        # Device fleet management
│   │
│   ├── 📂 devices/                # Device implementations
│   │   ├── 📄 BaseDevice.js       # Base device class
│   │   ├── 📄 TemperatureSensor.js # Temperature sensor
│   │   ├── 📄 MotionSensor.js     # Motion detector
│   │   └── 📄 LightSensor.js      # Light sensor
│   │
│   ├── 📂 collectors/             # Data collection services
│   │   └── 📄 DataCollector.js    # MQTT to InfluxDB collector
│   │
│   ├── 📂 config/                 # Configuration management
│   │   └── 📄 index.js            # Configuration loader
│   │
│   └── 📂 utils/                  # Utility functions
│       └── 📄 logger.js           # Logging utility
│
├── 📂 config/                     # External service configurations
│   └── 📄 mosquitto.conf          # MQTT broker configuration
│
├── 📂 grafana/                    # Grafana configurations
│   ├── 📂 provisioning/           # Auto-provisioning configs
│   │   ├── 📂 datasources/        # Data source definitions
│   │   │   └── 📄 influxdb.yml    # InfluxDB data source
│   │   └── 📂 dashboards/         # Dashboard provisioning
│   │       └── 📄 dashboard.yml   # Dashboard provider config
│   ├── 📂 dashboards/             # Dashboard JSON files
│   │   └── 📄 iot-overview.json   # Main IoT dashboard
│   └── 📂 alerts/                 # Alert rule definitions
│       └── 📄 device-alerts.yml   # Device monitoring alerts
│
└── 📂 node_modules/               # Node.js dependencies (auto-generated)
```

## 🏗️ Architecture Overview

### Data Flow
```
IoT Devices → MQTT Broker → Data Collector → InfluxDB → Grafana
     ↓              ↓              ↓           ↓         ↓
  Simulated    Eclipse       Node.js      Time-series  Dashboards
   Sensors    Mosquitto      Service       Database    & Alerts
```

### Component Responsibilities

#### 🔧 **Device Simulator** (`src/devices/`)
- **BaseDevice.js**: Common device functionality
  - MQTT connection management
  - Battery simulation
  - Heartbeat and status reporting
  - Failure simulation and recovery
  
- **Sensor Classes**: Specific sensor implementations
  - Realistic data generation
  - Location-based variations
  - Time-based patterns (daily/seasonal cycles)
  - Quality degradation simulation

#### 📊 **Data Collector** (`src/collectors/`)
- **DataCollector.js**: MQTT to InfluxDB bridge
  - Subscribe to all device topics
  - Parse and validate sensor data
  - Write structured data to InfluxDB
  - Error handling and reconnection logic

#### 🎛️ **Device Manager** (`src/DeviceManager.js`)
- Fleet management and orchestration
- Device lifecycle management
- Statistics collection and reporting
- Interactive command interface

#### ⚙️ **Configuration** (`src/config/`)
- Environment variable management
- Service connection parameters
- Simulation settings
- Logging configuration

## 🔌 Integration Points

### MQTT Topics Structure
```
iot/
├── sensors/
│   ├── temperature/{deviceId}    # Temperature readings
│   ├── motion/{deviceId}         # Motion detection events
│   └── light/{deviceId}          # Light level measurements
├── status/
│   └── {deviceType}/{deviceId}   # Device health status
└── alerts/
    └── {deviceType}/{deviceId}   # Device alerts and failures
```

### InfluxDB Schema
```
Measurements:
├── sensor_reading               # All sensor data
│   ├── Tags: device_id, device_type, sensor_type, location
│   └── Fields: temperature, humidity, motion_detected, illuminance, battery_level
├── device_status               # Device health data
│   ├── Tags: device_id, device_type, status
│   └── Fields: battery_level, message_count, error_count, uptime
└── device_alert               # Alert events
    ├── Tags: device_id, device_type, alert_type, reason
    └── Fields: battery_level
```

### Grafana Dashboard Structure
```
Dashboards:
├── IoT Overview               # High-level fleet status
├── Sensor Readings           # Detailed sensor data
├── Device Health             # Battery, connectivity, errors
├── System Performance        # Message rates, error rates
└── Alerts & Incidents       # Alert history and status
```

## 🚀 Deployment Options

### Development (Local)
- Docker Compose for infrastructure
- Node.js applications run locally
- Hot reload for development

### Production (Docker)
- All services containerized
- Persistent volumes for data
- Environment-specific configurations
- Health checks and restart policies

### Cloud Deployment
- Kubernetes manifests
- Managed services (AWS IoT, Azure IoT Hub)
- Scalable infrastructure
- Monitoring and logging

## 🔄 Data Lifecycle

### 1. **Generation** (Device Simulators)
- Realistic sensor readings
- Time-based patterns
- Random variations and noise
- Failure scenarios

### 2. **Transport** (MQTT)
- Publish/Subscribe messaging
- Topic-based routing
- Quality of Service levels
- Connection management

### 3. **Collection** (Data Collector)
- Message parsing and validation
- Data transformation
- Batch writing to database
- Error handling

### 4. **Storage** (InfluxDB)
- Time-series optimization
- Retention policies
- Compression
- Query performance

### 5. **Visualization** (Grafana)
- Real-time dashboards
- Historical analysis
- Alert management
- User interaction

## 🎯 Extension Points

### Adding New Sensor Types
1. Create new class extending `BaseDevice`
2. Implement `generateReading()` method
3. Add to `DeviceManager` device types
4. Update data collector parsing
5. Create Grafana dashboard panels

### Custom Data Processing
1. Add processing logic in `DataCollector`
2. Create derived measurements
3. Implement data aggregation
4. Add custom alert rules

### Integration with External Systems
1. Add webhook notifications
2. Implement REST API endpoints
3. Create data export functions
4. Add external data sources

## 📚 Learning Path

### Beginner
1. Run the basic simulator
2. Explore Grafana dashboards
3. Modify sensor parameters
4. Create simple alerts

### Intermediate
1. Add new sensor types
2. Customize dashboard layouts
3. Implement complex queries
4. Set up notification channels

### Advanced
1. Optimize performance
2. Implement custom processing
3. Add machine learning features
4. Deploy to production environment

## 🔍 Monitoring and Observability

### Application Metrics
- Device connection status
- Message processing rates
- Error rates and types
- Resource utilization

### Infrastructure Metrics
- MQTT broker performance
- InfluxDB query performance
- Grafana dashboard load times
- Docker container health

### Business Metrics
- Sensor reading accuracy
- Device uptime percentages
- Alert response times
- Data quality scores
